﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ImportCTF_SectionPAiring]
@DTbegin datetime,  
@DTEnd datetime,
@TYPECREW integer = 0
AS
BEGIN

----Declare 
----@DTbegin datetime='2018-07-01 00:00:00',  
----@DTEnd datetime='2018-07-31 23:59:00',
----@TYPECREW integer = 0

--====================================Таблицы Выход.==============================
Declare @REZCRW Table
(NN integer null,
 STR1 nvarchar(300) null)

--====================================Таблицы Внутр.===============================
Declare @TBTSK Table
(
	FlightTaskReestrID integer null,
	Ap_PlanFlightAirPortCrewID integer null,
	Bap integer
)

Declare @TVLEG table
(
FlightTaskReestrID integer,
Ap_PlanFlightAirPortCrewID integer,
AP_BASE_ID integer,
ff integer,
DateTakeoff Datetime,
F_CodeIATA nvarchar(5),
F_Ap_AirPortID integer,
F_Name nvarchar(5),
DateLanding Datetime,
L_Ap_AirPortID integer,
L_CodeIATA nvarchar(5),
L_Name nvarchar(5)
)

DEclare @TVLEGS Table
(
DateTakeoff datetime,
AP_TO nvarchar(5),
FlightName nvarchar(10),
DateLanding Datetime,
AP_LA nvarchar(5),
Pr_FlightTaskReestrID integer,
fl integer
)

------------------------------------------------------------------------
---------------------------------------------------------------------------
DECLARE @PERIOD NVARCHAR(100)  ='PERIOD:'
DECLARE @PLAN_TYPE NVARCHAR(100)='PLAN TYPE: DATED' 
DECLARE @TIME_MODE NVARCHAR(100)='TIME MODE: UTC' 
---------------------------------------------------------------------------
DECLARE @SECTION_CREW NVARCHAR(100)  ='SECTION: CREW'
DECLARE @CREW NVARCHAR(100)='CREW:' 
DECLARE @EOCREW NVARCHAR(100)='EOCREW'
----------------------------------------------------------------------------
DECLARE @EOSECTION NVARCHAR(100)='EOSECTION'
---------------------------------------------
DECLARE @SECTION_PAIRING NVARCHAR(100)  ='SECTION: PAIRING'
DECLARE @PAIRING  NVARCHAR(100)='PAIRING:' 
DECLARE @EOPAIRING NVARCHAR(100)='EOPAIRING' 
----------------------------------------------------- 
DECLARE @SECTION_LEG NVARCHAR(100)  ='SECTION: LEG'
----------------------------------------------------- 
DECLARE @SECTION_GROUND_DUTY NVARCHAR(100)  ='SECTION: GROUND DUTY'
----------------------------------------------------- 
DECLARE @DS nvarchar(1)=' '
DECLARE @STRFlights NVARCHAR(100)=''
DECLARE @CorrierCode nvarchar(2)='DP'
----------------------------------------------------
DECLARE @Duty_code NVARCHAR(10)='*'
DECLARE @Lock_code NVARCHAR(10)='L' 
DECLARE @Environment_code NVARCHAR(10)='1' 
DECLARE @Activity_code NVARCHAR(10)='LV ' 
DECLARE @Activity_attribute NVARCHAR(10)='*' 
DECLARE @Activity_type NVARCHAR(1)='F' 
DECLARE @ActivitySub_type NVARCHAR(1)='L'
DECLARE @Horizontal_lock1 NVARCHAR(1)='X'
DECLARE @Horizontal_lock2 NVARCHAR(1)='N'
DECLARE @Horizontal_lock3 NVARCHAR(1)='L'
DECLARE @FLIGHTCREW nvarchar(15)='1/1/0/0/0/0/0 '
DECLARE @CABINCREW nvarchar(15)='0/0/0/1/0/3/0 '


Declare
@DT_CRbegin datetime=dateadd(dd,-6, @DTbegin),  
@DT_CREnd datetime=dateadd(dd,+12, @DTEnd),
@CREWSTR nvarchar(15)=''
 
If @TYPECREW=0  Set @CREWSTR=@FLIGHTCREW
ELSE Set @CREWSTR=@CABINCREW

-----------------------------------------
--Select @DT_CRbegin, @DT_CREnd 
---------------------------------------------

INSERT INTO @TVLEGS
Select DISTINCT 
PFTO.DateTakeoff,
AP_TO.CodeIATA AP_TO,
FL.Name,
PFLA.DateLanding,
AP_LA.CodeIATA AP_LA,
TSK.Pr_FlightTaskReestrID,
0
From Pr_FlightTaskReestrs TSK 
INNER JOIN  Ap_PlanFlightAirPorts PFTO ON PFTO.Pr_FlightTaskReestrID = TSK.Pr_FlightTaskReestrID and PFTO.AirPortNumber=1
INNER JOIN  Ap_PlanFlightAirPorts PFLA ON PFTO.Ap_PlanFlightID = PFLA.Ap_PlanFlightID and PFTO.AirPortNumber+1=PFLA.AirPortNumber
INNER JOIN  Ap_PlanFlights PFL ON PFTO.Ap_PlanFlightID=PFL.Ap_PlanFlightID
LEFT JOIN   Fl_Flights		FL ON PFL.Fl_FlightID=FL.Fl_FlightID
LEFT JOIN   Ap_Airports AP_TO  ON PFTO.Ap_AirportID = AP_TO.Ap_AirportID
LEFT JOIN   Ap_Airports AP_LA  ON PFLA.Ap_AirportID = AP_LA.Ap_AirportID
Where 
convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
AND PFL.OnlyFinanceCalculation = 0
AND PFL.Sh_ScheduleVariantTypeID IS NULL
and PFL.Status & 256 <> 256  ----отмененные рейсы
AND ISNULL(FL.FlightVariant,0) = 0 
AND PFL.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки

--========================================================================================
------Select	DISTINCT	
------		PFA.Pr_FlightTaskReestrID,
------		isnull(PR.Pr_PersonnelID,1)  Ap_PlanFlightAirPortCrewID,--isnull(PR.Pr_PersonnelID,1) Ap_PlanFlightAirPortCrewID,
------		PTR.Ap_AirPortID  Bap  --PTR.Ap_AirPortID
------		--PFC.*	
------	FROM Ap_PlanFlights PFL
------		INNER JOIN Ap_PlanFlightAirPorts PFA ON PFA.Ap_PlanFlightID = PFL.Ap_PlanFlightID and PFA.AirPortNumber=1
------		INNER JOIN Fl_Flights			   FL ON PFL.Fl_FlightID=FL.Fl_FlightID
------		--OUTER APPLY (Select top 1 * From Ap_PlanFlightAirPortCrews TT where TT.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID and TT.OrderNumber=1  and tt.CrewType=0 Order by TT.CrewType) PFC
------		LEFT JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
------		LEFT JOIN Pr_Personnels PR on  PFC.Pr_PersonnelID=PR.Pr_PersonnelID
------		LEFT JOIN Pr_PersPosts Post ON PR.Pr_PersonnelID = Post.Pr_PersonnelID AND (Post.DateBegin < getutcdate() and (Post.DateEnd is null or Post.DateEnd > getutcdate()))
------		LEFT JOIN Pr_StaffTrees TR ON Post.Pr_StaffTreeID = TR.Pr_StaffTreeID  
------		LEFT  JOIN Pr_StaffTrees PTR ON PTR.Pr_StaffTreeID = TR.Pr_StaffTreeIDParent 
------		--LEFT JOIN Pr_ArmChairTypes ON PFC.Pr_ArmChairTypeID = Pr_ArmChairTypes.Pr_ArmChairTypeID 
------		--LEFT JOIN Pr_ArmChairRoles ON Pr_ArmChairRoles.Pr_ArmChairRoleID = PFC.Pr_ArmChairRoleID 

------	WHERE 
------		convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
------		AND PFL.OnlyFinanceCalculation = 0
------		AND PFL.Sh_ScheduleVariantTypeID IS NULL
------		and PFL.Status & 256 <> 256  ----отмененные рейсы
------		AND ISNULL(FL.FlightVariant,0) = 0 
------		AND PFL.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки
------		----
------		and (PFC.OrderNumber=1)
------		and (PFC.CrewType=@TYPECREW)
------    GROUP BY  PFA.Pr_FlightTaskReestrID,  isnull(PR.Pr_PersonnelID,1)  , PTR.Ap_AirPortID --, --, PTR.Ap_AirPortID   ,isnull(PR.Pr_PersonnelID,1)
--=================================================================================================================================================

--=======================Полетные задания с обезличинным капитаном================================================
--==================Проблема по обезличинным капитанам нет привязки к BaseHome
--================================================================================================================
Insert INTO @TBTSK
Select	DISTINCT	
		PFA.Pr_FlightTaskReestrID,
		MAX(isnull(PR.Pr_PersonnelID,1))  Ap_PlanFlightAirPortCrewID,--isnull(PR.Pr_PersonnelID,1) Ap_PlanFlightAirPortCrewID,
		MAX(PTR.Ap_AirPortID) Bap  --PTR.Ap_AirPortID
		--PFC.*	
	FROM Ap_PlanFlights PFL
		INNER JOIN Ap_PlanFlightAirPorts PFA ON PFA.Ap_PlanFlightID = PFL.Ap_PlanFlightID and PFA.AirPortNumber=1
		INNER JOIN Fl_Flights			   FL ON PFL.Fl_FlightID=FL.Fl_FlightID
		--OUTER APPLY (Select top 1 * From Ap_PlanFlightAirPortCrews TT where TT.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID and TT.OrderNumber=1  and tt.CrewType=0 Order by TT.CrewType) PFC
		LEFT JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
		LEFT JOIN Pr_Personnels PR on  PFC.Pr_PersonnelID=PR.Pr_PersonnelID
		LEFT JOIN Pr_PersPosts Post ON PR.Pr_PersonnelID = Post.Pr_PersonnelID AND (Post.DateBegin < getutcdate() and (Post.DateEnd is null or Post.DateEnd > getutcdate()))
		LEFT JOIN Pr_StaffTrees TR ON Post.Pr_StaffTreeID = TR.Pr_StaffTreeID  
		LEFT  JOIN Pr_StaffTrees PTR ON PTR.Pr_StaffTreeID = TR.Pr_StaffTreeIDParent 
		--LEFT JOIN Pr_ArmChairTypes ON PFC.Pr_ArmChairTypeID = Pr_ArmChairTypes.Pr_ArmChairTypeID 
		--LEFT JOIN Pr_ArmChairRoles ON Pr_ArmChairRoles.Pr_ArmChairRoleID = PFC.Pr_ArmChairRoleID 

	WHERE 
		convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
		AND PFL.OnlyFinanceCalculation = 0
		AND PFL.Sh_ScheduleVariantTypeID IS NULL
		and PFL.Status & 256 <> 256  ----отмененные рейсы
		AND ISNULL(FL.FlightVariant,0) = 0 
		AND PFL.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки
		----
		and (PFC.OrderNumber=1)
		and (PFC.CrewType=@TYPECREW)
    GROUP BY  PFA.Pr_FlightTaskReestrID --,  isnull(PR.Pr_PersonnelID,1)  , PTR.Ap_AirPortID --, --, PTR.Ap_AirPortID   ,isnull(PR.Pr_PersonnelID,1)
----================================================================================================================
---select * From @TBTSK
--=====================Формирование Данных по первому и последнему рейса в  Полетном задании========================
--==================================================================================================================
INSERT INTO @TVLEG
Select 
T.FlightTaskReestrID,
T.Ap_PlanFlightAirPortCrewID,
ISNULL(T.Bap,58),
Case ISNULL(T.Bap,58)
-----------------------Определение флагов включения рейса в один TRIP ------------------------------------
WHEN 58 then 
		Case
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0 --BB вылет прилет из базы в базу
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1 --BN
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2 --NN
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3 --NB
		End
WHEN 86 then
		Case
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3
		End
ELSE 
		Case
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3
		End
END ff,
----------------------------------------------------------------------------------------------
FR.DateTakeoff,
FR.CodeIATA,
FR.Ap_AirPortID,
FR.Name,
LR.DateLanding,
LR.Ap_AirPortID,
LR.CodeIATA,
LR.Name
From @TBTSK T
OUTER APPLY (select top 1
			 PFA.DateTakeoff,
			 FL.NAME,
			 PRT.Ap_AirPortID,
			 PRT.CodeIATA	
			 From Ap_PlanFlightAirPorts PFA
			 INNER JOIN Ap_PlanFlights PFL ON PFL.Ap_PlanFlightID=PFA.Ap_PlanFlightID
			 INNER JOIN Fl_Flights     FL  ON PFL.Fl_FlightID=FL.Fl_FlightID
			 INNER JOIN Ap_AirPorts    PRT  ON PRT.Ap_AirPortID=PFA.Ap_AirPortID
			 WHERE 
			 PFA.AirPortNumber=1
			 and T.FlightTaskReestrID=PFA.Pr_FlightTaskReestrID
			 ORDER BY PFA.DateTakeoff ASC
			 ) FR
OUTER APPLY (select top 1
			 PFA1.DateLanding,
			 FL.NAME,
			 PRT.Ap_AirPortID,
			 PRT.CodeIATA	
			 From Ap_PlanFlightAirPorts PFA
			 INNER JOIN Ap_PlanFlightAirPorts PFA1 ON PFA.Ap_PlanFlightID=PFA1.Ap_PlanFlightID and PFA.AirPortNumber+1=PFA1.AirPortNumber
			 INNER JOIN Ap_PlanFlights PFL ON PFL.Ap_PlanFlightID=PFA.Ap_PlanFlightID
			 INNER JOIN Fl_Flights     FL  ON PFL.Fl_FlightID=FL.Fl_FlightID
			 INNER JOIN Ap_AirPorts    PRT  ON PRT.Ap_AirPortID=PFA1.Ap_AirPortID
			 WHERE 
			 PFA.AirPortNumber=1
			 and T.FlightTaskReestrID=PFA.Pr_FlightTaskReestrID
			 ORDER BY PFA1.DateLanding DESC
			 ) LR
Where FlightTaskReestrID is not null
ORDER BY 
FR.DateTakeoff,
T.Ap_PlanFlightAirPortCrewID,
FR.CodeIATA
--==================================================================================================================

---Select * From @TVLEG Where Ap_PlanFlightAirPortCrewID=1582

--=============================== Начало Курсора 1===============
DECLARE @CURFOOD CURSOR
DECLARE @CURLEG CURSOR
DECLARE 
@NN integer=0,
@STR NVARCHAR(max)='',
@STR1 NVARCHAR(max)=''

DECLARE @BeginParing integer=0
DECLARE 
@FlightTaskReestrID integer,
@PlanFlightAirPortCrewID integer,
@AP_BASE_ID integer,
@ff integer,
@DateTakeoff Datetime,
@F_CodeIATA nvarchar(5),
@F_Ap_AirPortID integer,
@F_Name nvarchar(5),
@DateLanding Datetime,
@L_Ap_AirPortID integer,
@L_CodeIATA nvarchar(5),
@L_Name nvarchar(5),
@Beg nvarchar(3),
@Bend nvarchar(3),
@Define integer,
-------------------
@Nrow integer,
@Nleg integer,
@NrowBegin integer,
@TO_DateTakeoff Datetime,
@TO_CodeIATA nvarchar(5),
@LegName nvarchar(5),
@LA_DateLanding DateTime,
@LA_CodeIATA  nvarchar(5)
--=========================================
---Select * From @TVLEG
--=========================================
----Select
------TB.Beg,
------TB.Bend,
------TB.Define,
----TB.FlightTaskReestrID ,
----COUNT(*),
----MAX(TB.Ap_PlanFlightAirPortCrewID),
----MIN(TB.Ap_PlanFlightAirPortCrewID),
----MIN(TB.AP_BASE_ID),
----MAX(TB.AP_BASE_ID)
------TB.DateTakeoff ,
------TB.F_CodeIATA ,
------TB.F_Ap_AirPortID ,
------TB.F_Name ,
------TB.DateLanding ,
------TB.L_Ap_AirPortID ,
------TB.L_CodeIATA ,
------TB.L_Name  
----From 	(Select 
----		FlightTaskReestrID ,
----		Ap_PlanFlightAirPortCrewID ,
----		ROW_NUMBER() OVER(PARTITION BY Ap_PlanFlightAirPortCrewID Order by FlightTaskReestrID) Define,
----		AP_BASE_ID ,
----		isnull(H.HFF,-1) HFF,
----		ff ,
----		isnull(L.HLL,-1) HLL,
----		Case
----		When ff=0 then 'Beg'
----		When ff=1 then 'Beg'
----		When hff in (1,2) and ff=2  then ''
----		When hff not in (1,2) and ff=2 then 'Beg'
----		When hff not in (1,2) and ff=3 then 'Beg'
----		end Beg,
----		Case
----		When ff=0 then 'End'
----		When ff=1 and hll not in (2,3) then 'End'
----		When ff=2 and hll in (2,3) then ''
----		When ff=2 and hll not in (2,3) then 'End'
----		When ff=3 then 'End'
----		end Bend,
----		DateTakeoff ,
----		F_CodeIATA ,
----		F_Ap_AirPortID ,
----		F_Name ,
----		DateLanding ,
----		L_Ap_AirPortID ,
----		L_CodeIATA ,
----		L_Name  
----			From @TVLEG T
----			OUTER APPLY (Select top 1 
----							FF HFF 
----							From @TVLEG F 
----							Where 
----							F.DateTakeoff<T.DateTakeoff 
----							and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
----							Order by F.DateTakeoff Desc 
----						) H
----			OUTER APPLY (Select top 1 
----							FF HLL 
----							From @TVLEG F 
----							Where 
----							F.DateTakeoff>T.DateTakeoff 
----							and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
----							Order by F.DateTakeoff ASC 
----						) L
----			WHere --F_Name='111'
----			((
----				--@Frg=0
----				--and 
----				Case
----				When ff=0 or ff=1 then T.DateTakeoff
----				When Ff=2 or ff=3 then (Select top 1 
----										F.DateTakeoff 
----										From @TVLEG F 
----										Where 
----										F.DateTakeoff<T.DateTakeoff 
----										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
----										and F.ff=1 
----										Order by F.DateTakeoff Desc 
----										) 
----				----------------------------------------------
----				End between @DT_CRbegin and @DTbegin
----				----------------------------------------------
----				and 
----				Case
----				When ff=0 or ff=3 then T.DateLanding
----				When Ff=2 or ff=1 then (Select top 1 
----										F.DateTakeoff 
----										From @TVLEG F 
----										Where 
----										F.DateTakeoff>T.DateTakeoff 
----										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
----										and F.ff=3 
----										Order by F.DateTakeoff ASC 
----										) 
----				End  >= @DTbegin
----				and t.FlightTaskReestrID is not null
----			)
----				or
----			(
----				--@Frg=1
----				--and 
----				Case
----				When ff=0 or ff=1 then T.DateTakeoff
----				When Ff=2 or ff=3 then (Select top 1 
----										F.DateTakeoff 
----										From @TVLEG F 
----										Where 
----										F.DateTakeoff<T.DateTakeoff 
----										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
----										and F.ff=1 
----										Order by F.DateTakeoff Desc 
----										) 
----				End between @DTbegin and @DTEnd
----				and 
----				Case
----				When ff=0 or ff=3 then T.DateLanding
----				When Ff=2 or ff=1 then (Select top 1 
----										F.DateTakeoff 
----										From @TVLEG F 
----										Where 
----										F.DateTakeoff>T.DateTakeoff 
----										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
----										and F.ff=3 
----										Order by F.DateTakeoff ASC 
----										) 
----				End  between @DTbegin and @DTEnd
----				and t.FlightTaskReestrID is not null
----				)
----					or
----				(
----				Case
----				When ff=0 or ff=1 then T.DateTakeoff
----				When Ff=2 or ff=3 then (Select top 1 
----										F.DateTakeoff 
----										From @TVLEG F 
----										Where 
----										F.DateTakeoff<T.DateTakeoff 
----										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
----										and F.ff=1 
----										Order by F.DateTakeoff Desc 
----										) 
----				End between @DTbegin and @DTEnd
----				and 
----				Case
----				When ff=0 or ff=3 then T.DateLanding
----				When Ff=2 or ff=1 then (Select top 1 
----										F.DateTakeoff 
----										From @TVLEG F 
----										Where 
----										F.DateTakeoff>T.DateTakeoff 
----										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
----										and F.ff=3 
----										Order by F.DateTakeoff ASC 
----										) 
----				End  between @DTEnd and @DT_CREnd
----				and t.FlightTaskReestrID is not null
----				)
----				) --and F_Name='502'--and ff=3
----			) TB
----			--Where Ap_PlanFlightAirPortCrewID=1582
----			GROUP  BY
----			FlightTaskReestrID
----			--TB.Ap_PlanFlightAirPortCrewID,
----						--TB.DateTakeoff
----			HAVING Count(*)>=2
--===========================================
----================================Заголовок CTF===========================
----Set @NN=@NN+1
----INSERT INTO @REZCRW 
----VALUES (@NN, @PERIOD+' '+Convert(nvarchar(10),dateadd(dd,-6,@DTbegin),112)+' - '+Convert(nvarchar(10),dateadd(dd,12,@DTEnd),112))
----Set @NN=@NN+1
----INSERT INTO @REZCRW 
----VALUES (@NN, @PLAN_TYPE)
----Set @NN=@NN+1
----INSERT INTO @REZCRW 
----VALUES (@NN, @TIME_MODE)
----Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @SECTION_PAIRING)
--======================================Данные =====================
SET @CURFOOD  = CURSOR SCROLL
FOR 
---------
Select
TB.Beg,
TB.Bend,
TB.Define,
TB.FlightTaskReestrID ,
TB.Ap_PlanFlightAirPortCrewID ,
TB.AP_BASE_ID,
TB.DateTakeoff ,
TB.F_CodeIATA ,
TB.F_Ap_AirPortID ,
TB.F_Name ,
TB.DateLanding ,
TB.L_Ap_AirPortID ,
TB.L_CodeIATA ,
TB.L_Name  
From 	(Select 
		FlightTaskReestrID ,
		Ap_PlanFlightAirPortCrewID ,
		ROW_NUMBER() OVER(PARTITION BY Ap_PlanFlightAirPortCrewID Order by FlightTaskReestrID) Define,
		AP_BASE_ID ,
		isnull(H.HFF,-1) HFF,
		ff ,
		isnull(L.HLL,-1) HLL,
		Case
		When ff=0 then 'Beg'
		When ff=1 then 'Beg'
		When hff in (1,2) and ff=2  then ''
		When hff not in (1,2) and ff=2 then 'Beg'
		When hff not in (1,2) and ff=3 then 'Beg'
		end Beg,
		Case
		When ff=0 then 'End'
		When ff=1 and hll not in (2,3) then 'End'
		When ff=2 and hll in (2,3) then ''
		When ff=2 and hll not in (2,3) then 'End'
		When ff=3 then 'End'
		end Bend,
		DateTakeoff ,
		F_CodeIATA ,
		F_Ap_AirPortID ,
		F_Name ,
		DateLanding ,
		L_Ap_AirPortID ,
		L_CodeIATA ,
		L_Name  
			From @TVLEG T
			OUTER APPLY (Select top 1 
							FF HFF 
							From @TVLEG F 
							Where 
							F.DateTakeoff<T.DateTakeoff 
							and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
							Order by F.DateTakeoff Desc 
						) H
			OUTER APPLY (Select top 1 
							FF HLL 
							From @TVLEG F 
							Where 
							F.DateTakeoff>T.DateTakeoff 
							and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
							Order by F.DateTakeoff ASC 
						) L
			WHere
			((
				--@Frg=0
				--and 
				Case
				When ff=0 or ff=1 then T.DateTakeoff
				When Ff=2 or ff=3 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff<T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=1 
										Order by F.DateTakeoff Desc 
										) 
				----------------------------------------------
				End between @DT_CRbegin and @DTbegin
				----------------------------------------------
				and 
				Case
				When ff=0 or ff=3 then T.DateLanding
				When Ff=2 or ff=1 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff>T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=3 
										Order by F.DateTakeoff ASC 
										) 
				End  >= @DTbegin
				and t.FlightTaskReestrID is not null
			)
				or
			(
				--@Frg=1
				--and 
				Case
				When ff=0 or ff=1 then T.DateTakeoff
				When Ff=2 or ff=3 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff<T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=1 
										Order by F.DateTakeoff Desc 
										) 
				End between @DTbegin and @DTEnd
				and 
				Case
				When ff=0 or ff=3 then T.DateLanding
				When Ff=2 or ff=1 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff>T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=3 
										Order by F.DateTakeoff ASC 
										) 
				End  between @DTbegin and @DTEnd
				and t.FlightTaskReestrID is not null
				)
					or
				(
				Case
				When ff=0 or ff=1 then T.DateTakeoff
				When Ff=2 or ff=3 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff<T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=1 
										Order by F.DateTakeoff Desc 
										) 
				End between @DTbegin and @DTEnd
				and 
				Case
				When ff=0 or ff=3 then T.DateLanding
				When Ff=2 or ff=1 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff>T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=3 
										Order by F.DateTakeoff ASC 
										) 
				End  between @DTEnd and @DT_CREnd
				and t.FlightTaskReestrID is not null
				)
				) --and ff=3
			) TB
			ORDER BY
			TB.Ap_PlanFlightAirPortCrewID,
			TB.DateTakeoff
			
-----------
SET @BeginParing=1
SET @NrowBegin=0
OPEN @CURFOOD
FETCH NEXT FROM @CURFOOD INTO 
	@Beg ,
	@Bend ,
	@Define ,
	@FlightTaskReestrID ,
	@PlanFlightAirPortCrewID ,
	@AP_BASE_ID ,
	@DateTakeoff ,
	@F_CodeIATA ,
	@F_Ap_AirPortID ,
	@F_Name ,
	@DateLanding ,
	@L_Ap_AirPortID ,
	@L_CodeIATA ,
	@L_Name 

WHILE @@FETCH_STATUS = 0
  BEGIN
	SET @STR=''
	IF @Beg='Beg' 
	BEGIN
		SET @BeginParing=0
		SET @Nleg=0
		SET @NN=@NN+1
		SET @STR=@STR+@PAIRING+CONVERT(nvarchar(25),@FlightTaskReestrID)+' "'+CONVERT(nvarchar(25),@FlightTaskReestrID)+'" '+@CREWSTR+LTRIM(RTRIM(@F_CodeIATA))
		SET @NrowBegin=@NN
		INSERT INTO @REZCRW
		select @NN,@STR 
	END
	----IF (@Beg is null or @Beg='') and (@BeginParing=1)
	----BEGIN
	----	SET @BeginParing=0
	----	SET @Nleg=0
	----	SET @NN=@NN+1
	----	SET @STR=@STR+@PAIRING+CONVERT(nvarchar(25),@FlightTaskReestrID)+' "'+CONVERT(nvarchar(25),@FlightTaskReestrID)+'" '+@CREWSTR+LTRIM(RTRIM(@F_CodeIATA))
	----	SET @NrowBegin=@NN
	----	INSERT INTO @REZCRW
	----	select @NN,@STR 
	----END
		----------------------------------------------------
		-----------------------------------------------------
		SET @CURLEG  = CURSOR SCROLL
			FOR 			
			Select 
			DateTakeoff,
			AP_TO,
			FlightName,
			DateLanding,
			AP_LA
			From @TVLEGS
			Where Pr_FlightTaskReestrID=@FlightTaskReestrID
			Order by DateTakeoff

			OPEN @CURLEG
			FETCH NEXT FROM @CURLEG INTO
				@TO_DateTakeoff ,
				@TO_CodeIATA ,
				@LegName ,
				@LA_DateLanding,
				@LA_CodeIATA 
			WHILE @@FETCH_STATUS = 0
			BEGIN
			If  @BeginParing=0
			BEGIN
				SET @STR1=''
				SET @Nleg=@Nleg+1
				SET @NN=@NN+1
				SET @STR1=@Activity_type+@DS+@ActivitySub_type+@DS+
							Case
							when @Nleg=1 then @Horizontal_lock1
							when @DateLanding =@LA_DateLanding and  @Bend='End'  then @Horizontal_lock3
							else @Horizontal_lock2
							end +@DS+
							Convert(nvarchar(10),@TO_DateTakeoff,112)+@DS+@TO_CodeIATA +@DS+dbo.PD_fn_ShowDateAsHHMM(@TO_DateTakeoff )+@DS+
							@CorrierCode+@DS+convert(nvarchar(6),@LegName) +@DS+'*'+@DS+'1'+@DS+dbo.PD_fn_ShowDateAsHHMM(@LA_DateLanding)+@DS+@LA_CodeIATA+@DS+Convert(nvarchar(10),@LA_DateLanding,112)
							+ ' #duty'+ convert(nvarchar(6),@Define)
					BEGIN TRANSACTION
						INSERT INTO @REZCRW
						select @NN,@STR1 
					COMMIT TRANSACTION
					--------------------------
					BEGIN TRANSACTION
						UPDATE @TVLEGS 
						SET FL=1
						Where LTRIM(RTRIM(FlightName))=LTRIM(RTRIM(@LegName)) and convert(datetime, DateTakeoff)=convert(datetime,@TO_DateTakeoff)
						--Select * From @TVLEGS Where LTRIM(RTRIM(FlightName))='441' --and DateTakeoff=@TO_DateTakeoff
					COMMIT TRANSACTION
					-----------------------------------------------------
			END
			FETCH NEXT FROM @CURLEG INTO 
				@TO_DateTakeoff ,
				@TO_CodeIATA ,
				@LegName ,
				@LA_DateLanding,
				@LA_CodeIATA 
			END
		CLOSE @CURLEG
		DEALLOCATE @CURLEG 

    ---------------------------------------
	IF  @BeginParing=0 and  @Bend='End' 
	Begin
	---------------------------------------
		BEGIN TRANSACTION
			UPDATE @REZCRW 
			SET STR1=SUBSTRING(TT.STR1,charindex(@PAIRING,TT.STR1,1),LEN(@PAIRING)) + ' ' +  convert(nvarchar(5),@Nleg) + ' ' + SUBSTRING(TT.STR1,LEN(@PAIRING)+1,LEN(TT.STR1)-LEN(@PAIRING)+1)
			From @REZCRW TT where TT.NN=@NrowBegin 
		COMMIT TRANSACTION
		---------------------------------------
		BEGIN TRANSACTION
			SET  @BeginParing=1
			SET @NrowBegin=0
			SET @NN=@NN+1
			INSERT INTO @REZCRW
			select @NN,@EOPAIRING 
		COMMIT TRANSACTION
	End
	
  FETCH NEXT FROM @CURFOOD INTO 
	@Beg ,
	@Bend ,
	@Define ,
	@FlightTaskReestrID ,
	@PlanFlightAirPortCrewID ,
	@AP_BASE_ID ,
	@DateTakeoff ,
	@F_CodeIATA ,
	@F_Ap_AirPortID ,
	@F_Name ,
	@DateLanding ,
	@L_Ap_AirPortID ,
	@L_CodeIATA ,
	@L_Name 
  END
CLOSE @CURFOOD
DEALLOCATE @CURFOOD 

SET @NN=@NN+1
BEGIN TRANSACTION
	INSERT INTO @REZCRW
	select @NN,@EOSECTION 
COMMIT TRANSACTION

Select * from @REZCRW
--Select * from @TVLEGS where fl=0 and DateTakeoff between @DTbegin and @DTEnd  and FlightName not in ('001','002')
END