﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE PD_JPR_ImportEtab_TurnoverAviaBoard
@DTbegin datetime,
@DTend   datetime 

AS
BEGIN
Declare
@DT_begin datetime =dateadd(dd,-6,@DTbegin) ,
@DT_end   datetime =dateadd(dd,12,@DTend),  
@Handle nvarchar(MAX) = '
/*

This table stores information about aircraft rotations. It holds
information about a leg and its onward leg. It can be used when
building aircraft rotations in Studio (depending on the rules activated).
For more information, see Function Reference Manual, Basics, Rotation Rules.
*/
10
Sleg_carrier_code "Carrier code" ?"Carrier code of the current leg",
Ileg_flight_number "Flight number" ?"Flight number of the current leg",
Sleg_flight_suffix "Flight suffix" ?"Flight suffix of the current leg. If no flight suffix exist ''*'' is used",
Sleg_departure_airport "Departure airport" ?"Departure airport of current leg[config][translation_type=airportCodes]",
Aleg_departure_date_utc "Departure date (UTC)" ?"Departure date of current leg in UTC-time",
Sonw_leg_carrier_code "Onward carrier code" ?"Carrier code of the next leg in A/C rotation",
Ionw_leg_flight_number "Onward flight number" ?"Flight number of the next leg in A/C rotation",
Sonw_leg_flight_suffix "Onward flight suffix" ?"Flight suffix of the next leg in A/C rotation. If no flight suffix exist ''*'' is used",
Sonw_leg_departure_airport "Onward departure airport" ?"Departure airport of the next leg in A/C rotation",
Aonw_leg_departure_date_utc "Onward departure date (UTC)" ?"Departure date of the next leg in A/C rotation in UTC-time",
'

DECLARE @TT Table
(
APL_Name nvarchar(10),
TXT nvarchar(MAX)
)
--=======================================
DECLARE @TF Table
(
APL_Name nvarchar(10),
DateTakeoff datetime,
CarComp1 nvarchar(2),
Flight1 nvarchar(5),
Leg1 nvarchar(1),
CodeIATA1 nvarchar(3),
DTTakeoff1 nvarchar(10)
)

INSERT INTO @TF
Select 
	APL.Name APL_Name,
	PFTO.DateTakeoff,
	------------------------------------
	'DP' CarComp,
	FL.Name Flight,
	'*' Leg,
	Ap.CodeIATA,
	UPPER(Format(PFTO.DateTakeoff,'ddMMMyyyy')) DTTakeoff
	From Ap_PlanFlightAirPorts PFTO  
	LEft JOIN Ap_PlanFlights PF ON PFTO.Ap_PlanFlightID=PF.Ap_PlanFlightID 
	LEft JOIN Ap_PlanFlightAirPorts PFLA ON PFTO.Ap_PlanFlightID=PFLA.Ap_PlanFlightID and PFLA.AirPortNumber=PFTO.AirPortNumber+1
	LEFT JOIN Ap_AirPorts AP ON AP.Ap_AirPortID=PFTO.Ap_AirPortID
	LEFT JOIN Ap_AirPorts AP1 ON AP1.Ap_AirPortID=PFLA.Ap_AirPortID
	LEFT JOIN Fl_Flights FL ON FL.Fl_FlightID=PF.Fl_FlightID
	LEFT JOIN At_Plns APL on APL.At_PlnID=PFTO.At_PlnIDTakeoff
	LEFT JOIN At_Plns APL1 on APL1.At_PlnID=PFLA.At_PlnIDLanding
	Where 
	PFTO.DateTakeoff between @DT_begin and @DT_end
	AND PF.OnlyFinanceCalculation = 0
	AND PF.Sh_ScheduleVariantTypeID IS NULL
	and PF.Status & 256 <> 256  ----отмененные рейсы
	AND ISNULL(FL.FlightVariant,0) = 0 
	AND PF.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки
	and APL.Name=APL1.Name
	Order by APL.Name,PFTO.DateTakeoff
--=============================================
INSERT INTO @TT
Select '1', @Handle

INSERT INTO @TT
Select 
TX.APL_Name APL ,
TX.FST+', '+TX.SST+';'  TXT
From 	(
		Select
		RTT.APL_Name,
		Case
		When PPTX is null and NNTX is not null then TTX
		When PPTX is not null and NNTX is not null then TTX
		When PPTX is not null and NNTX is  null  then null
		End FST,
		Case
		When PPTX is null and NNTX is not null then NNTX
		When PPTX is not null and NNTX is not null then NNTX
		When PPTX is not null and NNTX is  null  then null
		End SST
		From 	(Select 
				T.APL_Name,
				-----------------------------------------
				--PP.DateTakeoff,
				('"'+ PP.CarComp1+'", '+PP.Flight1+', "'+PP.Leg1+'", '+'"'+PP.CodeIATA1+'", '+' '+PP.DTTakeoff1) as  PPTX,
				------------------------------------------
				--T.DateTakeoff,
				('"'+ t.CarComp1+'", '+t.Flight1+', "'+t.Leg1+'", '+'"'+t.CodeIATA1+'", '+' '+t.DTTakeoff1) as  TTX,
				------------------------------------------
				--NN.DateTakeoff,
				('"'+ NN.CarComp1+'", '+NN.Flight1+', "'+NN.Leg1+'", '+'"'+NN.CodeIATA1+'", '+' '+NN.DTTakeoff1) as  NNTX
				------------------------------------------
				From @TF T
				OUTER APPLY (select Top 1 * From @TF P where P.APL_Name=T.APL_Name and P.DateTakeoff < T.DateTakeoff order by P.DateTakeoff DESC) as  PP
				OUTER APPLY (select Top 1 * From @TF N where N.APL_Name=T.APL_Name and N.DateTakeoff > T.DateTakeoff order by N.DateTakeoff ASC ) as  NN
				) RTT
		Where 
		Case
		When PPTX is null and NNTX is not null then TTX
		When PPTX is not null and NNTX is not null then TTX
		When PPTX is not null and NNTX is  null  then null
		End is not null
		) TX

Select * From @TT
--===========================================
END
