﻿ ----=============================================
 ----Author:		<Author,,Name>
 ----Create date: <Create Date,,>
 ----Description:	<Description,,>
 ----=============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Crew_AIRPORT]
@DTbegin datetime='2019-05-01 00:00:00',  
@DTEnd datetime='2019-06-01 00:00:00'

AS
BEGIN
--=======================================================================
--Crew_AIRPORT
--=======================================================================
--=======================================================================
--=======================================================================

------Declare
------@TBB nvarchar(5)='1851',
------@DTbegin datetime='2019-09-01 00:00:00',  
------@DTEnd datetime='2019-10-01 00:00:00'


Declare
@DTending nvarchar(10)='30DEC2075',
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))

DECLARE @AP table
(
NAmeAP nvarchar(200),
CatAP nvarchar(3),
Tren integer,
CodeCountryAP nvarchar(5),
Country nvarchar(5)
)

INSERT INTO @AP
SELECT 
PR.FullNameEng NAmeAP,
Case 
When PR.Class=1 then 'A'
When PR.Class=2 then 'B'
When PR.Class=3 then 'B+'
When PR.Class=4 then 'C'
end CatAP,
Case 
When PR.Class IN (1,2) then 0
When PR.Class IN (3,4) then 1
end Tren,
PR.CodeIATA CodeCountryAP,
CN.CodeEng
From   Ap_AirPorts PR
 INNER JOIN Gn_Citys CT On CT.Gn_CityID=PR.Gn_CityID
 INNER JOIN Gn_Countrys CN On Cn.Gn_CountryID=CT.Gn_CountryID
Where PR.Class>0

----------------------------------------------------------------------------------------
		
DECLARE @CR table
(
CatAP nvarchar(3),
CodeCountryAP nvarchar(5),
Tren integer,
MVL integer,
Lang integer,
Pasport integer,
ArmChair nvarchar(5),
BlockTime integer
)
INSERT INTO @CR
VALUES
--==================Новые==============================
 ('A', 'RU', 0,	0 ,	0,	0, 	'КВС',0),
 ('A', 'RU', 0,	0 ,	0,	0,  'ВП', 0), 
 ('A', 'RU', 0,	0 ,	0,	0, 	'ПИ', 0),
 ('A', '',	 0,	1 ,	1,	1, 	'КВС',0),
 ('A', '',	 0,	1 ,	1,	1,  'ВП', 0),
 ('A', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
---------------------------------------
 ('B', 'RU', 0,	0 ,	0,	0, 	'КВС',0),
 ('B', 'RU', 0,	0 ,	0,	0,  'ВП', 0), 
 ('B', 'RU', 0,	0 ,	0,	0, 	'ПИ', 0),
 ('B', '',	 0,	1 ,	1,	1, 	'КВС',0),
 ('B', '',	 0,	1 ,	1,	1,  'ВП', 0), 
 ('B', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
----------------------------------------
 ('B+', 'RU',1, 0 ,	0,	0, 	'КВС',500),
 ('B+', 'RU',0, 0 ,	0,	0,  'ВП', 300 ),
 ('B+', 'RU',0, 0 ,	0,	0, 	'ПИ', 0),
 ('B+', '',	 1,	1 ,	1,	1, 	'КВС',500),
 ('B+', '',	 0,	1 ,	1,	1,  'ВП', 300), 
 ('B+', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
 ---------------------------------------
 ('C', 'RU', 1,	0 ,	0,	0, 	'КВС',800),
 ('C', 'RU', 1,	0 ,	0,	0,  'ВП', 1000), 
 ('C', 'RU', 0,	0 ,	0,	0, 	'ПИ', 0),
 ('C', '' ,	 1,	1 ,	1,	1, 	'КВС',800),
 ('C', '' ,  1,	1 ,	1,	1,  'ВП', 1000),
 ('C', '' ,	 0,	1 ,	1,	1, 	'ПИ', 0)

--==================Новые==============================
---- ('A', 'RU', 0,	0 ,	0,	0, 	'КВС',0),
---- ('A', 'RU', 0,	0 ,	0,	0,  'ВП', 0), 
---- ('A', 'RU', 0,	0 ,	0,	0, 	'ПИ', 0),
---- ('A', '',	 0,	1 ,	1,	1, 	'КВС',0),
---- ('A', '',	 0,	1 ,	1,	1,  'ВП', 0),
---- ('A', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
-------------------------------------------
---- ('B', 'RU', 0,	0 ,	0,	0, 	'КВС',0),
---- ('B', 'RU', 0,	0 ,	0,	0,  'ВП', 0), 
---- ('B', 'RU', 0,	0 ,	0,	0, 	'ПИ', 0),
---- ('B', '',	 0,	1 ,	1,	1, 	'КВС',0),
---- ('B', '',	 0,	1 ,	1,	1,  'ВП', 0), 
---- ('B', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
--------------------------------------------
---- ('B+', 'RU',1, 0 ,	0,	0, 	'КВС',200),
---- ('B+', 'RU',1, 0 ,	0,	0,  'ВП', 0 ),
---- ('B+', 'RU',0, 0 ,	0,	0, 	'ПИ', 0),
---- ('B+', '',	 1,	1 ,	1,	1, 	'КВС',200),
---- ('B+', '',	 1,	1 ,	1,	1,  'ВП', 0), 
---- ('B+', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
---- ---------------------------------------
---- ('C', 'RU', 1,	0 ,	0,	0, 	'КВС',800),
---- ('C', 'RU', 1,	0 ,	0,	0,  'ВП', 1000), 
---- ('C', 'RU', 0,	0 ,	0,	0, 	'ПИ', 0),
---- ('C', '' ,	 1,	1 ,	1,	1, 	'КВС',800),
---- ('C', '' ,  1,	1 ,	1,	1,  'ВП', 1000),
---- ('C', '' ,	 0,	1 ,	1,	1, 	'ПИ', 0)

 --==================Старые==============================
------ ('A', 'RU', 0,	0 ,	0,	0, 	'КВС',0),
------ ('A', 'RU', 0,	0 ,	0,	0,  'ВП', 0), 
------ ('A', 'RU', 0,	0 ,	0,	0, 	'ПИ', 0),
------ ('A', '',	 0,	1 ,	1,	1, 	'КВС',0),
------ ('A', '',	 0,	1 ,	1,	1,  'ВП', 0),
------ ('A', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
---------------------------------------------
------ ('B', 'RU', 0,	0 ,	0,	0, 	'КВС',0),
------ ('B', 'RU', 0,	0 ,	0,	0,  'ВП', 0), 
------ ('B', 'RU', 0,	0 ,	0,	0, 	'ПИ', 0),
------ ('B', '',	 0,	1 ,	1,	1, 	'КВС',0),
------ ('B', '',	 0,	1 ,	1,	1,  'ВП', 0), 
------ ('B', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
----------------------------------------------
------ ('B+', 'RU',1, 0 ,	0,	0, 	'КВС',200),
------ ('B+', 'RU',0, 0 ,	0,	0,  'ВП', 0 ),
------ ('B+', 'RU',0, 0 ,	0,	0, 	'ПИ', 0),
------ ('B+', '',	 1,	1 ,	1,	1, 	'КВС',200),
------ ('B+', '',	 0,	1 ,	1,	1,  'ВП', 0), 
------ ('B+', '',	 0,	1 ,	1,	1, 	'ПИ', 0),
------ ---------------------------------------
------ ('C', 'RU', 1,	0 ,	0,	0, 	'КВС',800),
------ ('C', 'RU', 0,	0 ,	0,	0,  'ВП', 1000), 
------ ('C', 'RU', 1,	0 ,	0,	0, 	'ПИ', 0),
------ ('C', '' ,	 1,	1 ,	1,	1, 	'КВС',800),
------ ('C', '' ,  0,	1 ,	1,	1,  'ВП', 1000),
------ ('C', '' ,	 1,	1 ,	1,	1, 	'ПИ', 0)
--================================================================
----------Select
---------- T.*,
----------CNT.CodeEng
---------- From @AP T
---------- LEFT JOIN Ap_AirPorts AP ON AP.CodeIATA=T.CodeCountryAP
---------- LEFT JOIN Gn_Citys CT ON CT.Gn_CityID=AP.Gn_CityID
---------- LEFT JOIN Gn_Countrys CNT ON CNT.Gn_CountryID=CT.Gn_CountryID

-------- --Select *From @CR
-------- ----Допуск на МВЛ	
-------- ----IV уровень английского языка (ICAO)	
-------- ----Загран. паспорт	
-------- ----RANK

 ------Select APP.CodeCountryAP,  CR.* From @AP APP 
 ------Inner join @CR CR On CR.CatAP=APP.CatAP and (APP.Country=CR.CodeCountryAP or (APP.Country<>'RU' and CR.CodeCountryAP=''))	
 ------where  APP.CatAP<>'A'	--and CR.Tren=0
 ------Order by APP.CodeCountryAP
		
--====================================================
Select
TT.TableNumber,
TT.RR,
TT.CodeCountryAP,
MIN(TT.valid_from_date2) valid_from_date2,
MAX(TT.valid_to_date2) valid_to_date2
--SUM(TT.TimeFlightWithEngine) TimeFlightWithEngine
From 
	(	SELECT 
		replace(Pr.TableNumber,'/','00') TableNumber,
		'AIRPORT' RR,
		AA.CodeCountryAP,
		UPPER(isnull(FORMAT( MAX(Pr_PersonnelFlights.DateTakeoffReal),'ddMMMyyyy'),''))  valid_from_date2,
		UPPER(isnull(FORMAT(DATEADD(d, ValidPeriod, MAX(Pr_PersonnelFlights.DateTakeoffReal)),'ddMMMyyyy'),@DTending))  valid_to_date2,
		0 TimeFlightWithEngine
		----Ap_Airports.Name, 
		----Pr_ArmChairTypes.Name,
		---- MAX(Pr_PersonnelFlights.DateTakeoffReal) BeginDate, 
		---- DATEADD(d, ValidPeriod, MAX(Pr_PersonnelFlights.DateTakeoffReal)) EndDate, 
		---- MAX(Pr_PersonnelFlightID) RecordID, 
		---- MAX(Pr_PersonnelFlights.Type) Type, 
		---- Ap_AirportCrewLimitTypes.ValidPeriod, 
		----CASE WHEN SUM(CASE WHEN Pr_PersonnelFlights.Ap_PlanFlightAirportID IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*) = 1 THEN 1 ELSE NULL END ExistLinks 
		 FROM Ap_AirportCrewLimits 
		 INNER JOIN Ap_AirportCrewLimitTypes ON Ap_AirportCrewLimitTypes.Ap_AirportCrewLimitTypeID = Ap_AirportCrewLimits.Ap_AirportCrewLimitTypeID 
		 INNER JOIN Ap_Airports ON Ap_Airports.Ap_AirportID = Ap_AirportCrewLimits.Ap_AirportID 
		 INNER  JOIN @AP AA on AA.CodeCountryAP=Ap_Airports.CodeIATA
		 INNER JOIN Pr_PersonnelFlights ON Pr_PersonnelFlights.Ap_AirportIDLanding = Ap_AirportCrewLimits.Ap_AirportID AND Pr_PersonnelFlights.Pr_ArmChairTypeID = Ap_AirportCrewLimits.Pr_ArmChairTypeID 
		 INNER JOIN Pr_Personnels PR  ON PR.Pr_PersonnelID=Pr_PersonnelFlights.Pr_PersonnelID
		 INNER JOIN Pr_ArmChairTypes ON Pr_ArmChairTypes.Pr_ArmChairTypeID = Pr_PersonnelFlights.Pr_ArmChairTypeID 
		 OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
		 OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
		Where 
		(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
		and isnumeric(replace(Pr.TableNumber,'/','00')) >''
		and (Pr.Pr_CategoryTypeID in (1))
		--and Pr.TableNumber=@TBB
		GROUP BY
		Pr.TableNumber, 
		AA.CodeCountryAP,
		Ap_AirportCrewLimitTypes.ValidPeriod  
		HAVING 
		(DATEADD(d, ValidPeriod, MAX(Pr_PersonnelFlights.DateTakeoffReal)) > @DTACT)
		--ORDER BY Pr.TableNumber

		UNION

		Select 
		PP.TableNumber,
		'AIRPORT' PP,
		APP.CodeCountryAP,
		UPPER(isnull(FORMAT(@DTACT,'ddMMMyyyy'),''))  valid_from_date2,
		UPPER(isnull(FORMAT(@DTACT2,'ddMMMyyyy'),@DTending))  valid_to_date2,
		pp.TimeFlightWithEngine
		From 
			(Select 
			replace(Pr.TableNumber,'/','00') TableNumber,
			Case
				When Pr.Pr_CategoryTypeID=1 and STF.Code in ('CP','СР', 'CPP', 'КВС','КВС-стажер','Нач ОПЛС' ) then 'КВС' --,'SI'
				When Pr.Pr_CategoryTypeID=1 and STF.Code in ('П-Инсп','CRMI','FI','DOLR','SPI','COPLS','CRMI', 'ПИ ИБП','ПИ','ИТ','CRMIFD' ) then 'ПИ' --,'SI'
				When Pr.Pr_CategoryTypeID=1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст','ВП') then 'ВП'
				else''
			End  MainFunc,
			case when PAS.FL>0 then 1 else 0 end Pasport,
			case when LAN.Fl>0 then 1 else 0 end Lang,
			case when MVL.Fl>0 then 1 else 0 end mvl,
			INS.CNT,
			isnull(FlightSum.TimeFlightIndependent,0)/3600 TimeFlightIndependent,
			isnull(FlightSum.TimeFlightWithEngine,0)/3600 TimeFlightWithEngine
			From Pr_Personnels PR
			OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
			LEft JOIN Pr_StaffTrees STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
			left JOIN  Pr_StaffTrees  PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
			left JOIN  Ap_AirPorts    AP     on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
			OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
			OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
			OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
			---------------------------PASSPORT---------------------------------------------
			OUTER APPLY (select top 1 isnull(CER.Pr_DocumentTypeID,0) Fl, CER.Pr_DocumentTypeID, CER.DateBegin, CER.DateEnd  
						From Pr_Certifications CER 
						where 
						CER.Pr_PersonnelID=PR.Pr_PersonnelID 
						and  CER.Pr_DocumentTypeID in (2,18,29)
						and ((getdate() >= CER.DateBegin or getdate()<= CER.DateBegin) and (getdate() <=CER.DateEnd or CER.DateEnd is null))
						ORDER BY CER.DateBegin DESC
						) PAS
			-------------------------------------LANGUAGE----------------------------------------------------
			OUTER APPLY (select top 1 isnull(CER.Pr_LimitTypeID,0) Fl, CER.Pr_LimitTypeID, CER.DateBegin, CER.DateEnd  
						From Pr_Certifications CER 
						where 
						CER.Pr_PersonnelID=PR.Pr_PersonnelID 
						and  CER.Pr_LimitTypeID in (36,38)
						and ((getdate() >= CER.DateBegin or getdate()<= CER.DateBegin) and (getdate() <=CER.DateEnd or CER.DateEnd is null))
						ORDER BY CER.DateBegin DESC
						) LAN
			-------------------------------------MVL----------------------------------------------------
			OUTER APPLY (select top 1 isnull(CER.Pr_EducationTypeID,0) Fl, CER.Pr_EducationTypeID, CER.DateBegin, CER.DateEnd  
						From Pr_Certifications CER 
						where 
						CER.Pr_PersonnelID=PR.Pr_PersonnelID 
						and  CER.Pr_EducationTypeID in (155,154)
						and ((getdate() >= CER.DateBegin or getdate()<= CER.DateBegin) and (getdate() <=CER.DateEnd or CER.DateEnd is null))
						ORDER BY CER.DateBegin DESC
						) MVL
			----------------------------------TimeFlightIndependent-----------------------------------------
			OUTER APPLY (SELECT 
						SUM(Landings) Landings, 
						SUM(LandingsOnDevices) LandingsOnDevices, 
						SUM(TimeOnDevices) TimeOnDevices, 
						SUM(TimeEngineWorkBefore + TimeFlight + TimeEngineWorkAfter) TimeFlightWithEngine, 
						SUM(TimeFlightNight) TimeFlightNight, 
						SUM(TimeFlightIndependent) TimeFlightIndependent, 
						SUM(TimeWork) TimeWork
						FROM Pr_PersonnelFlights fsum 
						WHERE 
						fsum.Pr_PersonnelID = Pr.Pr_PersonnelID 
						AND	fsum.DateTakeoffReal < @DTACT2 ----dateadd(d,1,EOMONTH(dateadd(m,-1,getdate()))) 
						AND  fsum.Type <> 2  
						AND  fsum.Type <> 3 
						AND  fsum.Type <> 1  
					) FlightSum 
			----------------------------------INS---------------------------------
			OUTER APPLY (Select Count(*) CNT From Pr_Certifications CER 
							where 
							CER.Pr_PersonnelID=PR.Pr_PersonnelID 
							and  (CER.Pr_LimitTypeID in (51,65,60,57,56,58) or CER.Pr_EducationTypeID in (65,71))
							and (
									--(@DTACT between CER.DateBegin and CER.DateEnd) or (CER.DateEnd is null) or (@DTACT2 between CER.DateBegin and CER.DateEnd)
									(@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) 
									and 
									(@DTACT <=CER.DateEnd or CER.DateEnd is null)
								)
						) INS 
			Where 
			(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
			and isnumeric(replace(Pr.TableNumber,'/','00')) >''
			and (Pr.Pr_CategoryTypeID in (1))
		) PP
		INNER JOIN (Select APP1.CodeCountryAP, CR.CatAP, CR.Lang, CR.ArmChair, CR.MVL, CR.Tren, CR.Pasport, CR.BlockTime From @AP APP1 
					Inner join @CR CR On CR.CatAP=APP1.CatAP and (APP1.Country=CR.CodeCountryAP or (APP1.Country<>'RU' and CR.CodeCountryAP=''))	
					where  APP1.CatAP<>'A'	and CR.Tren=0) APP ON ((APP.ArmChair=PP.MainFunc) or ((PP.CNT>0) and (APP.ArmChair='ПИ'))) 
																	and (APP.Lang=PP.Lang or APP.Lang=0) 
																	and (APP.MVL=PP.mvl or APP.mvl=0) 
																	and (APP.Tren=0)
																	and (APP.Pasport=PP.Pasport or APP.Pasport=0)	
																	and (PP.TimeFlightWithEngine>=APP.BlockTime)	
		Where  
		APP.CodeCountryAP<>''
		--and PP.TableNumber=@TBB
		) TT
GROUP BY 
TT.TableNumber,
TT.RR,
TT.CodeCountryAP
--HAVING TT.TableNumber=@TBB
ORDER BY TT.TableNumber
End