﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_InsertUniForms]
				@STRXML nvarchar(MAX)
AS
BEGIN

----DECLARE @STRXML nvarchar(max)='<Table>
----<Код>508</Код>
----<Товар>Фуражка IMG-1 (Биг-Евро) шнур gold, "Дубки" gold, кокарда</Товар>
----<Размер>58</Размер>
----<Норма xml:space="preserve"> </Норма>
----<Получил>1</Получил>
----<НеПолучил>-1</НеПолучил>
----<Документ>Выдача одежды Сотруднику (одиночная) кгВСт-0276 (21.01.20)</Документ>
----<Таб_x0023_Ном>752</Таб_x0023_Ном>
----<ФИО>Азин Валентин Владимирович</ФИО>
----<Подразделение>Лётная служба</Подразделение>
----<Должность>КВС</Должность>
----</Table>'

DECLARE @TBL Table
(
NameCode nvarchar(10),
Arriv integer,
NotArriv integer,
Docum nvarchar(2000),
TabNumber nvarchar(10),
Comment nvarchar(max)
)

DECLARE @xml xml
SELECT @xml = CAST(CAST(@STRXML AS VARBINARY(MAX)) AS XML) 

INSERT INTO @TBL
SELECT 
	x.Rec.query('./Код').value('.', 'nvarchar(10)') AS 'NameCode',
	x.Rec.query('./Получил').value('.', 'integer') AS 'Arriv',
	x.Rec.query('./НеПолучил').value('.', 'integer') AS 'NotArriv',
	x.Rec.query('./Документ').value('.', 'nvarchar(2000)') AS 'Docum',
	x.Rec.query('./Таб_x0023_Ном').value('.', 'nvarchar(10)') AS 'TabNumber',
	------------------------------------------------------------------------
	x.Rec.query('./Товар').value('.', 'nvarchar(1000)') +' / '+ x.Rec.query('./Размер').value('.', 'nvarchar(10)')
	+ ' / ' + x.Rec.query('./ФИО').value('.', 'nvarchar(2000)')
	+ ' / '+	x.Rec.query('./Должность').value('.', 'nvarchar(2000)') AS 'Comment'
	-------------------------------------------------------------------------
FROM @xml.nodes('/Table') as x(Rec)


--==============================New record===============================
--=======================================================================
INSERT INTO Pr_Uniforms
(
Pr_PersonnelID,
Pr_UniformTypeID,
DateBegin,
DateEnd,
Comment,
Executor,
Inspector,
LastChangeTime,
Quantity
)
Select 
PR.Pr_PersonnelID,
UFT.Pr_UniformTypeID,
CASE
WHEN CHARINDEX('(', Docum, LEN(Docum)-12)>0 then CONVERT(smallDatetime,SUBSTRING(Docum,CHARINDEX('(', Docum, LEN(Docum)-12)+1,8),4)
ELSE NULL
END DTBegin,
dateadd(yy,lmm.Years,CASE
						WHEN CHARINDEX('(', Docum, LEN(Docum)-12)>0 then CONVERT(smallDatetime,SUBSTRING(Docum,CHARINDEX('(', Docum, LEN(Docum)-12)+1,8),4)
						ELSE NULL
					 END) DTEnd,
TT.Comment,
259,
259,
getdate(),
TT.Arriv
From @TBL TT
INNER JOIN Pr_UniformTypes UFT ON UFT.CodeEng=TT.NameCode
OUTER APPLY (Select TOP 1 LM.Years From Pr_UniformLimits LM Where LM.Pr_UniformTypeID=UFT.Pr_UniformTypeID) LMM
INNER JOIN Pr_Personnels PR on PR.TableNumber=TT.TabNumber
LEFT JOIN Pr_Uniforms UF on UF.Pr_UniformTypeID=UFT.Pr_UniformTypeID and PR.Pr_PersonnelID=UF.Pr_PersonnelID 
and CASE
	WHEN CHARINDEX('(', Docum, LEN(Docum)-12)>0 then CONVERT(smallDatetime,SUBSTRING(Docum,CHARINDEX('(', Docum, LEN(Docum)-12)+1,8),4)
	ELSE NULL
	END between UF.DateBegin and  UF.DateEnd
Where 
TT.Arriv>0
and CASE
	WHEN CHARINDEX('(', Docum, LEN(Docum)-12)>0 then CONVERT(smallDatetime,SUBSTRING(Docum,CHARINDEX('(', Docum, LEN(Docum)-12)+1,8),4)
	ELSE NULL
	END is not null
and UF.DateEnd is null
and lmm.Years is not null
END

--===========================Delete old record===========================
--=======================================================================
Delete Pr_Uniforms 
WHERE Pr_UniformID IN
(Select 
UF.Pr_UniformID
From @TBL TT
INNER JOIN Pr_UniformTypes UFT ON UFT.CodeEng=TT.NameCode
OUTER APPLY (Select TOP 1 LM.Years From Pr_UniformLimits LM Where LM.Pr_UniformTypeID=UFT.Pr_UniformTypeID) LMM
INNER JOIN Pr_Personnels PR on PR.TableNumber=TT.TabNumber
LEFT JOIN Pr_Uniforms UF on UF.Pr_UniformTypeID=UFT.Pr_UniformTypeID and PR.Pr_PersonnelID=UF.Pr_PersonnelID 

Where 
TT.Arriv>0
and CASE
	WHEN CHARINDEX('(', Docum, LEN(Docum)-12)>0 then CONVERT(smallDatetime,SUBSTRING(Docum,CHARINDEX('(', Docum, LEN(Docum)-12)+1,8),4)
	ELSE NULL
	END is not null
and UF.LastChangeTime < CASE
						WHEN CHARINDEX('(', Docum, LEN(Docum)-12)>0 then CONVERT(smallDatetime,SUBSTRING(Docum,CHARINDEX('(', Docum, LEN(Docum)-12)+1,8),4)
						ELSE NULL
						END
)
