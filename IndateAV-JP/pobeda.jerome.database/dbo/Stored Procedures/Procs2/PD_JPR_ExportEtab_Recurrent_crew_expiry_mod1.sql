﻿ --=============================================
 ----Author:		Koroteev
 ----Create date: <Create Date,,>
 ----Description:	<Description,,>
 --=============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Recurrent_crew_expiry_mod1]
@DTbegin datetime,  
@DTEnd datetime
AS
BEGIN
--===================Test===================================
------Declare
------@DTbegin datetime='2020-07-01 00:00:00',  
------@DTEnd datetime='2020-07-31 23:59:00'
--======================================================
Declare
@DTending nvarchar(10)='30DEC2075',
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))

Declare @CC nvarchar(max)=
'4
Screw_id,
Srecurrent_type,
Srequired_equipment,
Aexpiry_date,
'

--=======================================================
DECLARE @TT table
(
TableNumber nvarchar(30),
Attribute_type  nvarchar(30), 
Srequired_equipment  nvarchar(30),
Aexpiry_date  nvarchar(30),
CNT integer,
STRLN nvarchar(max)
)
Declare @ATT table
(
TableNumber nvarchar(10),
Attribute_type  nvarchar(30), 
Srequired_equipment  nvarchar(30),
Aexpiry_date  nvarchar(30),
CNT integer
----DateEndCIN datetime,
----DateEndPP datetime,
----DateEnd datetime
)
--=======================================================
Declare @TBL TABLE
(
TypeCrew	integer,
TypeSert	nvarchar(30),
CaryIN		integer,
PPeriod		integer,
CaryOUT		integer,
PastEvent	integer,
TypeArg     nvarchar(3),
Arg			nvarchar(30),
ArgCaryIn	integer,
ArgPPeriod	integer,
AddMonn		integer
)

Insert into @TBL
Values
(5,'RSTI',	1,	1,  1,  0,  'Ev',	'RSTI',		1,	0,	3),
(5,'RSTI',	1,	1,	1,	0,	'Ev',	'RST',		1,	0,	3),
(5,'SEC',	1,	1,	1,	0,	'Ev',	'SEC',		1,	0,	3),
(5,'EPW',	1,	1,	1,	0,	'Ev',	'EPW',		1,	0,	3),
(5,'EPG',	1,	1,	1,	0,	'Ev',	'EPG',		1,	0,	3),
(5,'CRM',	1,	1,	1,	0,	'Ev',	'CRM',		1,	0,	3),
(5,'RDG',	1,	1,	1,	0,	'Ev',	'RDG',		1,	0,	3),
----------------------------------------------------------------
(5,'PTTCC',	0,	0,	0,	1,	'Ev',	'PTTCC',	1,	0,	3),
(5,'EPG',	0,	0,	0,	1,	'Ev',	'PTTCC',	1,	0,	3),
(5,'RDG',	0,	0,	0,	1,	'Ev',	'PTTCC',	1,	0,	3),
(5,'CRM',	0,	0,	0,	1,	'Ev',	'PTTCC',	1,	0,	3),
(5,'SEC',	0,	0,	0,	1,	'Ev',	'PTTCC',	1,	0,	3),
-----------------------------------------------------------------
(5,'EPG',	0,	0,	0,	1,	'Ev',	'PTTCC',	0,	1,	3),
(5,'RDG',	0,	0,	0,	1,	'Ev',	'PTTCC',	0,	1,	3),
(5,'CRM',	0,	0,	0,	1,	'Ev',	'PTTCC',	0,	1,	3),
(5,'SEC',	0,	0,	0,	1,	'Ev',	'PTTCC',	0,	1,	3),
-----------------------------------------------------------------
(5,'EPG',	1,	1,	1,	0,	'TS',	'PTTCC',	0,	1,	3),
(5,'RDG',	1,	1,	1,	0,	'TS',	'PTTCC',	0,	1,	3),
(5,'CRM',	1,	1,	1,	0,	'TS',	'PTTCC',	0,	1,	3),
(5,'SEC',	1,	1,	1,	0,	'TS',	'PTTCC',	0,	1,	3),
------------------------------------------------------------------
(5,'MED1CC',0,	0,	0,	1,	'Ev',	'MED1CC',	1,	0,	3),
(5,'MED6CC',0,	0,	0,	1,	'Ev',	'MED1CC',	1,	0,	3),
(5,'MED6CC',1,	1,	1,	0,	'TS',	'MED1CC',	0,	1,	3),
(5,'MED6CC',1,	1,	1,	0,	'Ev',	'MED1CC',	0,	1,	3),----добавили 16.04
(5,'MED',	0,	0,	0,	1,	'Ev',	'MED',		1,	0,	3),
(5,'MED1CC',0,	0,	0,	1,	'Ev',	'MED',		1,	0,	3),
(5,'MED6CC',0,	0,	0,	1,	'Ev',	'MED',		1,	0,	3),
(5,'MED1CC',1,	1,	1,	0,	'Ev',	'MED',		0,	1,	3),----добавили 16.04
(5,'MED6CC',1,	1,	1,	0,	'Ev',	'MED',		0,	1,	3),----добавили 16.04
(5,'MED6CC',1,	1,	1,	0,	'TS',	'MED',		0,	1,	3),
(5,'MED1CC',1,	1,	1,	0,	'TS',	'MED',		0,	1,	3),
(5,'MED6CC',1,	1,	1,	0,	'Ev',	'MED6CC',	1,	0,	3),
----------------------------------------------------------
(5,'PTTCC',	0,	0,	0,	1,	'Ev',	'TRC',		1,	0,	3),
(5,'EPG',	0,	0,	0,	1,	'Ev',	'TRC',		1,	0,	3),
(5,'RDG',	0,	0,	0,	1,	'Ev',	'TRC',		1,	0,	3),
(5,'SEC',	0,	0,	0,	1,	'Ev',	'TRC',		1,	0,	3),
(5,'CRM',	0,	0,	0,	1,	'Ev',	'TRC',		1,	0,	3),
------------------------------------------------------------
(5,'PTTCC',	1,	1,	1,	0,	'Ev',	'TRC',		0,	1,	3),
(5,'EPG',	1,	1,	1,	0,	'Ev',	'TRC',		0,	1,	3),
(5,'RDG',	1,	1,	1,	0,	'Ev',	'TRC',		0,	1,	3),
(5,'SEC',	1,	1,	1,	0,	'Ev',	'TRC',		0,	1,	3),
(5,'CRM',	1,	1,	1,	0,	'Ev',	'TRC',		0,	1,	3),
----------------------------------------------------------------
(5,'LC',	1,	1,	1,	0,	'PP',	'PPKE_5-4',	1,	0,	3), ----добавили 19.04

--========================================================================
(1,'SEC',	1,	1,	1,	0,	'Ev',	'SEC',		1,	0,	3),
(1,'EPW',	1,	1,	1,	0,	'Ev',	'EPW',		1,	0,	3),
(1,'EPG',	1,	1,	1,	0,	'Ev',	'EPG',		1,	0,	3),
(1,'CRM',	1,	1,	1,	0,	'Ev',	'CRM',		1,	0,	3),
(1,'RDG',	1,	1,	1,	0,	'Ev',	'RDG',		1,	0,	3),
(1,'PTT',	1,	1,	1,	0,	'Ev',	'PTT',		1,	0,	3),
(1,'FFS',	1,	1,	1,	0,	'Ev',	'FFS',		1,	0,	3),
(1,'FFS',	1,	1,	1,	0,	'Ev',	'FFS',		0,	1,	3),
(1,'MED6',	1,	1,	1,	0,	'Ev',	'MED6',		1,	0,	3),
(1,'MED1',	0,	0,	0,	1,	'Ev',	'MED1',		1,	0,	3),
(1,'MED6',	0,	0,	0,	1,	'Ev',	'MED1',		1,	0,	3),
(1,'MED6',	1,	1,	1,	0,	'Ev',	'MED1',		0,	1,	3),----добавили 16.04
(1,'MED6',	1,	1,	1,	0,	'TS',	'MED1',		0,	1,	3),
--------------------------------------------------------------
(1,'LC',	1,	1,	1,	0,	'PP','PPLS_3-1-3',	1,	0,	3) ----добавили 19.04

--========================================================================
DEclare @CCT Table
(
Code nvarchar(30),
Name nvarchar(2000),
CatService integer,
CatRole nvarchar(30),
Device nvarchar(30),
LimitId integer,
DocumentId integer,
EducateId integer
)
INSERT INTO @CCT
Values
('FFS',		'Тренажер',										1,	null,	null,	35,	null,	null),
('SEC',		'Авиационная безопасность',						1,	null,	null,	75	,	null,	36),
('EPW',		'КПК Действия в аварийной обстановке (Вода)',	1,	null,	null,	53	,	null,	null),
('EPG',		'КПК Действия в аварийной обстановке (Суша)',	1,	null,	null,	54	,	null,	null),
('LC',		'Квалификационная проверка',					1,	null,	'73H',	46	,	null,	null),
('RDG',		'Перевозка ОГ',									1,	null,	null,	73	,	null,	41),
('PTT',		'Периодическая теоретическая подготовка',		1,	null,	null,	76	,	null,	42),
('MED1',	'Справка МО (годовой)',							1,	null,	null,	30	,	null,	null),
('MED6',	'Справка МО (полугодичный)',					1,	null,	null,	37	,	null,	null),
('CRM',		'Человеческий фактор CRM',						1,	null,	null,	74	,	null,	null),
--==============================================================================================================
('PTTCC',		'КПК--!!!Бортпроводников ВС',				5,	null,	null,	null,	null,	73),
('PTTCC',		'КПК--!!!Авиационная безопасность',			5,	null,	null,	null,	null,	36),
('PTTCC',		'КПК--!!!CRM',								5,	null,	null,	null,	null,	37),
--===========================================================================================================
('PBC',		'Проверка предполетного брифинга',				5,	null,	null,	68	,	null,	null),
('SANA',	'Санитарный минимум',							5,	null,	null,	42	,	null,	null),
('RSTI',		'Периодическая подготовка СБЭ',				5,	'SCC',	null,	null,	null,	59),
('MED',		'Справка ВЛЭК(полная)',							5,	null,	null,	41	,	null,	null),
--('MED3',	'Справка МО (квартальный)',						5,	null,	null,	null,	null,	null),
('SEC',		'Авиационная безопасность',						5,	null,	null,	75	,	null,	36),
('EPW',		'КПК Действия в аварийной обстановке (Вода)',	5,	null,	null,	53	,	null,	null),
('EPG',		'КПК Действия в аварийной обстановке (Суша)',	5,	null,	null,	54	,	null,	null),
('LC',		'Квалификационная проверка',					5,	null,	'73H',	46	,	null,	null),
('LC',		'Контрольно-проверочный полет',					5,	null,	'73H',	44	,	null,	null),
('RDG',		'Перевозка ОГ',									5,	null,	null,	73	,	null,	41),
---('PTT',	'Периодическая теоретическая подготовка',		    5,	null,	null,	76	,	null,	42),
('MED1CC',	'Справка МО (годовой)',							5,	null,	null,	30	,	null,	null),
('MED6CC',	'Справка МО (полугодичный)',					5,	null,	null,	37	,	null,	null),
('CRM',		'Человеческий фактор CRM',						5,	null,	null,	null,	null,	37)
--==============================================================================================================
--Select * From @CCT
--=======================================================================
--Select * From @TBL
--=======================================================================
--=============================================
--=============================================
INSERT INTO @ATT
Select 
TTB.TableNumber TableNumber,
TTB.Code Attribute_type,
TTB.Dev Srequired_equipment,
-------------------------------------------------------------------
UPPER(isnull(format(MAX(TTB.DateEnd) ,'ddMMMyyyy'),'')) Aexpiry_date,
MAX(isnull(TTB.CNT,0)) CNT
--UPPER(isnull(format(MAX(TTB.DateEnd1) ,'ddMMMyyyy'),'')) Aexpiry_date
FRom
(Select 
	PRR.TableNumber,
	--PRR.Pr_PersonnelID,
	CT.Code,
	isnull(CT.Device,'') Dev,
	----CASE
	----When isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd))  is not null then isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd))
	----Else @DTACT
	----END DateEnd,
	CASE
	When isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd))  is not null and COALESCE(EvgIN_CPO.CNT ,EvgIN_PS.CNT ,EvgPP_CPO.CNT ,EvgPP_PS.CNT, SerIN_CPO.CNT ,SerPP_CPO.CNT ,SerIN_PS.CNT  ,SerPP_PS.CNT, PPLIN_CPO.CNT)>0 
			then isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd))   -----=========================
	When isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd))  is not null and COALESCE(EvgIN_CPO.CNT ,EvgIN_PS.CNT ,EvgPP_CPO.CNT ,EvgPP_PS.CNT, SerIN_CPO.CNT ,SerPP_CPO.CNT ,SerIN_PS.CNT  ,SerPP_PS.CNT, PPLIN_CPO.CNT) is null
			then isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd))  --==============================
	Else isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd)) ---=================================================
	END DateEnd,
	CRT_E.Pr_EducationTypeID CRT_E,
	CRT_L.Pr_LimitTypeID CRT_L,
	CRT_D.Pr_DocumentTypeID CRT_D,
	COALESCE(EvgIN_CPO.CNT ,EvgIN_PS.CNT ,EvgPP_CPO.CNT ,EvgPP_PS.CNT, SerIN_CPO.CNT ,SerPP_CPO.CNT ,SerIN_PS.CNT  ,SerPP_PS.CNT,PPLIN_CPO.CNT) CNT  
	From (Select 
			PR.Pr_PersonnelID,
			replace(Pr.TableNumber,'/','00') TableNumber,
			Pr.Pr_CategoryTypeID,
			Case
			When STF.Code in ('NLS','CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD' ) then 'CP'  ---,'SI'
			When STF.Code in ('FO','2П', 'FOP','2П','2Пст') then 'FO'
			When STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
			When STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
			else''
			End  CatRole
			From Pr_Personnels PR
			OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
			LEft JOIN  Pr_StaffTrees STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
			left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
			left JOIN  Ap_AirPorts AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
			OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
			OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
			OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
			OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null))) CH 	
			Where 
			(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
			and isnumeric(replace(Pr.TableNumber,'/','00')) >''
			and (Pr.Pr_CategoryTypeID in (1,5))
			and CH.Pr_ArmChairTypeID  is not null
	) PRR
	LEFT JOIN  @CCT CT ON  CT.CatService=PRR.Pr_CategoryTypeID and ((CT.CatRole is null) or (CT.CatRole=PRR.CatRole))
	LEFT JOIN  Pr_Certifications CRT_L ON CRT_L.Pr_PersonnelID=PRR.Pr_PersonnelID and CRT_L.Pr_LimitTypeID=CT.LimitId
	LEFT JOIN  Pr_Certifications CRT_E ON CRT_E.Pr_PersonnelID=PRR.Pr_PersonnelID and CRT_E.Pr_EducationTypeID=CT.EducateId
	LEFT JOIN  Pr_Certifications CRT_D ON CRT_D.Pr_PersonnelID=PRR.Pr_PersonnelID and CRT_D.Pr_DocumentTypeID=CT.DocumentId
	--====================================================
----------TypeCrew	integer,
----------TypeSert	nvarchar(30),
----------CaryIN		integer,
----------PPeriod		integer,
----------CaryOUT		integer,
----------PastEvent	integer,
----------TypeArg     nvarchar(3),
----------Arg			nvarchar(30),
----------ArgCaryIn	integer,
----------ArgPPeriod	integer,
----------AddMonn		integer
	--=========================================CaryIN_CPO===========================
	OUTER APPLY (Select TOP 1 case when TT.AddMonn is not null then TT.AddMonn else 0 end CNT From  Pr_Events EV 
				INNER JOIN Pr_EventTypes TP1 on TP1.Pr_EventTypeID=EV.Pr_EventTypeID
				INNER JOIN @TBL TT on TT.Arg=TP1.CodeEng and TT.TypeArg='Ev' and TT.ArgCaryIn=1 
				Where 
				TT.TypeSert=CT.Code ----- отбор по типу сертификата
				and COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd) between   @DTACT and @DTACT2 --CPO ---даты сертификата в диапазоне
				and EV.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				and Ev.DateBegin between @DTACT and @DTbegin --caryIn  событие связанное с сертификатом в диапазоне
				) EvgIN_CPO
	--=========================================CaryIN_PS===========================
	OUTER APPLY (Select TOP 1 case when TT.AddMonn is not null then TT.AddMonn else 0 end CNT From  Pr_Events EV 
				INNER JOIN Pr_EventTypes TP1 on TP1.Pr_EventTypeID=EV.Pr_EventTypeID
				INNER JOIN @TBL TT on TT.Arg=TP1.CodeEng and TT.TypeArg='Ev' and TT.ArgCaryIn=1
				Where 
				TT.TypeSert=CT.Code 
				and COALESCE(CRT_E.DateEnd,CRT_L.DateEnd,CRT_D.DateEnd) > Ev.DateBegin  ---Past
				and COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd) between   @DTACT and @DTACT2 --CPO
				and EV.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				and Ev.DateBegin between @DTACT and @DTbegin --caryIn
				) EvgIN_PS
	--=========================================CaryPP_CPO===========================
	OUTER APPLY (Select TOP 1 case when TT.AddMonn is not null then TT.AddMonn else 0 end CNT From  Pr_Events EV 
				INNER JOIN Pr_EventTypes TP1 on TP1.Pr_EventTypeID=EV.Pr_EventTypeID
				INNER JOIN @TBL TT on TT.Arg=TP1.CodeEng and TT.TypeArg='Ev' and TT.ArgPPeriod=1
				Where 
				TT.TypeSert=CT.Code 
				and COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd) between   @DTACT and @DTACT2 --CPO
				and EV.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				and Ev.DateBegin between @DTbegin and @DTEnd --PlanPeriod
				) EvgPP_CPO
	--=========================================CaryPP_PS===========================
	OUTER APPLY (Select TOP 1 case when TT.AddMonn is not null then TT.AddMonn else 0 end CNT From  Pr_Events EV 
				INNER JOIN Pr_EventTypes TP1 on TP1.Pr_EventTypeID=EV.Pr_EventTypeID
				INNER JOIN @TBL TT on TT.Arg=TP1.CodeEng and TT.TypeArg='Ev' and TT.ArgPPeriod=1
				Where 
				TT.TypeSert=CT.Code 
				and COALESCE(CRT_E.DateEnd,CRT_L.DateEnd,CRT_D.DateEnd) > Ev.DateBegin ---Past
				and COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd) between   @DTACT and @DTACT2 --CPO
				and EV.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				and Ev.DateBegin between @DTbegin and @DTEnd --PlanPeriod
				) EvgPP_PS
	--=========================================SerIN_CPO==============================================     по дате сертификата
	OUTER APPLY (Select TOP 1 case when TT.AddMonn is not null then TT.AddMonn else 0 end CNT From Pr_Personnels P1
					INNER JOIN Pr_Certifications CRT1 ON CRT1.Pr_PersonnelID=P1.Pr_PersonnelID 
					LEFT JOIN  Pr_LimitTypes     Lim ON Lim.Pr_LimitTypeID=Crt1.Pr_LimitTypeID
					LEFT JOIN  Pr_EducationTypes Edu ON Edu.Pr_EducationTypeID=Crt1.Pr_EducationTypeID
					LEFT JOIN  Pr_DocumentTypes  Doc ON Doc.Pr_DocumentTypeID=Crt1.Pr_DocumentTypeID
					INNER JOIN @TBL TT on TT.Arg=COALESCE(Lim.CodeEng,Edu.CodeEng,Doc.CodeEng) and TT.TypeArg='TS' and TT.ArgCaryIn=1 
				Where 
				TT.TypeSert=CT.Code 
				and COALESCE(CRT_E.DateEnd,CRT_L.DateEnd,CRT_D.DateEnd) between   @DTACT and @DTACT2 --CPO
				and P1.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				and CRT1.DateBegin between @DTACT and @DTbegin --caryIn
				) SerIN_CPO
	--=========================================SerPP_CPO==============================================
	OUTER APPLY (Select TOP 1 case when TT.AddMonn is not null then TT.AddMonn else 0 end CNT From Pr_Personnels P1
					INNER JOIN Pr_Certifications CRT1 ON CRT1.Pr_PersonnelID=P1.Pr_PersonnelID 
					LEFT JOIN  Pr_LimitTypes     Lim ON Lim.Pr_LimitTypeID=Crt1.Pr_LimitTypeID
					LEFT JOIN  Pr_EducationTypes Edu ON Edu.Pr_EducationTypeID=Crt1.Pr_EducationTypeID
					LEFT JOIN  Pr_DocumentTypes  Doc ON Doc.Pr_DocumentTypeID=Crt1.Pr_DocumentTypeID
					INNER JOIN @TBL TT on TT.Arg=COALESCE(Lim.CodeEng,Edu.CodeEng,Doc.CodeEng) and TT.TypeArg='TS' and TT.ArgPPeriod=1 
				Where 
				TT.TypeSert=CT.Code 
				and COALESCE(CRT_E.DateEnd,CRT_L.DateEnd,CRT_D.DateEnd) between   @DTACT and @DTACT2 --CPO
				and P1.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				--and CRT1.DateBegin between @DTACT and @DTbegin --caryIn
				and CRT1.DateBegin between @DTbegin and @DTEnd --PlanPeriod
				) SerPP_CPO
	--=========================================SerIN_PS==============================================
	OUTER APPLY (Select TOP 1 case when TT.AddMonn is not null then TT.AddMonn else 0 end CNT From Pr_Personnels P1
					INNER JOIN Pr_Certifications CRT1 ON CRT1.Pr_PersonnelID=P1.Pr_PersonnelID 
					LEFT JOIN  Pr_LimitTypes     Lim ON Lim.Pr_LimitTypeID=Crt1.Pr_LimitTypeID
					LEFT JOIN  Pr_EducationTypes Edu ON Edu.Pr_EducationTypeID=Crt1.Pr_EducationTypeID
					LEFT JOIN  Pr_DocumentTypes  Doc ON Doc.Pr_DocumentTypeID=Crt1.Pr_DocumentTypeID
					INNER JOIN @TBL TT on TT.Arg=COALESCE(Lim.CodeEng,Edu.CodeEng,Doc.CodeEng) and TT.TypeArg='TS' and TT.ArgCaryIn=1 
				Where 
				TT.TypeSert=CT.Code 
				and COALESCE(CRT_E.DateEnd,CRT_L.DateEnd,CRT_D.DateEnd) > CRT1.DateBegin ---Past
				and COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd) between   @DTACT and @DTACT2 --CPO
				and P1.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				and CRT1.DateBegin between @DTACT and @DTbegin --caryIn
				) SerIN_PS
	--=========================================SerPP_PS==============================================
	OUTER APPLY (Select TOP 1 case when TT.AddMonn is not null then TT.AddMonn else 0 end CNT From Pr_Personnels P1
					INNER JOIN Pr_Certifications CRT1 ON CRT1.Pr_PersonnelID=P1.Pr_PersonnelID 
					LEFT JOIN  Pr_LimitTypes     Lim ON Lim.Pr_LimitTypeID=Crt1.Pr_LimitTypeID
					LEFT JOIN  Pr_EducationTypes Edu ON Edu.Pr_EducationTypeID=Crt1.Pr_EducationTypeID
					LEFT JOIN  Pr_DocumentTypes  Doc ON Doc.Pr_DocumentTypeID=Crt1.Pr_DocumentTypeID
					INNER JOIN @TBL TT on TT.Arg=COALESCE(Lim.CodeEng,Edu.CodeEng,Doc.CodeEng) and TT.TypeArg='TS' and TT.ArgPPeriod=1 
				Where 
				TT.TypeSert=CT.Code 
				and COALESCE(CRT_E.DateEnd,CRT_L.DateEnd,CRT_D.DateEnd) > CRT1.DateBegin ---Past
				and COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd) between   @DTACT and @DTACT2 --CPO
				and P1.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				--and CRT1.DateBegin between @DTACT and @DTbegin --caryIn
				and CRT1.DateBegin between @DTbegin and @DTEnd --PlanPeriod
				) SerPP_PS
		--====================================PplsPP_IN===============================================================
	OUTER APPLY (Select TOP 1 
					case when TT.AddMonn is not null then isnull(datediff(m,COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd),PFT.DateTakeoff),0)+TT.AddMonn+1 else 0 end CNT 
					From Ap_PlanFlightAirPortCrews CR
					INNER JOIN AP_PlanFlightAirPorts PFT on PFT.Ap_PlanFlightAirPortID=CR.Ap_PlanFlightAirPortID
					INNER JOIN Ak_PPLSTaskTypes PP ON PP.Ak_PPLSTaskTypeID=CR.Ak_PPLSTaskTypeID
					INNER JOIN @TBL TT on TT.Arg=PP.Comment and TT.TypeArg='PP' and TT.ArgCaryIn=1 
				Where
				TT.TypeSert=CT.Code 
				and COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd) < @DTbegin --CPO
				and CR.Pr_PersonnelID=PRR.Pr_PersonnelID
				and PRR.Pr_CategoryTypeID = TT.TypeCrew
				and PFT.DateTakeoff  between @DTACT and @DTbegin --caryIn
				Order by COALESCE(CRT_E.DateEnd  ,CRT_L.DateEnd,CRT_D.DateEnd) DESC
				) PPLIN_CPO
Where
	CASE
	When isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd)) is not null then isnull(CRT_L.DateEnd,isnull(CRT_E.DateEnd,CRT_D.DateEnd))
	Else @DTACT
	END  is not null
) TTB
GROUP BY
TTB.TableNumber,
TTB.Code,
TTB.Dev
ORDER BY TableNumber
--======================================
---Select * From  @ATT
--===========================================
INSERT INTO @TT
VALUES('','','','',0,@CC)
INSERT INTO @TT
Select 
	T.TableNumber,
	T.Attribute_type , 
	T.Srequired_equipment ,
	T.Aexpiry_date,
	T.CNT,
	'"'+T.TableNumber+'", '+
	'"'+T.attribute_type   +'", '+
	'"'+T.Srequired_equipment   +'", '+
		T.Aexpiry_date  +';'
From @ATT T
Where T.Aexpiry_date <>''
Order by TableNumber, attribute_type
--==========================================
Select t.* FROM @TT T
--=============================================
--=============================================
END
			