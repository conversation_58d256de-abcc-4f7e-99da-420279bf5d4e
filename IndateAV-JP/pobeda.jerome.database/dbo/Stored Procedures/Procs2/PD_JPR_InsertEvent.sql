﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_InsertEvent]
				@STRXML nvarchar(MAX)
AS
BEGIN
--=======================================================================================================
DECLARE @TBL Table
(
NameTrening nvarchar(3000),
<PERSON>IO nvarchar(3000),
<PERSON><PERSON> nvarchar(10),
DTBeg Datetime,
DTEnd Datetime,
Comment nvarchar(2000)
)

DECLARE @xml xml
SELECT @xml = CAST(CAST(@STRXML AS VARBINARY(MAX)) AS XML) 

INSERT INTO @TBL
SELECT 
	x.Rec.query('./Вид_x0020_тренировки').value('.', 'nvarchar(300)') AS 'NameTrening',
	x.Rec.query('./Ф_x0023_И_x0023_О').value('.', 'nvarchar(300)') AS 'FIO',
	x.Rec.query('./Табельный_x0020_номер').value('.', 'nvarchar(10)') AS 'Tabel',
	x.Rec.query('./Дата_x0020_и_x0020_время_x0020_начала').value('.', 'Datetime') AS 'DTBegin',
	x.Rec.query('./Дата_x0020_и_x0020_время_x0020_окончания').value('.', 'Datetime') AS 'DTEnd',
	x.Rec.query('./Доп_x0023_сведения').value('.', 'nvarchar(2000)') AS 'Comment'
FROM @xml.nodes('/Table') as x(Rec)

--========================================================================

INSERT INTO Pr_Events
(
	[Pr_EventTypeID],
	[Pr_PersonnelID],
	[DateBegin],
	[DateEnd],
	[Comment],
	[Executor],
	[Inspector],
	[Amount]
)
Select
EVT.Pr_EventTypeID,
PR.Pr_PersonnelID,
TT.DTB,
TT.DTE,
TT.Comment,
259,
259,
0 
From 
	(SELECT 
	T.Tabel,
	T.FIO,
	T.NameTrening,
	'@AVT  '+T.Comment Comment,
	Convert(smalldatetime,dateadd(hh,+3,t.DTBeg)) DTB,
	Convert(smalldatetime,dateadd(hh,+3,t.DTEnd)) DTE
	FROM @TBL T
	) TT
INNER JOIN Pr_Personnels PR ON PR.TableNumber=TT.Tabel
INNER JOIN Pr_EventTypes EVT ON rtrim(ltrim(EVT.Name))=rtrim(ltrim(TT.NameTrening))
LEFT JOIN Pr_Events EV ON 
						EV.Pr_PersonnelID=PR.Pr_PersonnelID
					and EVT.Pr_EventTypeID=EV.Pr_EventTypeID
					and EV.DateBegin=TT.DTB
					and EV.DateEnd=TT.DTE 
Where EV.Pr_EventID is null
--========================================================================
END
