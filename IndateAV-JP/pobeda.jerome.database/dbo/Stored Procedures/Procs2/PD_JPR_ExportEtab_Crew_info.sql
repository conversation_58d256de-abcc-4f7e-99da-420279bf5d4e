﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Crew_info]
---Declare
@DTbegin datetime='2019-10-01 00:00:00',  
@DTEnd datetime='2019-10-31 23:59:00'

AS
BEGIN
--=======================================================================
--  crew_info
--======================================================================
--crew_id	String	Crew ID	 	A	Табельный номер сотрудника
--Gender	Picklist	Gender	[ ‘M’ ; ‘F’ ]	A	Признак: мужской пол, значение: MПризнак: женский пол, значение: F
--Birthdate	AbsTime	Birth date	 	A	Дата рождения
--Surname	String	Surname	 	A	Фамилия
--FirstName	String	First name	 	A	Имя
--Signature	String	Signature	 	A	Значение, что видят планировщики в колонке слева в Studio. Чем короче, тем лучше, и это должно быть уникально. Необязательное поле.Предлагаемое значение: первая буква фамилия + первая буква имени + первая буква отчества
--MainCat	String	Main category	 	A	Признак: принадлежность сотрудника к летным экипажам, значение: FПризнак: принадлежность сотрудника к кабинным экипажам, значение: С
--Seniority	Integer	Seniority	 	A/M	Значение, позволяющее устанавливать различные Bid Target для групп сотрудников, объединенных общим значением. Чем менбше значение, тем более "старшим" считается сотрудник
--employment_date	AbsTime	Employment date	 	A	Дата приема на работу
--resignation_date	AbsTime	Resignation date	 	A	Дата увольнения
--MainFunc	String	Main func	 	A	Соответствует Rank
--SndFunc	String	Snd func	 	A	Соответствует Rank
--=======================================================================


--=====================Вычисление Рейтинга БП===================================================
Declare
@DTUTCBegin datetime=  EOMONTH (@DTbegin,-7),
@DTUTCBegin2 datetime=EOMONTH (@DTbegin,-3),
@DTUTCEnd datetime=EOMONTH (@DTbegin,-1)

Select 
TT.PersonnelID,
TT.AvgCrewMark,
ISNULL(HSP6.nDay6,0) nDay6,
ISNULL(HSP2.nDay2,0) nDay2,
case
When TT.AvgCrewMark >= 4.7 and ISNULL(HSP2.nDay2,0)=0 then 1
When TT.AvgCrewMark between 4.60 and 4.699  then 2 ---and ISNULL(HSP2.nDay2,0)=0 
When TT.AvgCrewMark between 4.55 and 4.599  then 3 ---and ISNULL(HSP2.nDay2,0)=0
else 4
end RTG
INTO #TBL
From [REPORTSDB1].[REPORTS].[dbo].[CrewMark] TT 
OUTER APPLY ( Select
				SUM(Case
					When convert(date,dateadd(hh,0,EV.DateBegin)) between convert(date,@DTUTCBegin)  and  convert(date,@DTUTCEnd) 
						and convert(date,dateadd(hh,0,EV.DateFinish))   between convert(date,@DTUTCBegin)  and  convert(date,@DTUTCEnd)
						then ISNULL(DateDiff(dd,convert(date,dateadd(hh,0,EV.DateBegin)),convert(date,dateadd(hh,0,EV.DateFinish)))+1,0)

					When convert(date,dateadd(hh,0,EV.DateBegin)) between convert(date,@DTUTCBegin)  and  convert(date,@DTUTCEnd) 
						and convert(date,dateadd(hh,0,EV.DateFinish)) >= convert(date,@DTUTCEnd)  --пересечение справа
						then ISNULL(DateDiff(dd,convert(date,dateadd(hh,0,EV.DateBegin)),convert(date,@DTUTCEnd))+1,0)

					When convert(date,dateadd(hh,0,EV.DateBegin)) <= convert(date,@DTUTCBegin)   
						and convert(date,dateadd(hh,0,EV.DateFinish)) between convert(date,@DTUTCBegin)  and  convert(date,@DTUTCEnd) --пересечение слева
						then ISNULL(DateDiff(dd,convert(date,dateadd(hh,0,@DTUTCBegin)),convert(date,dateadd(hh,0,EV.DateFinish)))+1,0)

					When convert(date,dateadd(hh,0,EV.DateBegin)) < convert(date,@DTUTCBegin) 
						and convert(date,dateadd(hh,0,EV.DateFinish))   > convert(date,@DTUTCBegin) --пересечение слева и справа
						then isnull(DateDiff(dd,convert(date,@DTUTCBegin),convert(date,@DTUTCBegin))+1,0) 
				 end) nDay6
				FROM Pr_Orders  EV 			
				inner join	Pr_OrderTypes RTP	on RTP.Pr_OrderTypeID =EV.OrderType
				inner join Pr_Personnels  PR	on PR.Pr_PersonnelID=EV.Pr_PersonnelID
				LEFT  Join Pr_RestTypes  RR	ON RR.Pr_RestTypeID = EV.Pr_RestTypeID				
				Where 
				EV.OrderType in  (6)
				and 
				(
					(convert(date,dateadd(hh,0,EV.DateBegin)) between convert(date,@DTUTCBegin)  and  convert(date,@DTUTCEnd) and convert(date,dateadd(hh,0,EV.DateFinish)) between convert(date,@DTUTCBegin)  and  convert(date,@DTUTCEnd))
					or 
					(convert(date,dateadd(hh,0,EV.DateBegin)) between convert(date,@DTUTCBegin)  and  convert(date,@DTUTCEnd) and convert(date,dateadd(hh,0,EV.DateFinish)) >= convert(date,@DTUTCEnd)) --пересечение справа
					or
					(convert(date,dateadd(hh,0,EV.DateBegin)) <= convert(date,@DTUTCBegin)   and convert(date,dateadd(hh,0,EV.DateFinish)) between convert(date,@DTUTCBegin)  and  convert(date,@DTUTCEnd)) --пересечение слева
					or
					(convert(date,dateadd(hh,0,EV.DateBegin)) < convert(date,@DTUTCBegin) and convert(date,dateadd(hh,0,EV.DateFinish)) > convert(date,@DTUTCBegin)) --пересечение слева и справа
				) 
				and PR.Pr_PersonnelID=TT.PersonnelID
			) HSP6
OUTER APPLY (SELECT  
				SUM(Case
					When convert(date,dateadd(hh,0,EV.DateBegin)) between convert(date,@DTUTCBegin2)  and  convert(date,@DTUTCEnd) 
						and convert(date,dateadd(hh,0,EV.DateFinish))   between convert(date,@DTUTCBegin2)  and  convert(date,@DTUTCEnd)
						then ISNULL(DateDiff(dd,convert(date,dateadd(hh,0,EV.DateBegin)),convert(date,dateadd(hh,0,EV.DateFinish)))+1,0)

					When convert(date,dateadd(hh,0,EV.DateBegin)) between convert(date,@DTUTCBegin2)  and  convert(date,@DTUTCEnd) 
						and convert(date,dateadd(hh,0,EV.DateFinish)) >= convert(date,@DTUTCEnd)  --пересечение справа
						then ISNULL(DateDiff(dd,convert(date,dateadd(hh,0,EV.DateBegin)),convert(date,@DTUTCEnd))+1,0)

					When convert(date,dateadd(hh,0,EV.DateBegin)) <= convert(date,@DTUTCBegin2)   
						and convert(date,dateadd(hh,0,EV.DateFinish)) between convert(date,@DTUTCBegin2)  and  convert(date,@DTUTCEnd) --пересечение слева
						then ISNULL(DateDiff(dd,convert(date,dateadd(hh,0,@DTUTCBegin2)),convert(date,dateadd(hh,0,EV.DateFinish)))+1,0)

					When convert(date,dateadd(hh,0,EV.DateBegin)) < convert(date,@DTUTCBegin2) 
						and convert(date,dateadd(hh,0,EV.DateFinish))   > convert(date,@DTUTCBegin2) --пересечение слева и справа
						then isnull(DateDiff(dd,convert(date,@DTUTCBegin2),convert(date,@DTUTCBegin))+1,0) 
				end) nDay2

				FROM Pr_Orders  EV --RD Pr_Events				
				inner join	Pr_OrderTypes RTP	on RTP.Pr_OrderTypeID =EV.OrderType
				inner join Pr_Personnels  PR	on PR.Pr_PersonnelID=EV.Pr_PersonnelID
				LEFT  Join Pr_RestTypes  RR	ON RR.Pr_RestTypeID = EV.Pr_RestTypeID
				
				Where 
				(EV.OrderType in  (6))
				and 
				(
					(convert(date,dateadd(hh,0,EV.DateBegin)) between convert(date,@DTUTCBegin2)  and  convert(date,@DTUTCEnd) and convert(date,dateadd(hh,0,EV.DateFinish)) between convert(date,@DTUTCBegin2)  and  convert(date,@DTUTCEnd))
					or 
					(convert(date,dateadd(hh,0,EV.DateBegin)) between convert(date,@DTUTCBegin2)  and  convert(date,@DTUTCEnd) and convert(date,dateadd(hh,0,EV.DateFinish)) >= convert(date,@DTUTCEnd)) --пересечение справа
					or
					(convert(date,dateadd(hh,0,EV.DateBegin)) <= convert(date,@DTUTCBegin2)   and convert(date,dateadd(hh,0,EV.DateFinish)) between convert(date,@DTUTCBegin2)  and  convert(date,@DTUTCEnd)) --пересечение слева
					or
					(convert(date,dateadd(hh,0,EV.DateBegin)) < convert(date,@DTUTCBegin2) and convert(date,dateadd(hh,0,EV.DateFinish)) > convert(date,@DTUTCBegin2)) --пересечение слева и справа
				) 
				and  PR.Pr_PersonnelID=TT.PersonnelID
			) HSP2
Order by AvgCrewMark DESC

----Select * From #TBL
---==================================================================
--===================================================================
Declare @CC nvarchar(max)=
'12
SCrewId "Crew ID" ?"Unique crew identifier",
EGender "Gender" [ "M" ; "F" ],
ABirthdate "Birth date",
SSurname "Surname",
SFirstName "First name",
SSignature "Signature" ?"A unique string that identifies the specific crew member for the planners",
SMainCat "Main category" ?"The crew category to which this crew member belongs, F for Flight Deck or C for Cabin",
ISeniority "Seniority" ?"Positive integer where lower equals more senior",
Aemployment_date "Employment date",
Aresignation_date "Resignation date",
SMainFunc "Main func" ?"Primary rank of the crew member",
SSndFunc "Snd func" ?"Not used, needed for backwards compatibility",
'
Declare
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))

DECLARE @TT table
(
TableNumber nvarchar(30), 
Gender nvarchar(30),  
Birthdate nvarchar(30), 
Surname nvarchar(30), 
FirstName nvarchar(30), 
Signature nvarchar(70), 
MainCat nvarchar(30), 
Seniority nvarchar(30), 
employment_date nvarchar(30), 
resignation_date nvarchar(30), 
MainFunc nvarchar(30), 
SndFunc nvarchar(30), 	
STRLN nvarchar(max)
)

Declare
@DTending nvarchar(10)='30DEC2075'

--=============================================================
--=============================================================
INSERT  INTO @TT
VALUES('','','','','','','','','','','','',@CC)
INSERT  INTO @TT
Select 
	TableNumber, 
	--DateBegin,
	Gender, 
	Birthdate, 
	Surname, 
	FirstName, 
	Signature, 
	MainCat, 
	Seniority,
	employment_date, 
	resignation_date,
	MainFunc,  
	SndFunc,	
------------------------------------
	'"'+TableNumber +'", '+
	'"'+Gender  +'", '+
	Birthdate  +', '+
	'"'+Surname +'", '+ 
	'"'+FirstName +'", '+ 
	'"'+Signature+'", '+  
	'"'+MainCat+'", '+  
	Seniority  +', '+ 
	employment_date  +', '+
	resignation_date  +', '+
	'"'+MainFunc +'", '+ 
	'"'+SndFunc +'";'
------------------------------- 	
From (	
		Select 
		---------------------------------------------------------------------------------
		replace(Pr.TableNumber,'/','00')  TableNumber,
		--PST.DateBegin,
		-------------------------------------
		case 
		when PR.Sex in ('М','M') then 'M'
		when PR.Sex in ('Ж', 'F') then 'F'
		else ''
		end Gender,
		----------------------------------------
		UPPER(isnull(format(PR.BirthDay,'ddMMMyyyy'),'')) Birthdate,
		--------------------------------------
		PR.LastNameEng Surname,
		PR.FirstNameEng FirstName,
		PR.LastNameEng +' '+ Substring(PR.FirstNameEng,1,1)+'. ' + Substring(PR.LastNameEng,1,1)+'.' Signature,
		---------------------------------------------------------------------------------------------------
		Case
		when PR.Pr_CategoryTypeID =1 then 'F'
		when PR.Pr_CategoryTypeID =5 then 'C'
		else ''
		end MainCat,
		Convert(nvarchar,
		Case
		When PR.Pr_CategoryTypeID=1 then
			Case
			When abs(datediff(dd, ORD1.DateBegin, getdate()))> 4*365 then 1
			When abs(datediff(dd, ORD1.DateBegin, getdate()))between 2*365 and 4*365 then 2
			When abs(datediff(dd, ORD1.DateBegin, getdate()))between 1*365 and 3*365-1 then 3
			else 4
			End
		When PR.Pr_CategoryTypeID=5 then ISNULL(RR.RTG,4)
		else 4
		End
		) Seniority,
		---'1' 
		----------------------------------------------------------------------------------------
		UPPER(isnull(format(ORD1.DateBegin  ,'ddMMMyyyy'),'')) employment_date,
		CASE
		WHEN isnull(PST.DateBegin,ORD1.DateBegin) <  isnull(PST.DateEnd,ORD2.DateBegin) then UPPER(isnull(format(isnull(PST.DateEnd,ORD2.DateBegin),'ddMMMyyyy'),@DTending)) 
		WHEN isnull(PST.DateBegin,ORD1.DateBegin) >= isnull(PST.DateEnd,ORD2.DateBegin) then UPPER(@DTending) 
		else UPPER(isnull(format(isnull(PST.DateEnd,ORD2.DateBegin),'ddMMMyyyy'),@DTending)) 
		END resignation_date,
		----------------------------------------------------------------------------------------
		--'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD'
		--'2П','2Пст'
		--==============================================================
		--'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП'
		--'ПБ','ПБст'
		Case
			When PR.Pr_CategoryTypeID=1 and STF.Code in ('NLS','CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD' ) then 'CP'  ---,'SI'
			When PR.Pr_CategoryTypeID=1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст','2Пу','FOT','FOTR') then 'FO'
			When PR.Pr_CategoryTypeID=5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
			When PR.Pr_CategoryTypeID=5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
			else''
		End  MainFunc,
		-----------------------------------------------------------------------------------------
		Case
			When PR.Pr_CategoryTypeID=1 and STF.Code in ('NLS','CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'CPP','КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD') then 'CP' ---,'SI'
			When PR.Pr_CategoryTypeID=1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст','2Пу','FOT','FOTR') then 'FO'
			When PR.Pr_CategoryTypeID=5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
			When PR.Pr_CategoryTypeID=5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
			else''
		End  SndFunc
		----------------------------------------------------------------------------------------
		From Pr_Personnels PR
		OUTER APPLY (select top 1 
							* 
						From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
						LEft JOIN Pr_StaffTrees STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
						left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
						left JOIN  Ap_AirPorts AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
		--------------Seniority---------------------
						left JOIN  #TBL RR on RR.PersonnelID=PR.Pr_PersonnelID
		--------------------------------------------
		OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=Pr.Pr_PersonnelID and Pr_Orders.OrderType in (1,2)  order by Pr_Orders.Datebegin asc) ORD1 --and (@DTACT2>= Pr_Orders.DateBegin)
		OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=Pr.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
		------------------------------------------
		OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
		OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null))) CH 
		Where 
		(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
		and isnumeric(replace(Pr.TableNumber,'/','00')) >''
		and (Pr.Pr_CategoryTypeID in (1,5))
		and CH.Pr_ArmChairTypeID  is not null
	) TT
	where TT.MainFunc>''
--=====================================
Select 
	TableNumber, 
	Gender, 
	Birthdate, 
	Surname, 
	FirstName, 
	Signature, 
	MainCat, 
	Seniority,
	employment_date, 
	resignation_date,
	MainFunc,  
	SndFunc,	
	STRLN
 From @TT order by resignation_date,TableNumber

 Drop table #TBL
End