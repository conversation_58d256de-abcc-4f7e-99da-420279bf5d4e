﻿ ----=============================================
 ----Author:		Коротеев
 ----Create date: <Create Date,,>
 ----Description:	<Description,,>
 ----=============================================
CREATE  PROCEDURE [dbo].[PD_JPR_InsertAssignment]
				@STRXML NVARCHAR(MAX)='',
				@TYPECREW integer = 1,		--0 пилоты 1 --БП --11 все
				@REM nvarchar(100) =''

AS
BEGIN
------Declare
------@STRXML NVARCHAR(MAX)='<Flights><Id_Flight>4169</Id_Flight><NumberFlight>305</NumberFlight><CarGo>DP</CarGo><APTKF>LED</APTKF><APLND>AER</APLND><DataFlight>2019-03-31T00:00:00Z</DataFlight><STD>2019-03-31T12:15:00Z</STD><STA>2019-03-31T15:25:00Z</STA><TypeFlight>L</TypeFlight><IdParing>11103787127154</IdParing><IdTask>3098</IdTask><TPCrew>0/0/0/0/0/1/0</TPCrew><Tabel>1271</Tabel><PPLS></PPLS></Flights><Flights><Id_Flight>4170</Id_Flight><NumberFlight>118</NumberFlight><CarGo>DP</CarGo><APTKF>AER</APTKF><APLND>VKO</APLND><DataFlight>2019-03-31T00:00:00Z</DataFlight><STD>2019-03-31T16:10:00Z</STD><STA>2019-03-31T18:25:00Z</STA><TypeFlight>L</TypeFlight><IdParing>11103787127154</IdParing><IdTask>3098</IdTask><TPCrew>0/0/0/0/0/1/0</TPCrew><Tabel>1271</Tabel><PPLS></PPLS></Flights><Flights><Id_Flight>4171</Id_Flight><NumberFlight>883</NumberFlight><CarGo>DP</CarGo><APTKF>VKO</APTKF><APLND>SAW</APLND><DataFlight>2019-04-01T00:00:00Z</DataFlight><STD>2019-04-01T06:25:00Z</STD><STA>2019-04-01T10:00:00Z</STA><TypeFlight>L</TypeFlight><IdParing>11103787127154</IdParing><IdTask>3100</IdTask><TPCrew>0/0/0/0/0/1/0</TPCrew><Tabel>1271</Tabel><PPLS></PPLS></Flights><Flights><Id_Flight>4172</Id_Flight><NumberFlight>884</NumberFlight><CarGo>DP</CarGo><APTKF>SAW</APTKF><APLND>VKO</APLND><DataFlight>2019-04-01T00:00:00Z</DataFlight><STD>2019-04-01T10:50:00Z</STD><STA>2019-04-01T14:15:00Z</STA><TypeFlight>L</TypeFlight><IdParing>11103787127154</IdParing><IdTask>3100</IdTask><TPCrew>0/0/0/0/0/1/0</TPCrew><Tabel>1271</Tabel><PPLS></PPLS></Flights><Flights><Id_Flight>4173</Id_Flight><NumberFlight>205</NumberFlight><CarGo>DP</CarGo><APTKF>VKO</APTKF><APLND>LED</APLND><DataFlight>2019-04-02T00:00:00Z</DataFlight><STD>2019-04-02T06:00:00Z</STD><STA>2019-04-02T07:25:00Z</STA><TypeFlight>L</TypeFlight><IdParing>11103787127154</IdParing><IdTask>3102</IdTask><TPCrew>0/0/0/0/0/1/0</TPCrew><Tabel>1271</Tabel><PPLS></PPLS></Flights><Flights><Id_Flight>4174</Id_Flight><NumberFlight>507</NumberFlight><CarGo>DP</CarGo><APTKF>LED</APTKF><APLND>OGZ</APLND><DataFlight>2019-04-02T00:00:00Z</DataFlight><STD>2019-04-02T08:10:00Z</STD><STA>2019-04-02T11:10:00Z</STA><TypeFlight>L</TypeFlight><IdParing>11103787127154</IdParing><IdTask>3102</IdTask><TPCrew>0/0/0/0/0/1/0</TPCrew><Tabel>1271</Tabel><PPLS></PPLS></Flights><Flights><Id_Flight>4175</Id_Flight><NumberFlight>508</NumberFlight><CarGo>DP</CarGo><APTKF>OGZ</APTKF><APLND>LED</APLND><DataFlight>2019-04-02T00:00:00Z</DataFlight><STD>2019-04-02T11:50:00Z</STD><STA>2019-04-02T15:10:00Z</STA><TypeFlight>L</TypeFlight><IdParing>11103787127154</IdParing><IdTask>3102</IdTask><TPCrew>0/0/0/0/0/1/0</TPCrew><Tabel>1271</Tabel><PPLS></PPLS></Flights>'
------,@TYPECREW integer = 1		--0 пилоты 1 --БП --11 все
------=======================================================
Declare @ChainPlnID bigint

DECLARE @TTT Table
(
NumberFlight nvarchar(10) null,
CarGo nvarchar(5)  null,
AP_TKF nvarchar(5) null,
AP_LND nvarchar(5) null,
DataFlight DateTime null,
STD DateTime null,
STA DateTime null,
IdParing bigint null,
IdTask bigint null,
TypeFlight nvarchar(2) null,
TPCrew nvarchar(15) null,
IDTaskCP bigint null,
Tabel nvarchar(15) null,
PPLS nvarchar(50) null
)

DECLARE @TBL Table
(
NumberFlight nvarchar(10) null,
CarGo nvarchar(5)  null,
AP_TKF nvarchar(5) null,
AP_LND nvarchar(5) null,
DataFlight DateTime null,
STD DateTime null,
STA DateTime null,
IdParing bigint null,
IdTask bigint null,
TypeFlight nvarchar(2) null,
TPCrew nvarchar(15) null,
IDTaskCP bigint null,
Tabel nvarchar(15) null,
PPLS nvarchar(50) null,
------------------------------------
Pr_ArmChairTypeID integer,
NOrder integer,
TypeCrew integer,
----------------------------------
Ap_PlanFlightID integer,
Fl_FlightID integer,
Pr_PersonnelID integer,
Ap_PlanFlightAirPortID_TO integer,
Ap_PlanFlightAirPortID_LA integer,
Pr_FlightTaskReestrID integer,
Ap_PlanFlightAirPortCrewID integer,
APTO integer,
APLA integer,
Ak_PPLSTaskTypeID integer,
Pr_CategoryTypeID integer
)

--================================================================
DECLARE @xml xml
SELECT @xml = CAST(CAST(@STRXML AS VARBINARY(MAX)) AS XML) 
DECLARE @AP integer
DEclare @SHT integer
--================================================================
INSERT INTO @TTT
SELECT 
    x.Rec.query('./NumberFlight').value('.', 'nvarchar(10)') AS 'NumberFlight',
	x.Rec.query('./CarGo').value('.', 'nvarchar(5)') AS 'CarGo',
	x.Rec.query('./APTKF').value('.', 'nvarchar(5)') AS 'AP_TKF',
	x.Rec.query('./APLND').value('.', 'nvarchar(5)') AS 'AP_LND',
    x.Rec.query('./DataFlight').value('.', 'DateTime') AS 'DataFlight',
	x.Rec.query('./STD').value('.', 'DateTime') AS 'STD',
	x.Rec.query('./STA').value('.', 'DateTime') AS 'STA',
	x.Rec.query('./IdParing').value('.', 'bigint') AS 'IdParing',
	x.Rec.query('./IdTask').value('.', 'bigint') AS 'IdTask',
	x.Rec.query('./TypeFlight').value('.', 'nvarchar(2)') AS 'TypeFlight',
	x.Rec.query('./TPCrew').value('.', 'nvarchar(15)') AS 'TPCrew',
	x.Rec.query('./IDTaskCP').value('.', 'bigint') AS 'IDTaskCP',
	x.Rec.query('./Tabel').value('.', 'nvarchar(10)') AS 'Tabel',
	x.Rec.query('./PPLS').value('.', 'nvarchar(50)') AS 'PPLS'

FROM @xml.nodes('/Flights') as x(Rec)
--===============================================
--select * From @TTT
--===============================================
--===============================================
BEGIN TRANSACTION	
UPDATE @TTT
Set
NumberFlight=
CASE 
When TT.NumberFlight='1' then '001' 
When TT.NumberFlight='2' then '002' 
When TT.NumberFlight='3' then '003' 
When TT.NumberFlight='4' then '004' 
else TT.NumberFlight end,
DataFlight=CASE 
When TT.NumberFlight='1' then convert(date,TT.STA) 
When TT.NumberFlight='2' then convert(date,TT.STA) 
When TT.NumberFlight='3' then convert(date,TT.STA) 
When TT.NumberFlight='4' then convert(date,TT.STA) 
else TT.DataFlight end,
STA=CASE 
When TT.NumberFlight='1' then dateadd(mi,-1,TT.STA) 
When TT.NumberFlight='2' then dateadd(mi,-1,TT.STA) 
When TT.NumberFlight='3' then dateadd(mi,-1,TT.STA) 
When TT.NumberFlight='4' then dateadd(mi,-1,TT.STA) 
else TT.STA end
From @TTT TT
COMMIT TRANSACTION
----------------------------------------------
--Select * From @TTT
----------------------------------------------	
BEGIN TRANSACTION	
INSERT INTO @TBL
Select
CASE 
When TT.NumberFlight='1' then '001'
When TT.NumberFlight='2' then '002'
When TT.NumberFlight='3' then '003'
When TT.NumberFlight='4' then '004'
else TT.NumberFlight
end NumberFlight,
TT.CarGo,
TT.AP_TKF,
TT.AP_LND,
TT.DataFlight,
TT.STD,
TT.STA,
TT.IdParing,
TT.IdTask,
TT.TypeFlight,
TT.TPCrew,
TT.IDTaskCP,
TT.Tabel,
TT.PPLS,
--TT.*,
CH.Pr_ArmChairTypeID,
----------------------------
ROW_NUMBER() OVER (PARTITION BY TT.NumberFlight, PR.Pr_CategoryTypeID,TT.TypeFlight order by TT.TPCrew Desc)  NOrder,
--Case 
--when TT.TPCrew='0/0/0/1/0/0/0' then 1
--when TT.TPCrew='0/0/0/0/1/0/0' then 2
--when TT.TPCrew='0/0/0/0/0/1/0' then 3
--when TT.TPCrew='0/0/0/0/0/0/1' then 4
--else 5
--end NOrder,
Case 
When PR.Pr_CategoryTypeID=1 and TT.TypeFlight='L' then 0
When PR.Pr_CategoryTypeID=1 and TT.TypeFlight='D' then 4
When PR.Pr_CategoryTypeID=1 and TT.TypeFlight='*' then 0
----------------------------------------
When PR.Pr_CategoryTypeID=5 and TT.TypeFlight='L' then 1
When PR.Pr_CategoryTypeID=5 and TT.TypeFlight='D' then 4
When PR.Pr_CategoryTypeID=5 and TT.TypeFlight='*' then 1
When PR.Pr_CategoryTypeID is null then 0
else 0
End TypeCrew,
----------------------------
PL.Ap_PlanFlightID,
FL.Fl_FlightID,
PR.Pr_PersonnelID,
PTO.Ap_PlanFlightAirPortID,
PLA.Ap_PlanFlightAirPortID,
PTO.Pr_FlightTaskReestrID,
CR.Ap_PlanFlightAirPortCrewID,
APTO.Ap_AirPortID,
APLA.Ap_AirPortID,
Isnull(PPL.Ak_PPLSTaskTypeID,0) Ak_PPLSTaskTypeID,
PR.Pr_CategoryTypeID Pr_CategoryTypeID
From @TTT TT	
INNER Join Fl_Flights FL on  FL.Name=TT.NumberFlight
INNER Join Ap_PlanFlights PL on  PL.Fl_FlightID=FL.Fl_FlightID --and convert(date, PL.FlightDate)=convert(date,TT.DataFlight)
INNER JOIN Ap_PlanFlightAirPorts PTO ON PTO.Ap_PlanFlightID=PL.Ap_PlanFlightID and PTO.AirPortNumber=1 and convert(smalldatetime,TT.STD)=convert(smalldatetime,PTO.DateTakeoff)
LEFT JOIN Ap_PlanFlightAirPorts PLA ON PLA.Ap_PlanFlightID=PTO.Ap_PlanFlightID and PTO.AirPortNumber+1=PLA.AirPortNumber and convert(smalldatetime,TT.STA)=convert(smalldatetime,PLA.DateLanding)
INNER JOIN Ap_AirPorts APTO ON APTO.CodeIATA=TT.AP_TKF
INNER JOIN Ap_AirPorts APLA ON APLA.CodeIATA=TT.AP_LND 
LEFT  JOIN Pr_Personnels PR ON PR.TableNumber=TT.Tabel and PR.TableNumber>'' and PR.Pr_CategoryTypeID in (1,5) ------------------Только из ЛС Защита от дулирующих Табельных номеров
OUTER APPLY ( Select top 1 * From Pr_PersChairs CC where CC.Pr_PersonnelID = PR.Pr_PersonnelID and ((CC.DateFinish is null) or (PTO.DateTakeoff < CC.DateFinish)) Order by CC.DateBegin Desc) CH			
LEFT JOIN Pr_ArmChairTypes AR		 ON CH.Pr_ArmChairTypeID = AR.Pr_ArmChairTypeID 
LEFT JOIN Ap_PlanFlightAirPortCrews CR ON CR.Ap_PlanFlightAirPortID=PTO.Ap_PlanFlightAirPortID and CR.Pr_PersonnelID=PR.Pr_PersonnelID
LEFT JOIN Ak_PPLSTaskTypes PPL ON LTRIM(RTRIM(PPL.Comment))=LTRIM(RTRIM(TT.PPLS)) and PPL.Comment>''
WHere
	(PL.OnlyFinanceCalculation = 0
	AND PL.Sh_ScheduleVariantTypeID IS NULL
	and PL.Status & 256 <> 256  ----отмененные рейсы
	AND (ISNULL(FL.FlightVariant,0) = 0  or TT.NumberFlight in ('001','002','003','004'))
	--AND PL.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки
	)
COMMIT TRANSACTION	
-------------------------------
----select * From @TBL
------------------------------

------=======================================================Удаление Назначения Ap_PlanFlightAirPortCrews===============================================			
------========================================================================================================================================
		BEGIN TRANSACTION	
		Delete Ap_PlanFlightAirPortCrews
		Where 
		Ap_PlanFlightAirPortID IN 
		(Select Ap_PlanFlightAirPortID_TO 
			From @TBL T
			Where
			Case 
				when @TYPECREW=0 and T.Pr_CategoryTypeID =1 then 1 
				when @TYPECREW=1 and T.Pr_CategoryTypeID =5 then 1
				when @TYPECREW=11 and T.Pr_CategoryTypeID in(1,5) then 1
			else 0
			end = 1
		)
		and
		(
		    Pr_PersonnelID IN 
			(Select Pr_PersonnelID From @TBL T Where
				Case 
					when @TYPECREW=0 and T.Pr_CategoryTypeID =1 then 1 
					when @TYPECREW=1 and T.Pr_CategoryTypeID =5 then 1
					when @TYPECREW=11 and T.Pr_CategoryTypeID in(1,5) then 1
				else 0
				end = 1
			) 
			or ((@REM <> Comment) and (CrewType in (Select T.TypeCrew From @TBL T)))
		) 

		COMMIT TRANSACTION		

------=============================================Назначение на Рейс в ПЗ====================================================================
------========================================================================================================================================
		BEGIN TRANSACTION
		INSERT INTO  dbo.Ap_PlanFlightAirPortCrews
		(Ap_PlanFlightAirPortID,Pr_PersonnelID,Pr_ArmChairTypeID,OrderNumber,CrewType,Executor,Inspector,IndependentFlight,Ak_PPLSTaskTypeID, Comment) ----,
		(
		Select 
			T.Ap_PlanFlightAirPortID_TO,
			T.Pr_PersonnelID,
			Case 
			When T.TypeCrew<>4 Then T.Pr_ArmChairTypeID 
			When T.TypeCrew=4  Then null 
			else null
			end,
			T.NOrder,
			T.TypeCrew,
			157,
			157,
			0,
			(Select PPL.Ak_PPLSTaskTypeID From Ak_PPLSTaskTypes PPL Where PPL.Ak_PPLSTaskTypeID=T.Ak_PPLSTaskTypeID),
			@REM
		From @TBL T 
		Where 
		T.Pr_PersonnelID is not null
		and
		Case 
			when @TYPECREW=0 and T.Pr_CategoryTypeID =1 then 1 
			when @TYPECREW=1 and T.Pr_CategoryTypeID =5 then 1
			when @TYPECREW=11 and T.Pr_CategoryTypeID in(1,5) then 1
		else 0
		end = 1
		)
		COMMIT TRANSACTION
		--================================================
		BEGIN TRANSACTION
		DECLARE @Ap_PlanFlightAirPortID bigint=(Select TOP 1 Ap_PlanFlightAirPortID_TO From @TBL)
		   exec dbo.ab_sp_SetCrewNumbersForPFCrews  @Ap_PlanFlightAirPortID ,3
		COMMIT TRANSACTION
	--======================Дополнительное обновление==============
		--BEGIN TRANSACTION
		--UPDATE dbo.Ap_PlanFlightAirPortCrews
		--	SET OrderNumber=T.NOrder
		--FROM dbo.Ap_PlanFlightAirPortCrews PP
		--INNER JOIN @TBL T  ON T.Ap_PlanFlightAirPortID_TO=PP.Ap_PlanFlightAirPortID and T.Pr_PersonnelID=PP.Pr_PersonnelID
		--COMMIT TRANSACTION
End