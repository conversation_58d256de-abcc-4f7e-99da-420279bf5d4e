﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Crew_employment]
@DTbegin datetime,  
@DTEnd datetime

AS
BEGIN

Declare
@DTending nvarchar(10)='30DEC2075'
Declare
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))
Declare @CC nvarchar(max)=
'5
Screw_id "Crew ID",
Eattribute_type [ "HOMEBASE" ; "RANK" ; "WORKRATE" ; "DUTYOVERTIME"],
Sattribute_value "Attribute value",
Avalid_from_date "Valid from date",
Avalid_to_date "Valid to date",'
DECLARE @TT table
(
TableNumber nvarchar(30),
Attribute_type  nvarchar(30), 
Attribute_value  nvarchar(30),
Valid_from_date  nvarchar(30),
Valid_to_date nvarchar(30),
STRLN nvarchar(max)
)


Declare @ATT table
(TableNumber nvarchar(10),
----attribute_type nvarchar(50),
----attribute_value nvarchar(50),
----valid_from_date nvarchar(20),
----valid_to_date nvarchar(20),
attribute_type1 nvarchar(50),
attribute_value1 nvarchar(50),
valid_from_date1 nvarchar(20),
valid_to_date1 nvarchar(20),
attribute_type2 nvarchar(50),
attribute_value2 nvarchar(50),
valid_from_date2 nvarchar(20),
valid_to_date2 nvarchar(20),
attribute_type3 nvarchar(50),
attribute_value3 nvarchar(50),
valid_from_date3 nvarchar(20),
valid_to_date3 nvarchar(20)
)

Declare @OUTATT table
(
TableNumber nvarchar(10),
attribute_type nvarchar(50),
attribute_value nvarchar(50),
valid_from_date nvarchar(20),
valid_to_date nvarchar(20)
)

------a.	Признак: наличие вида должности «Руководитель группы по стандартам и тренингу» (код РГСТ) – значение: CX80
------b.	Признак: наличие вида должности «начальник отделения кабинных экипажей СБП» (код Нач ОКЭ) – значение: CX80
------c.	Признак: наличие вида должности «Инструктор проводник бортовой по системе управления безопасностью полетов» (код ИПБ СУБП) – значение: FC80 (при отсутствии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»), 
																--CX90 (при наличии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»)
------d.	Признак: наличие вида должности «Инструктор по подготовке кабинного экипажа» (код ИПКЭ) – значение: CX80
------e.	Признак: наличие вида должности «Инструктор по CRM» (код CRMICC) – значение CX80
--=========================================================================================================================
------f.	Признак: наличие в ИС «Авиабит» вида должности «Директор по организации летной работы» (коды «Директор по ОЛР» и «ЛД» ) – значение: FX80 (при отсутствии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»), FX90 (при наличии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»)
------g.	Признак: наличие в ИС «Авиабит» вида должности «Старший пилот-инструктор» (код «ПИ») – значение: FX80 при отсутствии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»), FX90 (при наличии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»)
------h.	Признак: наличие в ИС «Авиабит» вида должности «CRM-инструктор» (код «CRMIFD») – значение: FX80 при отсутствии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»), FX90 (при наличии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»)
------i.	Признак: наличие в ИС «Авиабит» вида должности «Начальник отдела подготовки летного состава» (код «нач ОПЛС») – значение: FX80 при отсутствии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»), FX90 (при наличии согласия на увеличение полетного времени в разделе «Сертификация» ИС «Авиабит»)




INSERT INTO @ATT
Select 
---------------------------------------------------------------------------------
replace(Pr.TableNumber,'/','00') TableNumber,
-------------------------------------
-----------------------------------
'RANK' attribute_type1,   --CRM СБП 
Case
	When  PR.Pr_CategoryTypeID=1 and STF.Code in ('NLS','CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD' ) then 'CP' --- ,'SI'
	When  PR.Pr_CategoryTypeID=1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст','2Пу','FOT','FOTR') then 'FO'
	When  PR.Pr_CategoryTypeID=5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
	When  PR.Pr_CategoryTypeID=5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
	else''
End  attribute_value1,
UPPER(isnull(format(ORD1.DateBegin,'ddMMMyyyy'),'') ) valid_from_date1,
UPPER(isnull(format(PST.DateEnd,'ddMMMyyyy'),@DTending)) valid_to_date1,
-----------------------------------
---LIM.fl fl,
'WORKRATE' attribute_type2,
Case 
When PR.Pr_CategoryTypeID=1 and LIM.fl is null and STF.Code in ('CP','СР', 'CPP', 'КВС','КВС-стажер' ) then 'FC80' --,'SI'--'ПИ','ПИ ИБП','ИТ','П-Инсп' 'FI'
When PR.Pr_CategoryTypeID=1 and LIM.fl is null and STF.Code in ('FO','2П', 'FOP','2П','2Пст','2Пу','FOT','FOTR')  then 'FC80'
---------------------
---------------------
When PR.Pr_CategoryTypeID=1 and LIM.fl is null and STF.Code in ('NLS','CRMI','COPLS','DOLR','Директор по ОЛР','ЛД','CRMIFD','нач ОПЛС','SPI','ПИ','FI','ПИ ИБП','ИТ','П-Инсп') then 'FX80'
When PR.Pr_CategoryTypeID=1 and LIM.fl is not null and STF.Code in ('NLS','CRMI','COPLS','DOLR','Директор по ОЛР','ЛД','CRMIFD','нач ОПЛС','SPI','ПИ','FI','ПИ ИБП','ИТ','П-Инсп') then 'FX90'
---------------------
---------------------
When PR.Pr_CategoryTypeID=1 and LIM.fl is not null and STF.Code in ('CP','СР','FI','CPP', 'КВС','КВС-стажер')then 'FC90' --,'SI' --,'ПИ','ПИ ИБП','ИТ','П-Инсп'
When PR.Pr_CategoryTypeID=1 and LIM.fl is not null and STF.Code in ('FO','2П', 'FOP','2П','2Пст','2Пу','FOT','FOTR') then 'FC90'
---------------------------------------------------------------------------
When PR.Pr_CategoryTypeID=5 and LIM.fl is null and STF.Code in ('СПБ','ИПБ','СБП','БПИ')  then 'CC80'
When PR.Pr_CategoryTypeID=5 and LIM.fl is null and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC80'
---------------------
When PR.Pr_CategoryTypeID=5 and LIM.fl is not null and STF.Code in ('СПБ','ИПБ','СБП','БПИ')  then 'CC90'
When PR.Pr_CategoryTypeID=5 and LIM.fl is not null and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC90'
---------------------------
When PR.Pr_CategoryTypeID=5 and LIM.fl is null and STF.Code in ('РГСТ','Нач ОКЭ','ИПБ СУБП','ИПКЭ','CRMICC','CRMI')  then 'CX80'
When PR.Pr_CategoryTypeID=5 and LIM.fl is not null and STF.Code in ('РГСТ','Нач ОКЭ','ИПБ СУБП','ИПКЭ','CRMICC','CRMI') then 'CX90'

else ''
END attribute_value2,
UPPER(isnull(format(isnull(LIM.DateBegin,PST.DateBegin),'ddMMMyyyy'),'')) valid_from_date2,
UPPER(isnull(format(isnull(case when LIM.DateEnd between @DTbegin and @DTEnd then dateadd(dd,3,dateadd(m,+1,LIM.DateEnd)) else LIM.DateEnd end, PST.DateEnd),'ddMMMyyyy'),@DTending)) valid_to_date2,

----------------------------------------------
---LIMOVR.fl fldt,
'DUTYOVERTIME' attribute_type3,
case
when LIMOVR.fl is not null then 'DUTYOVERTIME'
else ''
end  attribute_value3,
case
when LIMOVR.fl is not null then UPPER(isnull(FORMAT(LIMOVR.DateBegin,'ddMMMyyyy'),''))
else ''
end valid_from_date3,
case
when LIMOVR.fl is not null then UPPER(isnull(FORMAT(LIMOVR.DateEnd,'ddMMMyyyy'),@DTending))
else ''
end valid_to_date3
----------------------------------------------
----LIM.fl,
----LIM.DateBegin,
----LIM.DateEnd
From Pr_Personnels PR
OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
LEft JOIN Pr_StaffTrees     STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
left JOIN  Ap_AirPorts       AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
-----------------------------------------------------------------FC90----------------------------------------------
OUTER APPLY (select top 1 isnull(CER.Pr_LimitTypeID,0) Fl ,CER.DateBegin, CER.DateEnd  From Pr_Certifications CER 
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_LimitTypeID=50
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			) LIM  ------------------------------------------------------------------------------FC90---------------
-----------------------------------------------------------------DUTYOVERTIME----------------------------------------------
OUTER APPLY (select top 1 isnull(CER.Pr_LimitTypeID,0) Fl ,CER.DateBegin, CER.DateEnd  From Pr_Certifications CER 
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_LimitTypeID=69
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			) LIMOVR  ------------------------------------------------------------------------------DUTYOVERTIME---------------
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2)  order by Pr_Orders.Datebegin asc) ORD1  --and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin)
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null))) CH 

Where 
(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
and isnumeric(replace(Pr.TableNumber,'/','00')) >''
and (Pr.Pr_CategoryTypeID in (1,5))
and CH.Pr_ArmChairTypeID  is not null
--===================================================
ORDER BY PR.Pr_CategoryTypeID, PR.TableNumber
--===============================================
----select * From @ATT T 
----order by T.TableNumber
--===============================================
----------------------1-------------------------------------
insert into @OUTATT
Select
replace(Pr.TableNumber,'/','00') TableNumber,
'HOMEBASE' attribute_type,
AA.CodeIATA  attribute_value,
UPPER(isnull(format(APP.DTBeg,'ddMMMyyyy'),'')) valid_from_date, 
UPPER(isnull(format(APP.DTEnd,'ddMMMyyyy'),@DTending)) valid_to_date
From Pr_Personnels PR
OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
LEft JOIN Pr_StaffTrees     STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
------left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
------left JOIN  Ap_AirPorts       AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
------OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2)  order by Pr_Orders.Datebegin asc) ORD1  --and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin)
------OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null))) CH 
OUTER APPLY (select * From dbo.PD_JPR_fn_GET_AeroportsPerson( dateadd(dd,-90,@DTACT), dateadd(dd,60,@DTACT2), '2075-12-30 23:59:00',PR.Pr_PersonnelID)) APP 
Left Join Ap_AirPorts AA on AA.Ap_AirPortID=APP.AP
Where 
(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
and isnumeric(replace(Pr.TableNumber,'/','00')) >''
and (Pr.Pr_CategoryTypeID in (1,5))
and CH.Pr_ArmChairTypeID  is not null
and Case
		When  PR.Pr_CategoryTypeID=1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD','SI' ) then 'CP' --- 
		When  PR.Pr_CategoryTypeID=1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст','2Пу','FOT','FOTR') then 'FO'
		When  PR.Pr_CategoryTypeID=5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
		When  PR.Pr_CategoryTypeID=5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
		else''
	End  <>''
-------------------------------------
insert into @OUTATT
Select
replace(Pr.TableNumber,'/','00') TableNumber,
'RANK' attribute_type1,   --CRM СБП 
Case
	When  PR.Pr_CategoryTypeID=1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD','SI' ) then 'CP' --- 
	When  PR.Pr_CategoryTypeID=1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст','2Пу','FOT','FOTR') then 'FO'
	When  PR.Pr_CategoryTypeID=5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
	When  PR.Pr_CategoryTypeID=5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
	else''
End  attribute_value1,
UPPER(isnull(format(PST.DateBegin,'ddMMMyyyy'),'') ) valid_from_date1,
UPPER(isnull(format(PST.DateEnd,'ddMMMyyyy'),@DTending)) valid_to_date1
From Pr_Personnels PR
OUTER APPLY (select  * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))) PST 
LEft JOIN Pr_StaffTrees     STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
left JOIN  Ap_AirPorts       AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
--OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2)  order by Pr_Orders.Datebegin asc) ORD1  --and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin)
--OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null))) CH 
Where 
(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
and isnumeric(replace(Pr.TableNumber,'/','00')) >''
and (Pr.Pr_CategoryTypeID in (1,5))
and CH.Pr_ArmChairTypeID  is not null
and Case
		When  PR.Pr_CategoryTypeID=1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD','SI' ) then 'CP' --- 
		When  PR.Pr_CategoryTypeID=1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст','2Пу','FOT','FOTR') then 'FO'
		When  PR.Pr_CategoryTypeID=5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
		When  PR.Pr_CategoryTypeID=5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
		else''
	End  <>''
---------------------------------
insert into @OUTATT
Select
TableNumber,
attribute_type2,
attribute_value2,
valid_from_date2 ,
valid_to_date2
From @ATT
Where attribute_value1<>'' 
--------------------------------
insert into @OUTATT
Select
	TableNumber,
	attribute_type3,
	attribute_value3,
	valid_from_date3 ,
	valid_to_date3
From @ATT
Where (attribute_value1<>'' and  attribute_value3<>'' ) 
------------------------------

INSERT INTO @TT
VALUES('','','','','',@CC)
INSERT INTO @TT
Select 
	T.TableNumber,
	T.attribute_type, 
	T.attribute_value, 
	T.valid_from_date, 
	T.valid_to_date, 
	'"'+T.TableNumber+'", '+
	'"'+T.attribute_type   +'", '+
	'"'+T.attribute_value  +'", '+
		T.valid_from_date  +', '+
		T.valid_to_date    +'; '
From @OUTATT T
WHERE not (T.attribute_value='' and T.valid_from_date='' and T.valid_to_date='' )
Order by TableNumber, attribute_type

------------------------------------
Select * FROM @TT
END