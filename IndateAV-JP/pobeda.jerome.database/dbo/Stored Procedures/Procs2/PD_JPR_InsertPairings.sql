﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_InsertPairings]
				@STRXML NVARCHAR(MAX)='',
				@SENS integer=2  ----mask 1--ВС 2--FCREW 4--CabinCrew 8--Tehnics
AS
BEGIN
--Declare
--@STRXML NVARCHAR(MAX)=''

DECLARE @TBL Table
(
NumberFlight nvarchar(10) null,
CarGo nvarchar(5)  null,
AP_TKF nvarchar(5) null,
AP_LND nvarchar(5) null,
DataFlight DateTime null,
STD DateTime null,
STA DateTime null,
IdParing bigint null,
IdTask bigint null,
TypeFlight nvarchar(2) null,
T<PERSON><PERSON> nvarchar(15) null,
IDTaskCP bigint null,
<PERSON><PERSON> nvarchar(10),
<PERSON><PERSON> nvarchar(50) 
)

--================================================================
Declare @ChainPlnID integer=0
DECLARE @xml xml
SELECT @xml = CAST(CAST(@STRXML AS VARBINARY(MAX)) AS XML) 
DECLARE @AP integer
DEclare @SHT integer
--================================================================
BEGIN TRANSACTION	
INSERT INTO @TBL
SELECT 
    x.Rec.query('./NumberFlight').value('.', 'nvarchar(10)') AS 'NumberFlight',
	x.Rec.query('./CarGo').value('.', 'nvarchar(5)') AS 'CarGo',
	x.Rec.query('./APTKF').value('.', 'nvarchar(5)') AS 'AP_TKF',
	x.Rec.query('./APLND').value('.', 'nvarchar(5)') AS 'AP_LND',
    x.Rec.query('./DataFlight').value('.', 'DateTime') AS 'DataFlight',
	x.Rec.query('./STD').value('.', 'DateTime') AS 'STD',
	x.Rec.query('./STA').value('.', 'DateTime') AS 'STA',
	x.Rec.query('./IdParing').value('.', 'integer') AS 'IdParing',
	x.Rec.query('./IdTask').value('.', 'integer') AS 'IdTask',
	x.Rec.query('./TypeFlight').value('.', 'nvarchar(2)') AS 'TypeFlight',
	x.Rec.query('./TPCrew').value('.', 'nvarchar(15)') AS 'TPCrew',
	x.Rec.query('./IDTaskCP').value('.', 'bigint') AS 'IDTaskCP',
	x.Rec.query('./Tabel').value('.', 'nvarchar(10)') AS 'Tabel',
	x.Rec.query('./PPLS').value('.', 'nvarchar(50)') AS 'PPLS'


FROM @xml.nodes('/Flights') as x(Rec)
--================================================================
--Select * From @TBL
COMMIT TRANSACTION		
			
--======================================Определение летного подразделения для цепочки===============================================
--==================================================================================================================================		
Set @AP=isnull((Select	top 1 ATO.Ap_AirPortID	from @TBL  T
			LEFT JOIN Ap_AirPorts ATO ON ATO.CodeIATA=T.AP_TKF
			order by T.STD asc),58)
Set @SHT=(Select
			Case 
			When @SENS=2 then			
						--------------Пилоты-----------
						(Select TOP 1 
						Case
						when @AP=58 then  199
						else TR.Pr_StaffTreeID
						end Pr_StaffTreeID

						From  Pr_StaffTrees	TR   
						LEFT JOIN Pr_StaffTrees PTR with (nolock) ON PTR.Pr_StaffTreeID = TR.Pr_StaffTreeIDParent
						Where  
						TR.Ap_AirPortID=@AP
						and PTR.Ap_AirPortID is null
						and PTR.Name is not null
						and PTR.Pr_StaffTreeID=199)
			When @SENS=4 then
						---------------БП-------------
						isnull((Select TOP 1
						TR.Pr_StaffTreeID 
						From  Pr_StaffTrees	TR   
						LEFT JOIN Pr_StaffTrees PTR with (nolock) ON PTR.Pr_StaffTreeID = TR.Pr_StaffTreeIDParent
						Where  
						TR.Ap_AirPortID=@AP
						and PTR.Ap_AirPortID is null
						and PTR.Name is not null
						and PTR.Pr_StaffTreeID=203),455)
			else null
		    End
		)
--===================================================================================================================================		

--==============================ДОбавление рейсов в справочник АВИАБИТ=================================
--=====================================================================================================

--========================================без повторов===============================================
IF 
isnull((select count(*) 
From Ak_ChainPlns CH 
where 
charindex(convert(nvarchar(20),(Select top 1 T.IdParing From @TBL T  order by STD asc))+(Select top 1 convert(nvarchar(25),T.STD,104) From @TBL T  order by STD asc),CH.Name,1)>0
and @SENS=CH.MaskType),0)=0
--==========================================
BEGIN
	BEGIN TRANSACTION	
	INSERT INTO Fl_Flights
	(   [Gn_CompanyID],
		[Name],
		[Fl_FlightRegularTypeID],
		[CompanyCodeVariant],
		[Agreement],
		[Executor],
		[Inspector],
		[FlightVariant]
	)
		Select 
		GN.Gn_CompanyID,
		T.NumberFlight,
		1,
		2,
		0,
		130,
		130,
		0
		from @TBL  T
		LEFT Join Fl_Flights F On F.Name=T.NumberFlight
		LEFT Join Gn_Companys  GN On Gn.CodeIATA=T.CarGo or Gn.CodeCRT=T.CarGo
		LEFT JOIN Ap_AirPorts ATO ON ATO.CodeIATA=T.AP_TKF
		LEFT JOIN Ap_AirPorts ALA ON ALA.CodeIATA=T.AP_LND
		Where
		F.Fl_FlightID is null
		and Gn.CodeIATA <>'DP'
		and Gn.CodeCRT <>'ДР'
	COMMIT TRANSACTION

	--================================================================
	--================================================================
	BEGIN TRANSACTION	
	INSERT INTO [dbo].[Ak_ChainPlns] 
	-----------------------------------
	Select
	121,
	convert(date,(Select top 1 STD From @TBL order by STD asc)),
	dateadd(dd,2,convert(date,(Select top 1 STD From @TBL order by STD desc))),
	'JP--'+convert(nvarchar(20),(Select top 1 IdParing From @TBL order by STD asc))+(Select top 1 convert(nvarchar(25),T.STD,104) From @TBL T  order by STD asc)+ ' ' +(Select top 1 AP_TKF From @TBL order by STD asc) + '--'+(Select top 1 AP_LND From @TBL order by STA DESC),
	'',
	'JP--'+convert(nvarchar(20),(Select top 1 IdParing From @TBL order by STD asc)),
	122,
	122,
	GetDate(),
	null,
	1,
	1,
	@SENS,----mask 1--ВС 2--FCREW 4--CabinCrew 8--Tehnics
	0,0,0,0,0,0,0,
	1,----Task
	null --@AP
	--------------------------------------------
	Set @ChainPlnID=SCOPE_IDENTITY()
	---Select @ChainPlnID
	COMMIT TRANSACTION

	BEGIN TRANSACTION	
		UPDATE [dbo].[Ak_ChainPlns] 
		SET Pr_StaffTreeID=(Select case when @SHT is not null then @SHT else null end)
		where [Ak_ChainPlnID]=@ChainPlnID
	COMMIT TRANSACTION

	--=======================================================================================
	--=======================================================================================
	BEGIN TRANSACTION	
	INSERT INTO [dbo].[Ak_ChainPlnFlightRoutes]
			Select 
			@ChainPlnID,
			F.Fl_FlightID, 
			ATO.Ap_AirPortID ATO_ID,
			ALA.Ap_AirPortID ALA_ID,
			ROW_NUMBER() over( order by T.STD),
			T.NumberFlight,--'JP-Long-Pairing',
			126,
			126,
			GetDate(),
			null,
			DateDiff(dd,convert(date,(Select top 1 STD From @TBL order by STD asc)),convert(date,STD)),
			--------------------------------
			Case ROW_NUMBER() over(PARTITION BY IdParing order by T.STD)
			When 1 then 1
			else 0
			end,
			---1,-----------------начало раб время
			Case 
			when T.TypeFlight='D' then 1
			when T.TypeFlight='*' then 1
			Else 0
			end ,
			dbo.ab_fn_ShowHHMMSSAsInt(ISNULL(PFTO.DateTakeoff,STD)),
			dbo.ab_fn_ShowHHMMSSAsInt(ISNULL(PFLA.DateLanding,STA)),
			--dbo.ab_fn_ShowHHMMSSAsInt(STD),
			--dbo.ab_fn_ShowHHMMSSAsInt(STA),
			0
			from @TBL  T
			LEFT Join Fl_Flights F On F.Name=T.NumberFlight
			LEFT Join Gn_Companys  GN On (Gn.CodeIATA=T.CarGo or Gn.CodeCRT=T.CarGo)  -- F.Gn_CompanyID=GN.Gn_CompanyID --and              --
			LEFT JOIN Ap_AirPorts ATO ON ATO.CodeIATA=T.AP_TKF
			LEFT JOIN Ap_AirPorts ALA ON ALA.CodeIATA=T.AP_LND
			LEFT JOIN Ap_PlanFlights PF ON PF.Fl_FlightID=F.Fl_FlightID 
			LEFT JOIN Ap_PlanFlightAirPorts PFTO ON PFTO.Ap_PlanFlightID=PF.Ap_PlanFlightID and PFTO.AirPortNumber=1
			OUTER APPLY (select Top 1 * From Ap_PlanFlightAirPorts AP  where AP.Ap_PlanFlightID=PFTO.Ap_PlanFlightID order by AP.AirPortNumber Desc) PFLA
			Where 
			ISNULL(F.FlightVariant,0)=0
			and F.Fl_FlightID is not  null 
			AND 
			(
				(PF.OnlyFinanceCalculation = 0
				AND PF.Sh_ScheduleVariantTypeID IS NULL
				and PF.Status & 256 <> 256  ----отмененные рейсы
				AND ISNULL(F.FlightVariant,0) = 0 
				--AND PF.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки
				and (Gn.CodeIATA='DP'or Gn.CodeCRT='ДР')
				and convert(date,PFTO.DateTakeoff)=convert(date,T.DataFlight)
				)	 
				or 
				(Gn.CodeIATA <>'DP'and Gn.CodeCRT <>'ДР')
				and PF.Ap_PlanFlightID is null
			)
	COMMIT TRANSACTION
--------------------------------------------
END
END