﻿-- =============================================
-- Author:		Koroteev
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Crew_restrictions]
@DTbegin datetime,  
@DTEnd datetime
AS
BEGIN
----------=======================================================================
----------crew_restrictions
----------=======================================================================
------------6
------------Screw_id "Crew ID",
------------Erestriction_type "Restriction type" [ "Medical" ; "Inexperienced" ; "RestrictedStation" ; "Trainee" ],
------------Avalid_from_date "Valid from date",
------------Avalid_to_date "Valid to date",
------------Ivalue_int "Integer value" [ ? ; 0 ; ? ],
------------Svalue_str "String value" [ "?" ; "" ; "?" ] ?"AC qualification for Inexperienced, station for RestrictedStation",

------------"111764", "Inexperienced", 01JAN2010, 31MAR2010, 0, "B737";
------------"133674", "Medical", 01JAN2010, 31MAY2010, 0, " ";
------------"543513", "Medical", 01JAN2000, 31DEC2099, 0, " ";
------------"405565", "RestrictedStation", 01JAN2000, 31DEC2099, 0, "GOT";
------------"401170", "RestrictedStation", 01JAN2000, 31DEC2099, 0, "JFK";
------------"123456", "Trainee", 01JAN2010, 31MAY2010, 0, " ";
----------=======================================================================
----------=======================================================================
----DEclare
----@DTbegin datetime='2020-06-01 00:00:00',  
----@DTEnd datetime='2020-06-30 00:00:00'

Declare
@DTending nvarchar(10)='30DEC2075',
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))

Declare @CC nvarchar(max)=
'6
Screw_id "Crew ID",
Erestriction_type "Restriction type" [ "Medical" ; "Inexperienced" ; "RestrictedStation" ; "Trainee" ],
Avalid_from_date "Valid from date",
Avalid_to_date "Valid to date",
Ivalue_int "Integer value" [ ? ; 0 ; ? ],
Svalue_str "String value" [ "?" ; "" ; "?" ] ?"AC qualification for Inexperienced, station for RestrictedStation",'

DECLARE @TT table
(
TableNumber nvarchar(30),
Attribute_type  nvarchar(30), 
Valid_from_date  nvarchar(30),
Valid_to_date nvarchar(30),
Integer_value integer,
String_value nvarchar(10),
STRLN nvarchar(max)
)
--=========================================
Declare @ATT table
(
TableNumber nvarchar(10),
attribute_type nvarchar(50),
valid_from_date nvarchar(20),
valid_to_date nvarchar(20),
Integer_value integer,
String_value nvarchar(10),
-------------------------------
attribute_type1 nvarchar(50),
valid_from_date1 nvarchar(20),
valid_to_date1 nvarchar(20),
Integer_value1 integer,
String_value1 nvarchar(10),
------------------------------------
attribute_type2 nvarchar(50),
valid_from_date2 nvarchar(20),
valid_to_date2 nvarchar(20),
Integer_value2 integer,
String_value2 nvarchar(10),
------------------------------
attribute_type3 nvarchar(50),
valid_from_date3 nvarchar(20),
valid_to_date3 nvarchar(20),
Integer_value3 integer,
String_value3 nvarchar(10)
)
--===========================================
Declare @OUTATT table
(
TableNumber nvarchar(10),
attribute_type nvarchar(50),
valid_from_date nvarchar(20),
valid_to_date nvarchar(20),
Integer_value integer,
String_value nvarchar(10)
)

INSERT INTO @ATT
Select  DISTINCT
replace(Pr.TableNumber,'/','00') TableNumber,
------------------------------------------------
------Case
------When STF.Code in ('CP',  'КВС','ПИ' )  then STF.Code
------When STF.Code in ('FO','2П', 'FOP','2П')  then STF.Code
------else null
------End valid_from_date1,
--===============================================
----ISNULL(FlightSum.TimeFlight,0)/3600  AAAA,
----ISNULL(OtherFlightSum.TimeOnDevices,0)/3600  BBBB,
----datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTEnd) CCCC,
-------------------------------------------------------
------------------------------------------
--КВС из АК	            500	8
--КВС со стороны	    300	6
--2 Пилот из АК 	    800	12
--2 Пилот со стороны	500	8
--------------------------------
-----------------------Medical-----------------
'Medical' attribute_type,
Case
when datediff(yy,PR.BirthDay,@DTACT)>=60 then UPPER(isnull(FORMAT(@DTACT,'ddMMMyyyy'),''))
else null
end valid_from_date,
-------
Case
when datediff(yy,PR.BirthDay,@DTACT)>=60 then @DTending
else null
end valid_to_date,
0 Integer_value, 
' ' String_value, 
---------------------Inexperienced--------------
------FlightSum.TimeFlight/3600  ggg,
------datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTEnd) fff,
-------------------------------------
'Inexperienced' attribute_type1,
----=========================Тестовые поля========
-----INS.idins,
------STF.Code,
------FlightSum.TimeFlight/3600,
------ISNULL(OtherFlightSum.TimeOnDevices,0),
------datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin),

------Case
------When 
------	(
------		STF.Code in ('CP','СР','CP', 'КВС', 'FO' ) 
------		and 
------		(
------		--(FlightSum.TimeFlight/3600 < 500 ) --and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600=0 )
------		--and
------		(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<=8 )
------		)
------	)
------then  111 ---UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))
------end ,
---=========================================
Case
-----------------------------Пилоты свои--------------------------------
When 
	(
		STF.Code in ('СР','CP', 'КВС' ) 
		and ISNULL(INS.idins,0)=0
		and 
		(
		(FlightSum.TimeFlight/3600 < 500 and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600=0 )
		and
		(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<8 )
		)
	)
then  UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))  ---- --КВС из АК	  500	8
When 
	(
		STF.Code in ('FO','2П') 
		and  ISNULL(INS.idins,0)=0
		and 
		(
		(FlightSum.TimeFlight/3600 < 500 and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600=0) 
		and
		(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<8 )
		)
	)
then UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))  -----2 Пилот из АК   800	12
-----------------------------Пилоты со стороны---------------------------------
When 
	(
		STF.Code in ('СР','CP', 'КВС' ) 
		and ISNULL(INS.idins,0)=0
		and 
		(
		(FlightSum.TimeFlight/3600 < 500 and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 > 0 )--and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 < 1000 
		and
		(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<8) 
		)
	)
then  UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))  --КВС со стороны	    300	6
When 
	(
		STF.Code in ('FO','2П') 
		and ISNULL(INS.idins,0)=0
		and
		(
		(FlightSum.TimeFlight/3600 < 500 and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 > 0 )--and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 < 1000  
		and
		(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<8) 
		)
	)
then UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),'')) --2 Пилот со стороны	500	8

----When 
----	(STF.Code in ('CP', 'КВС') 
----	and  FlightSum.TimeFlight/3600 < 300
----	and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 >= 1000 
----	)
----	and
----	(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTEnd)<6 
----	)
----then  UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))
----When 
----	(STF.Code in ('FO','2П') 
----	and  FlightSum.TimeFlight/3600 < 300
----	and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 >= 1000 
----	) 
----	and
----	(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTEnd)<6  
----	)
----then UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))
else null
End valid_from_date1,
--==============================================
--==============================================
-----------------------------Пилоты свои--------------------------------
Case
When 
	(
		STF.Code in ('NLS','СР', 'CP', 'КВС') 
		and ISNULL(INS.idins,0)=0
		and 
		(
			( FlightSum.TimeFlight/3600 < 500 and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600=0 )
		and
			(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<8 )
		)
	) 
then  UPPER(isnull(FORMAT(@DTACT2,'ddMMMyyyy'),''))
When 
	(
		STF.Code in ('FO','2П') 
		and ISNULL(INS.idins,0)=0
		and 
		(
			(FlightSum.TimeFlight/3600 < 500 and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600=0)
		and
			(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<8 ) 
		)
	)  
then  UPPER(isnull(FORMAT(@DTACT2,'ddMMMyyyy'),''))
--------------------------------------------------------
-----------------------------Пилоты со стороны---------------------------------
When 
	(
		STF.Code in ('NLS','СР', 'CP', 'КВС') 
		and ISNULL(INS.idins,0)=0
		and  
		(
			(FlightSum.TimeFlight/3600 < 500 and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 > 0 ) --and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 < 1000 
		and
			(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<8)
		)
	)
then  UPPER(isnull(FORMAT(@DTACT2,'ddMMMyyyy'),''))
When 
	(
		STF.Code in ('FO','2П') 
		and ISNULL(INS.idins,0)=0
		and  
		(
			(FlightSum.TimeFlight/3600 < 500 and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 > 0 ) --and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 < 1000 
		and
			(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTbegin)<8)
		)
	)
then  UPPER(isnull(FORMAT(@DTACT2,'ddMMMyyyy'),''))
--=================================================================
----When 
----	(STF.Code in ('CP', 'КВС') 
----	and  FlightSum.TimeFlight/3600 < 300
----	and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 >= 1000 
----	)
----	and
----	(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTEnd)<6 
----	)
----then  UPPER(isnull(FORMAT(@DTACT2,'ddMMMyyyy'),''))
----When 
----	(STF.Code in ('FO','2П') 
----	and  FlightSum.TimeFlight/3600 < 300
----	and ISNULL(OtherFlightSum.TimeOnDevices,0)/3600 >= 1000 
----	) 
----	and
----	(datediff(m,ISNULL(CH.DateBegin, PST.DateBegin),@DTEnd)<6  
----	)
----then  UPPER(isnull(FORMAT(@DTACT2,'ddMMMyyyy'),''))

else null
End valid_to_date1,
0 Integer_value1, 
isnull(AIR.PlnTypeIATA,' ')  String_value1, 

-------------------RestrictedStation-------------
'RestrictedStation' attribute_type2,
null valid_from_date2,
null valid_to_date2,
0 Integer_value2, 
' ' String_value2, 
-------------------Trainee----------------------
'Trainee' attribute_type3,
Case
When STF.Code in ( 'CPP','КВС-стажер','FOP','2Пст','ПБст','2Пу','FOT','FOTR')  then  UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))
else null
End valid_from_date3,
Case
When STF.Code in ( 'CPP','КВС-стажер','FOP','2Пст','ПБст','2Пу','FOT','FOTR')  then  UPPER(isnull(FORMAT(@DTACT2,'ddMMMyyyy'),''))
else null
End valid_to_date3,
0 Integer_value3, 
isnull(AIR.PlnTypeIATA,' ') String_value3 

From Pr_Personnels PR
OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
LEft JOIN Pr_StaffTrees   STF    on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
left JOIN  Pr_StaffTrees  PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
left JOIN  Ap_AirPorts    AP     on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null)) order by T.DateBegin Desc) CH 
-----------------------------Medical-----------------------------------
--------------------------------------------------------------------------------
-------------------------------------INSTRUCTOR----------------------------------------------------
OUTER APPLY (select  
				SUM(Case 
					when isnull(CER.Pr_LimitTypeID,0) =57 then 1
					else 0
					End)  +
				SUM(Case 
					when isnull(CER.Pr_LimitTypeID,0) =56 then 1
					else 0
					End) +
				SUM(Case 
					when isnull(CER.Pr_EducationTypeID,0) =46 then 1
					else 0
					End) +
				SUM(Case 
					when isnull(CER.Pr_EducationTypeID,0) =112 then 1
					else 0
					End) +
				SUM(Case 
					when isnull(CER.Pr_EducationTypeID,0) =65 then 1
					else 0
					End) +
				SUM(Case 
					when isnull(CER.Pr_EducationTypeID,0) =71 then 1
					else 0
					End) idins
			From Pr_Certifications CER 
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			--and  (CER.Pr_LimitTypeID in (51,65,60,57,56,58) or CER.Pr_EducationTypeID in (65,71))
			and  (CER.Pr_LimitTypeID in (56,57) or CER.Pr_EducationTypeID in (46,112,65,71))
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			GROUP BY CER.Pr_PersonnelID
			) INS
---------------------------Inexperienced-1--------------------------------------------
OUTER APPLY (Select
				ISNULL(DFF1.TimeFlight,0) TimeFlight
				From
				(SELECT 
					SUM(Landings) Landings, 
					SUM(LandingsOnDevices) LandingsOnDevices, 
					SUM(ISNULL(fsum.TimeFlight,0)) TimeOnDevices, 
					SUM(ISNULL(fsum.TimeFlight,0)) TimeFlight, 
					SUM(TimeFlightNight) TimeFlightNight, 
					SUM(TimeFlightIndependent) TimeFlightIndependent, 
					SUM(TimeWork) TimeWork
					FROM Pr_PersonnelFlights fsum 
					inner join Pr_ArmChairTypes ARM  on ARM.Pr_ArmChairTypeID = fsum.Pr_ArmChairTypeID 
					WHERE 
					fsum.Pr_PersonnelID = Pr.Pr_PersonnelID 
					and PST.DateBegin <= fsum.DateTakeoffReal 
					AND	 fsum.DateTakeoffReal < @DTbegin ----dateadd(d,1,EOMONTH(dateadd(m,-1,getdate()))) 
					and fsum.At_PlnID is not null
					AND  fsum.Type <> 2  
					---AND  fsum.Type <> 3  плановые включаем
					AND  fsum.Type <> 1  
					---==========================================
					and ISNULL(INS.idins,0)=0--не инструкторы
					and 
					Case
						When Pr.Pr_CategoryTypeID=1 and STF.Code in ('NLS','CP','СР', 'CPP', 'КВС','КВС-стажер','Нач ОПЛС' ) and ARM.CodeEng in ('CAP') then 1 -----'КВС' --,'SI'
						---When Pr.Pr_CategoryTypeID=1 and STF.Code in ('П-Инсп','CRMI','FI','DOLR','SPI','COPLS','CRMI', 'ПИ ИБП','ПИ','ИТ','CRMIFD' ) and ARM.CodeEng in ('CAP','PI') then 1 ---- 'ПИ' --,'SI'
						When Pr.Pr_CategoryTypeID=1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст','ВП','2Пу','FOT','FOTR') and ARM.CodeEng in ('FO')  then 1 -----'ВП'
						else 0
					End >0
					---==========================================
				) DFF1) FlightSum 
---------------------------Inexperienced-2--------------------------------------------
OUTER APPLY (Select
				ISNULL(DFF.TimeOnDevices,0) TimeOnDevices
				From
				(SELECT 
					SUM(Landings) Landings, 
					SUM(LandingsOnDevices) LandingsOnDevices, 
					SUM(ISNULL(fsum.TimeFlight,0)) TimeOnDevices, 
					SUM(ISNULL(TimeFlight,0)) TimeFlight, 
					SUM(TimeFlightNight) TimeFlightNight, 
					SUM(TimeFlightIndependent) TimeFlightIndependent, 
					SUM(TimeWork) TimeWork
					FROM Pr_PersonnelFlights fsum 
					WHERE 
					fsum.Pr_PersonnelID = Pr.Pr_PersonnelID 
					and fsum.At_PlnID is not null
					AND  fsum.Type=1  
				) DFF) OtherFlightSum 
				

-----------------------------------------------------
OUTER APPLY (select top 1 isnull(CER.Pr_EducationTypeID,0) Fl, PL.PlnTypeIATA ,CER.DateBegin, CER.DateEnd  
			From Pr_Certifications CER 
			left join At_PlnTypes PL ON PL.Name=CER.PlnTypeModifName
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_EducationTypeID in (43, 150)
			and  PL.PlnTypeIATA >''
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			ORDER BY CER.DateBegin DESC
			) AIR
----------------------------RestrictedStation-------------------------------------------
----------------------------------------------------------------------------------------
-----------------------------Trainee----------------------------------------------------
---------------------------------------------------------------------------------------
OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
----Where 
----(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
----and isnumeric(replace(Pr.TableNumber,'/','00')) >''
----and (Pr.Pr_CategoryTypeID in (1,5))
----and CH.Pr_ArmChairTypeID  is not null
--=====================================================
Where 
(@DTACT<= ORD1.DateFinish or ORD1.DateFinish  is null) 
and (@DTACT <= ORD2.Datebegin or ORD2.Datebegin  is null)
and Pr.TableNumber >''
and ((@DTACT2 >= PST.DateBegin) and (@DTACT<=PST.DateEnd or PST.DateEnd is null)) 
and (Pr.Pr_CategoryTypeID in (1,5))
---and  Pr.TableNumber in ('59','957')
--=================================

------Select * from @ATT

--===============================================
----------1-------------------------------------
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type,
valid_from_date,
valid_to_date,
Integer_value,
String_value  
From @ATT
Where valid_from_date is not null
-------------------------------------------
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type1,
valid_from_date1, 
valid_to_date1,
Integer_value1,
String_value1  
From @ATT
Where valid_from_date1 is not null
---------------------------------------
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type2,
valid_from_date2 ,
valid_to_date2,
Integer_value2,
String_value2  
From @ATT
Where valid_from_date2 is not null
--------------------------------------
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type3,
valid_from_date3 ,
valid_to_date3,
Integer_value3,
String_value3  
From @ATT
Where valid_from_date3 is not null
--------------------------------------
--===========================================
INSERT INTO @TT
VALUES('','','','',0,'',@CC)
INSERT INTO @TT
Select 
	T.TableNumber,
	T.attribute_type, 
	T.valid_from_date, 
	T.valid_to_date,
	T.Integer_value,
	T.String_value,   
	'"'+T.TableNumber+'", '+
	'"'+T.attribute_type   +'", '+
		T.valid_from_date  +', '+
		T.valid_to_date    +', '+
		convert(nvarchar(3),T.Integer_value)    +', '+
	'"'+T.String_value   +'";'
From @OUTATT T
Order by TableNumber, attribute_type
------------------------------------------
Select * FROM @TT
end
