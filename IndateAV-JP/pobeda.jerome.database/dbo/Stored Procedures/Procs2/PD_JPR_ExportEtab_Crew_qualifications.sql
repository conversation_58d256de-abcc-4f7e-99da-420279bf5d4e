﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Crew_qualifications]
@DTbegin datetime,  
@DTEnd datetime

AS
BEGIN
--=========================================================
----Declare
----@DTbegin datetime='2020.03.01 00:00:00',  
----@DTEnd datetime='2020.03.31 23:59:00'
--========================================================

--=======================================================================
--Crew_qualifications
--=======================================================================
------5
------Screw_id "Crew ID",
------Equalification_type "Qualification type" [ "AIRCRAFT" ; "CAT" ; "AIRPORT" ; "PASSPORT" ; "VISA" ; "LANGUAGE" ; "ETOPS" ; "INSTRUCTOR" ; "RHS" ],
------Squalification_sub_type "Qualification sub type",
------Avalid_from_date "Valid from date",
------Avalid_to_date "Valid to date",
------"140001", "AIRCRAFT", "B747", 01JAN2016, 01JAN2075;
------"140001", "AIRPORT", "GOT", 01JAN2016, 01JAN2075;
------"140056", "INSTRUCTOR", "TRI", 19MAR2015, 31DEC2075;
------"140475", "LANGUAGE", "PS", 02NOV2002, 31DEC2075;
------"143991", "PASSPORT", "AU", 27SEP2012, 27SEP2022;
------"143998", "VISA", "US", 09FEB2016, 08FEB2019;
------"143998", "ETOPS", "YES", 09FEB2016, 08FEB2019;
--=======================================================================
--=======================================================================

Declare
@DTending nvarchar(10)='30DEC2075',
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))

Declare @CC nvarchar(max)=
'5
Screw_id "Crew ID",
Equalification_type "Qualification type" [ "AIRCRAFT" ; "CAT" ; "AIRPORT" ; "PASSPORT" ; "VISA" ; "LANGUAGE" ; "ETOPS" ; "INSTRUCTOR" ; "RHS" ],
Squalification_sub_type "Qualification sub type",
Avalid_from_date "Valid from date",
Avalid_to_date "Valid to date",'
DECLARE @TT table
(
TableNumber nvarchar(30),
Attribute_type  nvarchar(30), 
Attribute_value  nvarchar(30),
Valid_from_date  nvarchar(30),
Valid_to_date nvarchar(30),
STRLN nvarchar(max)
)

Declare @ATT table
(
TableNumber nvarchar(10),

attribute_type nvarchar(50),
attribute_value nvarchar(50),
valid_from_date nvarchar(20),
valid_to_date nvarchar(20),
----
attribute_type0 nvarchar(50),
attribute_value0 nvarchar(50),
valid_from_date0 nvarchar(20),
valid_to_date0 nvarchar(20),
----
attribute_type1 nvarchar(50),
attribute_value1 nvarchar(50),
valid_from_date1 nvarchar(20),
valid_to_date1 nvarchar(20),

attribute_type2 nvarchar(50),
attribute_value2 nvarchar(50),
valid_from_date2 nvarchar(20),
valid_to_date2 nvarchar(20),

attribute_type3 nvarchar(50),
attribute_value3 nvarchar(50),
valid_from_date3 nvarchar(20),
valid_to_date3 nvarchar(20),

attribute_type4 nvarchar(50),
attribute_value4 nvarchar(50),
valid_from_date4 nvarchar(20),
valid_to_date4 nvarchar(20),

attribute_type5 nvarchar(50),
attribute_value5 nvarchar(50),
valid_from_date5 nvarchar(20),
valid_to_date5 nvarchar(20)
)

Declare @OUTATT table
(
TableNumber nvarchar(10),
attribute_type nvarchar(50),
attribute_value nvarchar(50),
valid_from_date nvarchar(20),
valid_to_date nvarchar(20)
)

INSERT INTO @ATT
Select  DISTINCT
replace(Pr.TableNumber,'/','00') TableNumber,
-------------------AIRCRAFT------------------
'AIRCRAFT' attribute_type,
AIR.PlnTypeIATA attribute_value,
UPPER(isnull(FORMAT(AIR.DateBegin,'ddMMMyyyy'),'')) valid_from_date,
case when AIR.DateBegin  is not null then  UPPER(isnull(FORMAT(AIR.DateEnd,'ddMMMyyyy'),@DTending)) else Null end valid_to_date,

-------------------RHS-----------------------
'RHS' attribute_type0,
'' attribute_value0,
UPPER(isnull(FORMAT(RHS.DateBegin,'ddMMMyyyy'),'')) valid_from_date0,
case when RHS.DateBegin  is not null then  UPPER(isnull(FORMAT(RHS.DateEnd,'ddMMMyyyy'),@DTending)) else Null end valid_to_date0,

--------------------PASSPORT-----------------
'PASSPORT' attribute_type1,
case
when PAS.Pr_DocumentTypeID in (2,18)  then 'RU'
when PAS.Pr_DocumentTypeID in (29)    then 'BY' 
else '' 
end attribute_value1,
UPPER(isnull(FORMAT(PAS.DateBegin,'ddMMMyyyy'),'')) valid_from_date1,
case when PAS.DateBegin  is not null then  UPPER(isnull(FORMAT(PAS.DateEnd,'ddMMMyyyy'),@DTending)) else Null end valid_to_date1,

-------------------VISA-----------------------------
'VISA' attribute_type2,
VIS.Pr_DocumentTypeID attribute_value2,
UPPER(isnull(FORMAT(VIS.DateBegin,'ddMMMyyyy'),''))  valid_from_date2,
case when VIS.DateBegin  is not null then  UPPER(isnull(FORMAT(VIS.DateEnd,'ddMMMyyyy'),@DTending)) else Null end valid_to_date2,

-------------------LANGUAGE-----------------------------
'LANGUAGE' attribute_type3,
case 
when LAN.Pr_LimitTypeID is not null then 'EN'
else ''
end attribute_value3,
UPPER(isnull(FORMAT(LAN.DateBegin,'ddMMMyyyy'),'')) valid_from_date3,
case when LAN.DateBegin  is not null then  UPPER(isnull(FORMAT(LAN.DateEnd,'ddMMMyyyy'),@DTending)) else Null end valid_to_date3,

-------------------INSTRUCTOR-----------------------
'INSTRUCTOR' attribute_type4,
Case
When PR.Pr_CategoryTypeID=1 then 
							Case
							When INS.id51>0 and INS.id65>0 and INS.id60 >0  and INS.id57 >0 and INS.id56 >0  then 'TRI'
							When INS.id51>0 and INS.id65>0 and INS.id60 >0  and INS.id65ed >0                then 'TRE'
							When INS.id51>0 and INS.id65>0 and INS.id56 >0									 then 'TRR'
							When INS.id51>0 and INS.id65>0 and INS.id57 >0									 then 'SFI'
							When INS.id51>0 and INS.id65>0													 then 'FI'
							When INS.id51>0 and INS.id58>0													 then 'SI'
							When INS.id51>0 and STF.Code in ('CRMIFD')										 then 'CRMI'
							When INS.id71ed >0																 then 'TFI'
							else ''
							End
When PR.Pr_CategoryTypeID=5 then 
			                Case 
							When  STF.Code in ('CRMICC') then 'CRMI'
							When  STF.Code in ('ИПБ') then 'LI'
							When  STF.Code in ('БПИ') then 'LI'
							When  STF.Code in ('ИПКЭ') then 'TRI'
							When  STF.Code in ('ИПБ СУБП') then 'SI'
							else  ''
							End
End attribute_value4,
Case
When PR.Pr_CategoryTypeID=1 then 
							Case
							When INS.id51>0 and INS.id65>0 and INS.id60 >0  and INS.id57 >0 and INS.id56 >0  then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
							When INS.id51>0 and INS.id65>0 and INS.id60 >0  and INS.id65ed >0                then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
							When INS.id51>0 and INS.id65>0 and INS.id56 >0									 then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
							When INS.id51>0 and INS.id65>0 and INS.id57 >0									 then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),'')) 
							When INS.id51>0 and INS.id65>0													 then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
							When INS.id51>0 and INS.id58>0													 then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
							When INS.id51>0 and STF.Code in ('CRMIFD')										 then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
							When INS.id71ed >0																 then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
							else null
							End
When PR.Pr_CategoryTypeID=5 then 
			                Case 
							When  STF.Code in ('CRMICC','ИПБ', 'ИПКЭ', 'ИПБ СУБП', 'БПИ') then  UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))
							else null
							End
End valid_from_date4,
Case
When PR.Pr_CategoryTypeID=1 then 
							Case
							When INS.id51>0 and INS.id65>0 and INS.id60 >0  and INS.id57 >0 and INS.id56 >0  then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							When INS.id51>0 and INS.id65>0 and INS.id60 >0  and INS.id65ed >0                then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							When INS.id51>0 and INS.id65>0 and INS.id56 >0									 then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							When INS.id51>0 and INS.id65>0 and INS.id57 >0									 then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							When INS.id51>0 and INS.id65>0													 then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							When INS.id51>0 and INS.id58>0													 then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							When INS.id51>0 and STF.Code in ('CRMIFD')										 then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							When INS.id71ed >0																 then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							else null
							End
When PR.Pr_CategoryTypeID=5 then 
			                Case 
							When  PST.DateBegin is not null and STF.Code in ('CRMICC','ИПБ', 'ИПКЭ', 'ИПБ СУБП','БПИ') then  UPPER(isnull(FORMAT(PST.DateEnd,'ddMMMyyyy'),@DTending))
							else null
							End
End valid_to_date4,

-------------------CAT------------------
'CAT' attribute_type5,
CAT.CodeEng attribute_value5,
UPPER(isnull(FORMAT(AIR.DateBegin,'ddMMMyyyy'),'')) valid_from_date5,
case when AIR.DateBegin  is not null then  UPPER(isnull(FORMAT(AIR.DateEnd,'ddMMMyyyy'),@DTending)) else Null end valid_to_date5

---------------------------

From Pr_Personnels PR
OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
LEft JOIN Pr_StaffTrees   STF    on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
left JOIN  Pr_StaffTrees  PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
left JOIN  Ap_AirPorts    AP     on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null))) CH 
-------------------------------CAT------------------------------------------------	
OUTER APPLY (select top 1 isnull(CER.Pr_MeteoMinID,0) Fl, MET.CodeEng ,CER.DateBegin, CER.DateEnd  
			From Pr_Certifications CER 
			LEFT JOIN Pr_MeteoMins MET ON MET.Pr_MeteoMinID=CER.Pr_MeteoMinID
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  MET.CodeEng <>''
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			ORDER BY CER.DateBegin DESC
			) CAT
--------------------------------------Airocraft---------------------------------
OUTER APPLY (select top 1 isnull(CER.Pr_EducationTypeID,0) Fl, PL.PlnTypeIATA ,CER.DateBegin, CER.DateEnd  
			From Pr_Certifications CER 
			left join At_PlnTypes PL ON PL.Name=CER.PlnTypeModifName
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_EducationTypeID in (43, 150)
			and  PL.PlnTypeIATA >''
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			ORDER BY CER.DateBegin DESC
			) AIR
--------------------------------------RHS----------------------------------------
OUTER APPLY (select top 1 isnull(CER.Pr_EducationTypeID,0) Fl, PL.PlnTypeIATA ,CER.DateBegin, CER.DateEnd  
			From Pr_Certifications CER 
			left join At_PlnTypes PL ON PL.Name=CER.PlnTypeModifName
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_EducationTypeID in (69)
			and  PL.PlnTypeIATA >''
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			ORDER BY CER.DateBegin DESC
			) RHS
---------------------------PASSPORT---------------------------------------------
OUTER APPLY (select top 1 isnull(CER.Pr_DocumentTypeID,0) Fl, CER.Pr_DocumentTypeID, CER.DateBegin, CER.DateEnd  
			From Pr_Certifications CER 
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_DocumentTypeID in (2,18,29)
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			ORDER BY CER.DateBegin DESC
			) PAS
---------------------------------------VISA-------------------------------------------
OUTER APPLY (select top 1 isnull(CER.Pr_DocumentTypeID,0) Fl, CER.Pr_DocumentTypeID, CER.DateBegin, CER.DateEnd  
			From Pr_Certifications CER 
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_DocumentTypeID in (9,19,26)
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			ORDER BY CER.DateBegin DESC
			) VIS
-------------------------------------LANGUAGE----------------------------------------------------
OUTER APPLY (select top 1 isnull(CER.Pr_LimitTypeID,0) Fl, CER.Pr_LimitTypeID, CER.DateBegin, CER.DateEnd  
			From Pr_Certifications CER 
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_LimitTypeID in (36,38)
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			ORDER BY CER.DateBegin DESC
			) LAN
-------------------------------------INSTRUCTOR----------------------------------------------------
OUTER APPLY (select  
				SUM(Case 
					when isnull(CER.Pr_LimitTypeID,0) =51 then 1
					End) id51,
				SUM(Case 
					when isnull(CER.Pr_LimitTypeID,0) =65 then 1
					End) id65,
				SUM(Case 
					when isnull(CER.Pr_LimitTypeID,0) =60 then 1
					End) id60,
				SUM(Case 
					when isnull(CER.Pr_LimitTypeID,0) =57 then 1
					End) id57,
				SUM(Case 
					when isnull(CER.Pr_LimitTypeID,0) =56 then 1
					End) id56,
				SUM(Case 
					when isnull(CER.Pr_LimitTypeID,0) =58 then 1
					End) id58,
				SUM(Case 
					when isnull(CER.Pr_EducationTypeID,0) =65 then 1
					End) id65ed,
				SUM(Case 
					when isnull(CER.Pr_EducationTypeID,0) =71 then 1
					End) id71ed,
				MAX(CER.DateBegin) Dbeg, 
				MIN(CER.DateEnd) DEnd
			From Pr_Certifications CER 
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  (CER.Pr_LimitTypeID in (51,65,60,57,56,58) or CER.Pr_EducationTypeID in (65,71))
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			GROUP BY CER.Pr_PersonnelID
			) INS

OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
Where 
(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
and isnumeric(replace(Pr.TableNumber,'/','00')) >''
and (Pr.Pr_CategoryTypeID in (1,5))
and CH.Pr_ArmChairTypeID  is not null
--=====================================================
--Select * From @ATT
--=====================================================
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type,
attribute_value,
valid_from_date,
valid_to_date 
From @ATT
Where attribute_value<>''

----------------------CAT 5-------------------------------------
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type5,
attribute_value5,
valid_from_date5,
valid_to_date5 
From @ATT
Where attribute_type5='CAT' and attribute_value5 is not null

----------------------2-------------------------------------
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type0,
attribute_value0,
valid_from_date0,
valid_to_date0 
From @ATT
Where attribute_type0='RHS'
-------------------------------------
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type1,
attribute_value1,
valid_from_date1, 
valid_to_date1
From @ATT
Where attribute_value1<>'' 
---------------------------------
insert into @OUTATT
Select DISTINCT
TableNumber,
attribute_type2,
attribute_value2,
valid_from_date2 ,
valid_to_date2
From @ATT
Where attribute_value2<>'' 
--------------------------------
insert into @OUTATT
Select DISTINCT
	TableNumber,
	attribute_type3,
	attribute_value3,
	valid_from_date3 ,
	valid_to_date3
From @ATT
Where (attribute_value3<>'' ) 

--===============================================================
--===============================================================
Select
*
INTO #TTP
From 
(
Select  DISTINCT
replace(Pr.TableNumber,'/','00') TableNumber,
-------------------INSTRUCTOR-----------------------
'INSTRUCTOR' attribute_type4,

		Case
			When  PR.Pr_CategoryTypeID=1 and INS.id112ed >0 and INS.id46 >0	 and INS.id65ed >0		then 'TRE'     ---112ed,46, 65ed	 ---and INS.id56 >0 
		else null
		end  AT_Var1,
		Case							
			When  PR.Pr_CategoryTypeID=1 and INS.id112ed >0 and INS.id46 >0	and INS.id56 >0 	then 'TRR'     --112ed,46,56
		else null
		end  AT_Var2,
		Case
			When  PR.Pr_CategoryTypeID=1 and INS.id112ed >0 and INS.id57 >0	and INS.id46 >0		then 'SFI'     --112ed,57,46	
		else null
		end  AT_Var3,		
		Case
			When  PR.Pr_CategoryTypeID=1 and INS.id112ed >0 and INS.id46 >0	and INS.id65ed >0 and  INS.id56>0	then 'TRI'		--112ed 46 65ed 56
		else null
		end  AT_Var4,
		Case
			When  PR.Pr_CategoryTypeID=1 and INS.id112ed >0 and INS.id46 >0						then 'FI'		--112ed 46
		else null
		end  AT_Var5,
		Case
			When  PR.Pr_CategoryTypeID=1 and INS.id112ed >0 and INS.id57 >0 					then 'SI'		---112ed 57
		else null
		end  AT_Var6,
		Case
			When  PR.Pr_CategoryTypeID=1 and INS.id112ed >0 and STF.Code in ('CRMIFD')			then 'CRMI' 
		else null
		end  AT_Var7,
		Case
			When  PR.Pr_CategoryTypeID=1 and INS.id112ed >0 and INS.id46 >0 and INS.id71ed >0	then 'TFI'      ---112ed, 46 71ed   
		else null
		end  AT_Var8,
		Case 
			When PR.Pr_CategoryTypeID=5 and STF.Code in ('CRMICC')   then 'CRMI'
		else null
		end  AT_Var9,
		Case 
			When PR.Pr_CategoryTypeID=5 and STF.Code in ('ИПБ', 'БПИ', 'ИПКЭ')   then 'LI'
		else null
		end  AT_Var10,
		Case 
			When PR.Pr_CategoryTypeID=5 and STF.Code in ('ИПКЭ')     then 'TRI'
		else null
		end  AT_Var11,
		Case 
			When PR.Pr_CategoryTypeID=5 and STF.Code in ('ИПБ СУБП') then 'SI'
		else null
		end  AT_Var12,

Case
When PR.Pr_CategoryTypeID=1 then 
							Case
								When INS.id112ed >0 and INS.id46 >0  and INS.id56 >0  and INS.id65ed >0	then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
								When INS.id112ed >0 and INS.id46 >0										then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
								When INS.id112ed >0 and INS.id46 >0   and INS.id65ed >0					then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
								When INS.id112ed >0  and INS.id46 >0  and INS.id56 >0 					then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
								When INS.id112ed >0 and INS.id57 >0   and INS.id46 >0					then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
								When INS.id112ed >0 and INS.id57 >0 									then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
								When INS.id112ed >0 and STF.Code in ('CRMIFD')							then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
								When INS.id112ed >0 and INS.id46 >0 and INS.id71ed >0					then UPPER(isnull(FORMAT(INS.Dbeg,'ddMMMyyyy'),''))
							End
When PR.Pr_CategoryTypeID=5 then 
			                Case 
							When  STF.Code in ('БПИ','CRMICC','ИПБ', 'ИПКЭ', 'ИПБ СУБП') then  UPPER(isnull(FORMAT(PST.DateBegin,'ddMMMyyyy'),''))
							else null
							End
End valid_from_date4,
Case
When PR.Pr_CategoryTypeID=1 then 
							Case
								When INS.id112ed >0 and INS.id46 >0  and INS.id56 >0  and INS.id65ed >0	then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
								When INS.id112ed >0 and INS.id46 >0										then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),''))
								When INS.id112ed >0 and INS.id46 >0   and INS.id65ed >0					then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),''))
								When INS.id112ed >0  and INS.id46 >0   and INS.id56 >0 					then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
								When INS.id57 >0  and INS.id112ed >0	and INS.id46 >0					then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
								When INS.id112ed >0 and INS.id57 >0 									then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
								When INS.id112ed >0 and STF.Code in ('CRMIFD')							then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
								When INS.id112ed >0 and INS.id46 >0 and INS.id71ed >0					then UPPER(isnull(FORMAT(INS.DEnd,'ddMMMyyyy'),@DTending))
							else null
							End
When PR.Pr_CategoryTypeID=5 then 
			                Case 
								When  PST.DateBegin is not null and STF.Code in ('БПИ','CRMICC','ИПБ', 'ИПКЭ', 'ИПБ СУБП') then  UPPER(isnull(FORMAT(PST.DateEnd,'ddMMMyyyy'),@DTending))
							else null
							End
End valid_to_date4

From Pr_Personnels PR with (nolock)
OUTER APPLY (select top 1 * From Pr_PersPosts t with (nolock) where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
LEft JOIN Pr_StaffTrees   STF					with (nolock)   on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
left JOIN  Pr_StaffTrees  PrnSTF				with (nolock) ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
left JOIN  Ap_AirPorts    AP					with (nolock)   on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
OUTER APPLY (select top 1 * From Pr_Orders		with (nolock) where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
OUTER APPLY (select top 1 * From Pr_Orders		with (nolock) where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
-------------------------------------INSTRUCTOR----------------------------------------------------
OUTER APPLY (select  
				sum(Case 
					when isnull(CER.Pr_EducationTypeID,0) =112 then 1
					else 0
					End) id112ed,
				sum(Case 
					when isnull(CER.Pr_LimitTypeID,0) =46 then 1
					else 0
					End) id46,
				sum(Case 
					when isnull(CER.Pr_LimitTypeID,0) =57 then 1
					else 0
					End) id57,
				Sum(Case 
					when isnull(CER.Pr_LimitTypeID,0) =56 then 1
					else 0
					End) id56,
				Sum(Case 
					when isnull(CER.Pr_EducationTypeID,0) =65 then 1
					else 0
					End) id65ed,
				Sum(Case 
					when isnull(CER.Pr_EducationTypeID,0) =71 then 1
					else 0
					End) id71ed,
				Max(CER.DateBegin) Dbeg, 
				Min(CER.DateEnd) DEnd
			From Pr_Certifications CER with (nolock)
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  (CER.Pr_LimitTypeID in (46,56,57) or CER.Pr_EducationTypeID in (112,65,71))
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			GROUP BY CER.Pr_PersonnelID
			) INS
OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
Where 
(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
and isnumeric(replace(Pr.TableNumber,'/','00')) >''
and (Pr.Pr_CategoryTypeID in (1,5))
) NNN
Where valid_from_date4 >''
----------------------------------------------------
INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var1,valid_from_date4,valid_to_date4 From #TTP Where AT_Var1 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var2,valid_from_date4,valid_to_date4 From #TTP Where AT_Var2 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var3,valid_from_date4,valid_to_date4 From #TTP Where AT_Var3 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var4,valid_from_date4,valid_to_date4 From #TTP Where AT_Var4 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var5,valid_from_date4,valid_to_date4 From #TTP Where AT_Var5 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var6,valid_from_date4,valid_to_date4 From #TTP Where AT_Var6 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var7,valid_from_date4,valid_to_date4 From #TTP Where AT_Var7 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var8,valid_from_date4,valid_to_date4 From #TTP Where AT_Var8 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var9,valid_from_date4,valid_to_date4 From #TTP Where AT_Var9 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var10,valid_from_date4,valid_to_date4 From #TTP Where AT_Var10 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var11,valid_from_date4,valid_to_date4 From #TTP Where AT_Var11 is not null

INSERT INTO @OUTATT
Select TableNumber ,attribute_type4 ,AT_Var12,valid_from_date4,valid_to_date4 From #TTP Where AT_Var12 is not null

--===============================================================
-----------------------------------------------
insert into @OUTATT
EXEC PD_JPR_ExportEtab_Crew_AIRPORT @DTbegin ,  @DTEnd 
--------------------------------------------------
----select * from @OUTATT
----WHERE attribute_type='INSTRUCTOR'
----order by TableNumber
--------------------------------------------------
INSERT INTO @TT
VALUES('','','','','',@CC)
INSERT INTO @TT
Select 
	T.TableNumber,
	T.attribute_type, 
	T.attribute_value, 
	T.valid_from_date, 
	T.valid_to_date, 
	'"'+T.TableNumber+'", '+
	'"'+T.attribute_type   +'", '+
	'"'+T.attribute_value  +'", '+
		T.valid_from_date  +', '+
		T.valid_to_date    +'; '
From @OUTATT T
WHERE not (T.attribute_value='' and T.valid_from_date='' and T.valid_to_date='' )
Order by TableNumber, attribute_type

------------------------------------
Select * FROM @TT
--where attribute_type='PASSPORT' and TableNumber='685'
Drop table  #TTP
end
