﻿ ----=============================================
 ----Author:		<Author,,Name>
 ----Create date: <Create Date,,>
 ----Description:	<Description,,>
 ----=============================================
CREATE PROCEDURE [dbo].[PD_JPR_InsertEXPOMarker]
				@STRXML NVARCHAR(MAX)=''
AS
BEGIN
--===========================================
----	Declare
----@STRXML NVARCHAR(MAX)='<Flights>
----<Id_Flight>12433</Id_Flight>
----<NumberFlight>2</NumberFlight>
----<CarGo>DP</CarGo>
----<APTKF>VKO</APTKF>
----<APLND>VKO</APLND>
----<DataFlight>2020-03-07T00:00:00Z</DataFlight>
----<STD>2020-03-07T18:00:00Z</STD>
----<STA>2020-03-08T06:00:00Z</STA>
----<TypeFlight>*</TypeFlight>
----<IdParing>327701</IdParing>
----<IdTask>6438</IdTask>
----<TPCrew>1/0/0/0/0/0/0/0</TPCrew>
----<Tabel>1755</Tabel>
----<PPLS/>
----<Bid>true</Bid>
----</Flights>'	
	--=======================================================
	DECLARE @TTT Table
	(
	NumberFlight nvarchar(10) null,
	CarGo nvarchar(5)  null,
	AP_TKF nvarchar(5) null,
	AP_LND nvarchar(5) null,
	DataFlight DateTime null,
	STD DateTime null,
	STA DateTime null,
	IdParing bigint null,
	IdTask bigint null,
	TypeFlight nvarchar(10) null,
	TPCrew nvarchar(15) null,
	IDTaskCP bigint null,
	Tabel nvarchar(15) null,
	PPLS nvarchar(50) null,
	Bid bit
	)

	DECLARE @xml xml
	SELECT @xml = CAST(CAST(@STRXML AS VARBINARY(MAX)) AS XML) 
	DECLARE @AP integer
	DEclare @SHT integer
	INSERT INTO @TTT
	SELECT 
		x.Rec.query('./NumberFlight').value('.', 'nvarchar(10)') AS 'NumberFlight',
		x.Rec.query('./CarGo').value('.', 'nvarchar(5)') AS 'CarGo',
		x.Rec.query('./APTKF').value('.', 'nvarchar(5)') AS 'AP_TKF',
		x.Rec.query('./APLND').value('.', 'nvarchar(5)') AS 'AP_LND',
		x.Rec.query('./DataFlight').value('.', 'DateTime') AS 'DataFlight',
		x.Rec.query('./STD').value('.', 'DateTime') AS 'STD',
		x.Rec.query('./STA').value('.', 'DateTime') AS 'STA',
		x.Rec.query('./IdParing').value('.', 'bigint') AS 'IdParing',
		x.Rec.query('./IdTask').value('.', 'bigint') AS 'IdTask',
		x.Rec.query('./TypeFlight').value('.', 'nvarchar(10)') AS 'TypeFlight',
		x.Rec.query('./TPCrew').value('.', 'nvarchar(15)') AS 'TPCrew',
		x.Rec.query('./IDTaskCP').value('.', 'bigint') AS 'IDTaskCP',
		x.Rec.query('./Tabel').value('.', 'nvarchar(10)') AS 'Tabel',
		x.Rec.query('./PPLS').value('.', 'nvarchar(50)') AS 'PPLS',
		x.Rec.query('./Bid').value('.', 'bit') AS 'Bid'
	FROM @xml.nodes('/Flights') as x(Rec)
	where x.Rec.query('./Bid').value('.', 'bit')=1
	--===============================================
	---select * From @TTT
--===============================================
--===============================================
IF not EXISTS(Select 
			MRB.*
			From Ak_PlannerRequestTypes MRB
			Where MRB.CodeEng='Bids'
			)
Begin
		INSERT INTO Ak_PlannerRequestTypes
		Values(
		'Crew Bids',
		'Crew Bids',
		'Bids',
		'Bids',
		0,
		'',
		0,
		'Crew Bids',
		255,
		255,
		getdate(),
		null,
		2,
		0,
		65535,
		0)
END
--=============================================
--===============Удаление для тестирования=====================
-------------------------------------------------------
--===============Удаление для тестирования для ВСЕХ=====Вариант удаления 1================
----DELETE 
----From Ak_PlannerCrewRequests 
----Where Ak_PlannerCrewRequests.Ak_PlannerCrewRequestID in
----	(Select
----	MR.Ak_PlannerCrewRequestID
----	From  Pr_Personnels PR ------------------Только из ЛС Защита от дублирующих Табельных номеров	
----	inner  JOIN Ak_PlannerCrewRequests MR  ON MR.Pr_PersonnelID = PR.Pr_PersonnelID 
----	inner  JOIN Ak_PlannerRequestTypes TMR ON  TMR.Ak_PlannerRequestTypeID=MR.Ak_PlannerRequestTypeID and TMR.CodeEng='Bids' 
----	Where 
----	MR.Ak_PlannerCrewRequestID is not null 
----	)
----------------------------Вариант удаления 2-----------
--DELETE 
--From Ak_PlannerCrewRequests 
--Where Ak_PlannerCrewRequests.Ak_PlannerCrewRequestID in
--	(Select
--	MR.Ak_PlannerCrewRequestID
--	From @TTT TT
--	inner  JOIN Pr_Personnels PR  ON PR.TableNumber=TT.Tabel and PR.TableNumber>'' and PR.Pr_CategoryTypeID in (1,5) ------------------Только из ЛС Защита от дублирующих Табельных номеров	
--	left  JOIN Ak_PlannerCrewRequests MR  ON MR.Pr_PersonnelID = PR.Pr_PersonnelID  and abs(datediff(hh,convert(smalldatetime,MR.DateBegin),convert(smalldatetime,dateadd(hh,3,TT.STD))))<=7 and abs(datediff(hh,convert(smalldatetime,MR.DateEnd),convert(smalldatetime,dateadd(hh,3,TT.STA))))<=7
--	left  JOIN Ak_PlannerRequestTypes TMR ON  TMR.Ak_PlannerRequestTypeID=MR.Ak_PlannerRequestTypeID and TMR.CodeEng='Bids' 
--	Where 
--	TT.Bid=1 and MR.Ak_PlannerCrewRequestID is not null 
--	)
--==============================================

UPDATE [Ak_PlannerCrewRequests]
Set Comment='UPJP '+Comment
-----------------------
From Ak_PlannerCrewRequests 
Where Ak_PlannerCrewRequests.Ak_PlannerCrewRequestID in
	(Select
	MR.Ak_PlannerCrewRequestID
	From @TTT TT
	inner  JOIN Pr_Personnels PR  ON PR.TableNumber=TT.Tabel and PR.TableNumber>'' and PR.Pr_CategoryTypeID in (1,5) ------------------Только из ЛС Защита от дублирующих Табельных номеров	
	inner  JOIN Ak_PlannerCrewRequests MR  ON MR.Pr_PersonnelID = PR.Pr_PersonnelID  and abs(datediff(MINUTE,convert(smalldatetime,MR.DateBegin),convert(smalldatetime,dateadd(hh,0,TT.STD))))<=10 and abs(datediff(MINUTE,convert(smalldatetime,MR.DateEnd),convert(smalldatetime,dateadd(hh,0,TT.STA))))<=10
	inner  JOIN Ak_PlannerRequestTypes TMR ON  TMR.Ak_PlannerRequestTypeID=MR.Ak_PlannerRequestTypeID
	Where 
	TT.Bid=1 and MR.Ak_PlannerCrewRequestID is not null and TMR.CodeEng='Bids' and MR.RecordDeleted=0
	)
--=================================================
	INSERT INTO Ak_PlannerCrewRequests
	(
		Pr_PersonnelID,
		Name,
		DateBegin,
		DateEnd,
		Ak_PlannerRequestTypeID,
		Comment,
		Executor,
		Inspector,
		MarkerType,
		Color
	)
	select 
	 (Select PR.Pr_PersonnelID From Pr_Personnels PR where PR.TableNumber=TT.Tabel), ---PR.Pr_PersonnelID,
	'Crew Bid',
	dateadd(hh,0,TT.STD),
	dateadd(hh,0,TT.STA),
	(Select TMR.Ak_PlannerRequestTypeID  From Ak_PlannerRequestTypes TMR Where TMR.CodeEng='Bids'),
	'JP-'+convert(nvarchar(30),getutcdate(),120),
	0,
	0 ,
	(Select TMR.MarkerType From Ak_PlannerRequestTypes TMR Where TMR.CodeEng='Bids'),
	(Select TMR.Color From Ak_PlannerRequestTypes TMR Where TMR.CodeEng='Bids')
	-----------------------
	From @TTT TT
	Where 
	not EXISTS(Select
					MR.Ak_PlannerCrewRequestID
					From @TTT TTT
					inner  JOIN Pr_Personnels PR  ON PR.TableNumber=TTT.Tabel and PR.TableNumber>'' and PR.Pr_CategoryTypeID in (1,5) ------------------Только из ЛС Защита от дублирующих Табельных номеров	
					inner  JOIN Ak_PlannerCrewRequests MR  ON MR.Pr_PersonnelID = PR.Pr_PersonnelID  and abs(datediff(MINUTE,convert(smalldatetime,MR.DateBegin),convert(smalldatetime,dateadd(hh,0,TTT.STD))))<=10 and abs(datediff(MINUTE,convert(smalldatetime,MR.DateEnd),convert(smalldatetime,dateadd(hh,0,TTT.STA))))<=10
					inner  JOIN Ak_PlannerRequestTypes TMR ON  TMR.Ak_PlannerRequestTypeID=MR.Ak_PlannerRequestTypeID
					Where 
					TTT.Bid=1 and MR.Ak_PlannerCrewRequestID is not null and TMR.CodeEng='Bids' and MR.RecordDeleted=0
			   )

	--INNER  JOIN Pr_Personnels PR  ON PR.TableNumber=TT.Tabel and PR.TableNumber>'' and PR.Pr_CategoryTypeID in (1,5) ------------------Только из ЛС Защита от дублирующих Табельных номеров	
	--left  JOIN Ak_PlannerCrewRequests MR  ON MR.Pr_PersonnelID = PR.Pr_PersonnelID  and abs(datediff(MINUTE,convert(smalldatetime,MR.DateBegin),convert(smalldatetime,dateadd(hh,0,TT.STD))))<=10 and abs(datediff(MINUTE,convert(smalldatetime,MR.DateEnd),convert(smalldatetime,dateadd(hh,0,TT.STA))))<=10
	--left  JOIN Ak_PlannerRequestTypes TMR ON TMR.Ak_PlannerRequestTypeID=MR.Ak_PlannerRequestTypeID  and  TMR.CodeEng='Bids'
	--Where 
	--TT.Bid=1 
	--and MR.Ak_PlannerCrewRequestID is  null  	
	----------------------------------------------------------------------
END