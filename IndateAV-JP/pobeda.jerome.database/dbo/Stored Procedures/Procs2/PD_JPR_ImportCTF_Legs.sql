﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ImportCTF_Legs]
@DTbegin datetime,  
@DTEnd datetime,
@NN integer,
@NE integer output,
@RG integer=0
AS
BEGIN

----Declare 
----@DTbegin datetime='2018-04-01 00:00:00',  
----@DTEnd datetime='2018-04-30 23:59:00'


--====================================Таблицы Выход.==============================
Declare @REZCRW Table
(NN integer null,
 STR1 nvarchar(300) null)

--====================================Таблицы Внутр.===============================

------------------------------------------------------------------------
---------------------------------------------------------------------------
DECLARE @PERIOD NVARCHAR(100)  ='PERIOD:'
DECLARE @PLAN_TYPE NVARCHAR(100)='PLAN TYPE: DATED' 
DECLARE @TIME_MODE NVARCHAR(100)='TIME MODE: UTC' 
---------------------------------------------------------------------------
DECLARE @SECTION_CREW NVARCHAR(100)  ='SECTION: CREW'
DECLARE @CREW NVARCHAR(100)='CREW:' 
DECLARE @EOCREW NVARCHAR(100)='EOCREW'
----------------------------------------------------------------------------
DECLARE @EOSECTION NVARCHAR(100)='EOSECTION'
---------------------------------------------
DECLARE @SECTION_PAIRING NVARCHAR(100)  ='SECTION: PAIRING'
DECLARE @PAIRING  NVARCHAR(100)='PAIRING:' 
DECLARE @EOPAIRING NVARCHAR(100)='EOPAIRING' 
----------------------------------------------------- 
DECLARE @SECTION_LEG NVARCHAR(100)  ='SECTION: LEG'
----------------------------------------------------- 
DECLARE @SECTION_GROUND_DUTY NVARCHAR(100)  ='SECTION: GROUND DUTY'
----------------------------------------------------- 
DECLARE @DS nvarchar(1)=' '
DECLARE @STRFlights NVARCHAR(100)=''
DECLARE @CorrierCode nvarchar(2)='DP'
----------------------------------------------------
DECLARE @Duty_code NVARCHAR(10)='*'
DECLARE @Lock_code NVARCHAR(10)='L' 
DECLARE @Environment_code NVARCHAR(10)='1' 
DECLARE @Activity_code NVARCHAR(10)='LV ' 
DECLARE @Activity_attribute NVARCHAR(10)='*' 
DECLARE @Activity_type NVARCHAR(1)='F' 
DECLARE @ActivitySub_type NVARCHAR(1)='L'
DECLARE @Horizontal_lock1 NVARCHAR(1)='X'
DECLARE @Horizontal_lock2 NVARCHAR(1)='N'
DECLARE @Horizontal_lock3 NVARCHAR(1)='L'


Declare
@DT_CRbegin datetime=dateadd(dd,-6, @DTbegin),  
@DT_CREnd datetime=dateadd(dd,12, @DTEnd)

IF (@RG=1)  
	BEGIN
	Set @DT_CRbegin = @DTbegin  
	Set @DT_CREnd = dateadd(dd,12, @DTEnd)
	END

--DP 819 * VKO  CGN  1110 1406  0   Y  20180401 20180401    737
---------------------------------------------
Set @NN=@NN+1
INSERT INTO @REZCRW
select @NN,@SECTION_LEG 
--=================================
Insert Into @REZCRW
Select
@NN+ROW_NUMBER() over (order by Tl.DateTakeoff) ,
Tl.CarComp +' '+
Tl.Flight +' '+
Tl.Suf +' '+
Tl.Nleg +' '+
Tl.Ap +' '+
Tl.Ap1 +' '+
Tl.DTTakeoff +' '+
Tl.DTLanding +' '+
Tl.dN +' '+
Tl.Fl +' '+
Tl.DTB +' '+
Tl.DTE +' '+
Tl.Pln
From
	(Select 
		'DP' CarComp,
		FL.Name Flight,
		'*' Suf,
		'1' Nleg,
		Ap.CodeIATA Ap,
		Ap1.CodeIATA Ap1,
		UPPER(Format(PFTO.DateTakeoff,'HHmm')) DTTakeoff,
		UPPER(Format(PFLA.DateLanding,'HHmm')) DTLanding,
		RTRIM(LTRIM(convert(nvarchar(3),datediff(dd,PFTO.DateTakeoff,PFLA.DateLanding)))) dN,
		'Y' Fl,
		UPPER(Format(PFTO.DateTakeoff,'yyyyMMdd')) DTB,
		UPPER(Format(PFTO.DateTakeoff,'yyyyMMdd')) DTE,
		'73H' Pln,
		PFTO.DateTakeoff
		From Ap_PlanFlights PF
		OUTER APPLY (Select top 1 P.* From Ap_PlanFlightAirPorts P Where P.Ap_PlanFlightID=PF.Ap_PlanFlightID  Order by P.AirPortNumber asc) PFTO
		OUTER APPLY (Select top 1 P.* From Ap_PlanFlightAirPorts P Where P.Ap_PlanFlightID=PF.Ap_PlanFlightID  Order by P.AirPortNumber desc) PFLA
		--LEft JOIN Ap_PlanFlightAirPorts PFTO  ON PFTO.Ap_PlanFlightID=PF.Ap_PlanFlightID 
		--LEft JOIN Ap_PlanFlightAirPorts PFLA ON PFTO.Ap_PlanFlightID=PFLA.Ap_PlanFlightID and PFLA.AirPortNumber=PFTO.AirPortNumber+1
		LEFT JOIN Ap_AirPorts AP ON AP.Ap_AirPortID=PFTO.Ap_AirPortID
		LEFT JOIN Ap_AirPorts AP1 ON AP1.Ap_AirPortID=PFLA.Ap_AirPortID
		LEFT JOIN Fl_Flights FL ON FL.Fl_FlightID=PF.Fl_FlightID
		Where 
		PFTO.DateTakeoff between @DT_CRbegin and @DT_CREnd
		AND PF.OnlyFinanceCalculation = 0
		AND PF.Sh_ScheduleVariantTypeID IS NULL
		and PF.Status & 256 <> 256  ----отмененные рейсы
		AND ISNULL(FL.FlightVariant,0) = 0 
		AND PF.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки	
	) Tl
Order by Tl.DateTakeoff
--==================================
--====================================
SET @NE=(select Top 1 NN From @REZCRW Order by NN DESC)
INSERT INTO @REZCRW
Select @NE+1,'EOSECTION'
Select T.* from @REZCRW T
Order by T.NN
END