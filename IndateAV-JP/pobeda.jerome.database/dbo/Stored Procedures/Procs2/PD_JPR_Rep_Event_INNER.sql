﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_Rep_Event_INNER]

@DateBeginUTC datetime,  
@DateEndUTC datetime
--@TYPECREW integer = 0,   --0 пилоты 1 --БП --11 все
--@Parts integer=0,   -- 0-полный период--1 -Только IN--2 -ТОлько OUT	
--@Sourth integer=0,
--@CRW integer=0   --0--,tp CREW

AS
BEGIN
--=================================Для тестирования=======================================
Declare 
----@DTbegin datetime='2018-10-01 00:00:00',  
----@DTEnd datetime=  '2018-10-31 23:59:00',
@TYPECREW integer =11,   --0 пилоты 1 --БП --11 все
@Sourth integer=0,
@Parts integer=0,   -- 0-полный период
					 --1 -Только IN
					--2 -ТОлько OUT	
@CRW integer=0   --0--,tp CREW--1 c CREW	

SET @TYPECREW=11

Declare
@DT_CRbegin datetime=dateadd(dd,-31, @DateBeginUTC),  
@DT_CREnd datetime=dateadd(dd,+31, @DateEndUTC),
@NN integer=0
---------------------------------------------------------
Declare @TBLPER Table
(
Pr_PersonnelID integer,
TableNumber nvarchar(10),
MainFunc nvarchar(5),
SubFunc nvarchar(5)
)
Declare @TBLACT Table
(
Pr_EventID integer UNIQUE (Pr_EventID),
Pr_PersonnelID integer UNIQUE (Pr_EventID,Pr_PersonnelID),
Pr_EventTypeID integer,
TableNumber nvarchar(10) UNIQUE (Pr_EventID,TableNumber),
CodeEng nvarchar(10),
DateBegin datetime UNIQUE (Pr_EventID, DateBegin),
DateEnd datetime UNIQUE (Pr_EventID, DateEnd),
AP nvarchar(3)
)

Declare @TBLGRN Table
(
Pr_EventID integer UNIQUE (Pr_EventID),
Pr_PersonnelID integer UNIQUE (Pr_EventID,Pr_PersonnelID),
Pr_EventTypeID integer,
TableNumber nvarchar(10) UNIQUE (Pr_EventID,TableNumber),
CodeEng nvarchar(10),
DateBegin datetime UNIQUE (Pr_EventID, DateBegin),
DateEnd datetime UNIQUE (Pr_EventID, DateEnd),
AP nvarchar(3),
MainFunc nvarchar(5)
)
--==============================================================================================================================
--===================================Отбор персональных Данных===========================================
--==============================================================================================================================
INSERT INTO @TBLPER
Select 
*
From
(Select 
	PR.Pr_PersonnelID,
	-----------------------------------
	replace(Pr.TableNumber,'/','00')  TableNumber,  
	-----------------------------------
	  Case
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI','CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD')  then 'CP' --,'SI' 
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст') then 'FO'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
		else''
	End  MainFunc,
	---------------------------------------------------------------------------------------
	Case
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI','CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD' )  then 'CP' --,'SI'
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст') then 'FO'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
		else''
	End  SndFunc
	---------------------------------------------------------------------------------------
	From Pr_Personnels PR
	OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DT_CREnd >= T.DateBegin) and (@DT_CRbegin<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
	LEft JOIN Pr_StaffTrees STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
	left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
	left JOIN  Ap_AirPorts AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
	OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=Pr.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DT_CREnd>= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
	OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=Pr.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
	OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DT_CREnd) post 
	Where 
	(( post.DateEnd IS NULL OR post.DateEnd > @DT_CRbegin) )
	and isnumeric(replace(Pr.TableNumber,'/','00')) >''
	and (Pr.Pr_CategoryTypeID in (1,5))
	and case 
	when @TYPECREW=0 and PR.Pr_CategoryTypeID =1 then 1 
	when @TYPECREW=1 and PR.Pr_CategoryTypeID =5 then 1
	when @TYPECREW=11 and PR.Pr_CategoryTypeID in(1,5) then 1
	else 0
	end = 1
) tt where TT.MainFunc>'' 
ORDER BY TT.TableNumber
--==============================================================================================================================
--===================================Отбор персональных DUTY (не путать GROUND DUTY)============================================
--==============================================================================================================================
INSERT INTO  @TBLACT 
SELECT 
EV.Pr_EventID,
PR.Pr_PersonnelID,
TP.Pr_EventTypeID,
replace(Pr.TableNumber,'/','00') TableNumber,
--TP.Name,
TP.CodeEng,
dateadd(hh,-3,EV.DateBegin),
dateadd(hh,-3,EV.DateEnd),
--EV.Comment,
Case
when charindex('СКОЛКОВО',EV.Comment,1)>0 then 'VKO'
when charindex('Аэрофлот',EV.Comment,1)>0 then 'SVO'
when charindex('Санкт-',EV.Comment,1)>0 then 'LED'
when charindex('Офис',EV.Comment,1)>0 then 'VKO'
when charindex('Амстердам',EV.Comment,1)>0 then 'AMS'
else 'VKO'
End AP

FROM Pr_Events EV
inner join Pr_EventTypes TP on TP.Pr_EventTypeID=EV.Pr_EventTypeID
inner join Pr_Personnels PR on PR.Pr_PersonnelID=EV.Pr_PersonnelID
inner join @TBLPER T on T.TableNumber=Pr.TableNumber
Where TP.CodeEng in  ('OFF','WOF','ROF','SICK','ULVE','RLVE','MLVE','LVE','LLAB','OFFW','MED')
and convert(date,EV.DateBegin) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
and  isnumeric(replace(Pr.TableNumber,'/','00')) >''
--=================================================================================================
--===================================Отбор GROUND DUTY ============================================
--=================================================================================================
INSERT INTO  @TBLGRN 
SELECT 
EV.Pr_EventID,
PR.Pr_PersonnelID,
TP.Pr_EventTypeID,
replace(Pr.TableNumber,'/','00') TableNumber,
TP.CodeEng,
dateadd(hh,-3,EV.DateBegin),
dateadd(hh,-3,EV.DateEnd),
Case
when charindex('СКОЛКОВО',EV.Comment,1)>0 then 'VKO'
when charindex('Аэрофлот',EV.Comment,1)>0 then 'SVO'
when charindex('Санкт-',EV.Comment,1)>0 then 'LED'
when charindex('Офис',EV.Comment,1)>0 then 'VKO'
when charindex('Амстердам',EV.Comment,1)>0 then 'AMS'
else 'VKO'
End AP,
T.MainFunc

FROM Pr_Events EV
inner join Pr_EventTypes TP on TP.Pr_EventTypeID=EV.Pr_EventTypeID
inner join Pr_Personnels PR on PR.Pr_PersonnelID=EV.Pr_PersonnelID
inner join @TBLPER T on T.TableNumber=Pr.TableNumber
Where TP.CodeEng in ('HR','AVL','HBY','INIT','SCT','DIFF','ICPT','IIT','FFS','IFFS','FFSO','IFFSO','TSC','ENGLV','RDG','SEC','EPG','EPW','CRM','RST','RCC','TRC','IRC')
and convert(date,EV.DateBegin) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
and  isnumeric(replace(Pr.TableNumber,'/','00')) >''
--=================================================================================================



Select 
--FST.*,
--FST.Pr_PersonnelID ,
Pr.LastName+ ' ' +PR.FirstName+' '+ PR.MiddleName   FIO,
PP.MainFunc,
FST.TableNumber ,
FST.CodeEng ,
FST.DateBegin ,
FST.DateEnd ,
--SCD.*
-----------------------
SCD.CodeEng CodeEng1,
SCD.DateBegin DateBegin1,
SCD.DateEnd  DateEnd1
From (Select Pr_EventID ,Pr_PersonnelID ,Pr_EventTypeID ,TableNumber ,CodeEng ,DateBegin ,DateEnd,AP  
	  From (select Pr_EventID ,Pr_PersonnelID ,Pr_EventTypeID ,TableNumber ,CodeEng ,DateBegin ,DateEnd,AP  
			From @TBLACT
			UNION
			select Pr_EventID ,Pr_PersonnelID ,Pr_EventTypeID ,TableNumber ,CodeEng ,DateBegin ,DateEnd ,AP 
			From @TBLGRN
		  ) FF
     ) FST
INNER JOIN  Pr_Personnels PR on PR.Pr_PersonnelID=FST.Pr_PersonnelID
INNER JOIN @TBLPER PP on PP.Pr_PersonnelID=FST.Pr_PersonnelID
Left join  (Select Pr_EventID ,Pr_PersonnelID ,Pr_EventTypeID ,TableNumber ,CodeEng ,DateBegin ,DateEnd,AP  
			From (select Pr_EventID ,Pr_PersonnelID ,Pr_EventTypeID ,TableNumber ,CodeEng ,DateBegin ,DateEnd,AP  
					From @TBLACT
					UNION
					select Pr_EventID ,Pr_PersonnelID ,Pr_EventTypeID ,TableNumber ,CodeEng ,DateBegin ,DateEnd ,AP 
					From @TBLGRN
				  ) FF
		   ) SCD ON SCD.Pr_PersonnelID=FST.Pr_PersonnelID
				and ((FST.DateEnd between SCD.DateBegin and SCD.DateEnd) or (FST.DateBegin between SCD.DateBegin and SCD.DateEnd) )
				and FST.Pr_EventID<>SCD.Pr_EventID
Where SCD.Pr_EventID is not null
--=================================================================================================
End