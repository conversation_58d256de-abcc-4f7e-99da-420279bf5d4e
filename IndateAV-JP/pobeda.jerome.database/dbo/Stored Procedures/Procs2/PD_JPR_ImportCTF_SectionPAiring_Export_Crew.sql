﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ImportCTF_SectionPAiring_Export_Crew]
@DTbegin datetime='2018-08-01 00:00:00',  
@DTEnd datetime='2018-08-31 23:59:00',
@TYPECREW integer = 0,   --0 пилоты 1 --БП
@Parts integer=0,   -- 0-полный период--1 -Только IN--2 -ТОлько OUT	
@Sourth integer=0,
@CRW integer=0   --0--,tp CREW
AS
BEGIN


--=================================Для тестирования=======================================
------Declare 
------@DTbegin datetime='2018-09-10 00:00:00',  
------@DTEnd datetime=  '2018-09-17 23:59:00',
------@TYPECREW integer = 0,   --0 пилоты 1 --БП
------@Parts integer=0,   -- 0-полный период
------					--1 -Только IN
------					---2 -ТОлько OUT	
------@CRW integer=1   --0--,tp CREW--1 c CREW	
--=========================================================================================
--=========================================================================================

--=========================================================================================
--====================================Таблицы Выход.=======================================
Declare @REZCRW Table
(NN integer null,
 STR1 nvarchar(300) null)
--==========================================================================================
--====================================Таблицы Внутр.========================================
DECLARE @TBTPAR table
(
Beg nvarchar(3),
Bend nvarchar(3),
Define integer,
FlightTaskReestrID integer,
PlanFlightAirPortCrewID integer,
Tabel nvarchar(10),
TehNumber integer,
AP_BASE_ID integer,
DateTakeoff Datetime,
F_CodeIATA nvarchar(5),
F_Ap_AirPortID integer,
F_Name nvarchar(5),
DateLanding Datetime,
L_Ap_AirPortID integer,
L_CodeIATA nvarchar(5),
L_Name nvarchar(5),
TYPE_CREW nvarchar(30),
CrewType integer
)

Declare @TBTSK Table
(
	FlightTaskReestrID integer null,
	Ap_PlanFlightAirPortCrewID integer null,
	Tabel nvarchar(10),
	TehNumber integer,
	Bap integer,
	CrewType integer
)

Declare @TVLEG table
(
FlightTaskReestrID integer,
Ap_PlanFlightAirPortCrewID integer,
Tabel nvarchar(10),
TehNumber integer,
AP_BASE_ID integer,
ff integer,
DateTakeoff Datetime,
F_CodeIATA nvarchar(5),
F_Ap_AirPortID integer,
F_Name nvarchar(5),
DateLanding Datetime,
L_Ap_AirPortID integer,
L_CodeIATA nvarchar(5),
L_Name nvarchar(5),
CrewType integer
)

DEclare @TVLEGS Table
(
DateTakeoff datetime,
AP_TO nvarchar(5),
FlightName nvarchar(10),
DateLanding Datetime,
AP_LA nvarchar(5),
Pr_FlightTaskReestrID integer,
fl integer,
Ap_PlanFlightAirPortID integer
)

Declare @TBLPER Table
(
TableNumber nvarchar(10),
MainFunc nvarchar(5),
SubFunc nvarchar(5)
)

Declare @TBLGRN Table
(
Pr_EventID integer,
Pr_PersonnelID integer,
Pr_EventTypeID integer,
TableNumber nvarchar(10),
CodeEng nvarchar(10),
DateBegin datetime,
DateEnd datetime,
AP nvarchar(3)
)

Declare @TBLGRN_1 Table
(
Pr_EventID integer,
Pr_PersonnelID integer,
Pr_EventTypeID integer,
TableNumber nvarchar(10),
CodeEng nvarchar(10),
DateBegin datetime,
DateEnd datetime,
AP nvarchar(3),
MainFunc nvarchar(5)
)
DECLARE
@Pr_EventID integer,
@DT_Pr_PersonnelID integer,
@DT_Pr_EventTypeID integer,
@DT_TableNumber nvarchar(10),
@DT_CodeEng nvarchar(10),
@DT_DateBegin datetime,
@DT_DateEnd datetime,
@DT_AP nvarchar(3),
@MainFunc nvarchar(5),
@CounPer integer,
@CP_cnt integer,
@FO_cnt integer,
@SCC_cnt integer,
@CC_cnt integer
------------------------------------------------------------------------
---------------------------------------------------------------------------
DECLARE @PERIOD NVARCHAR(100)  ='PERIOD:'
DECLARE @PLAN_TYPE NVARCHAR(100)='PLAN TYPE: DATED' 
DECLARE @TIME_MODE NVARCHAR(100)='TIME MODE: UTC' 
---------------------------------------------------------------------------
DECLARE @SECTION_CREW NVARCHAR(100)  ='SECTION: CREW'
DECLARE @CREW NVARCHAR(100)='CREW:' 
DECLARE @EOCREW NVARCHAR(100)='EOCREW'
----------------------------------------------------------------------------
DECLARE @EOSECTION NVARCHAR(100)='EOSECTION'
---------------------------------------------
DECLARE @SECTION_PAIRING NVARCHAR(100)  ='SECTION: PAIRING'
DECLARE @PAIRING  NVARCHAR(100)='PAIRING:' 
DECLARE @EOPAIRING NVARCHAR(100)='EOPAIRING' 
----------------------------------------------------- 
DECLARE @SECTION_LEG NVARCHAR(100)  ='SECTION: LEG'
----------------------------------------------------- 
DECLARE @SECTION_GROUND_DUTY NVARCHAR(100)  ='SECTION: GROUND DUTY'
----------------------------------------------------- 
DECLARE @DS nvarchar(1)=' '
DECLARE @STRFlights NVARCHAR(100)=''
DECLARE @CorrierCode nvarchar(2)='DP'
----------------------------------------------------
DECLARE @Duty_code NVARCHAR(10)='*'
DECLARE @Lock_code NVARCHAR(10)='L' 
DECLARE @Environment_code NVARCHAR(10)='1' 
DECLARE @Activity_code NVARCHAR(10)='LV ' 
DECLARE @Activity_attribute NVARCHAR(10)='*' 
DECLARE @Activity_type NVARCHAR(1)='F' 
DECLARE @ActivitySub_type NVARCHAR(1)='L'
DECLARE @ActivitySub_typeDH NVARCHAR(1)='D'
DECLARE @Horizontal_lock1 NVARCHAR(1)='X'
DECLARE @Horizontal_lock2 NVARCHAR(1)='N'
DECLARE @Horizontal_lock3 NVARCHAR(1)='L'
DECLARE @FLIGHTCREW nvarchar(15)='1/1/0/0/0/0/0 '
DECLARE @CABINCREW nvarchar(15)='0/0/0/1/0/3/0 '


Declare
@DT_CRbegin datetime=dateadd(dd,-31, @DTbegin),  
@DT_CREnd datetime=dateadd(dd,+12, @DTEnd),
@CREWSTR nvarchar(15)='',
@NNduty integer
 
If @TYPECREW=1  Set @CREWSTR=@FLIGHTCREW
ELSE Set @CREWSTR=@CABINCREW

-----------------------------------------
--Select @DT_CRbegin, @DT_CREnd 
---------------------------------------------
--======================================================================================
--================отбор всех Person по службе @TYPECREW=================================
--======================================================================================
--======================================================================================

INSERT INTO @TBLPER
Select ---DISTINCT
	-------------------------------------
	PR.TableNumber,  
	-------------------------------------
	  Case
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'SI','CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD')  then 'CP' --,'SI' 
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст') then 'FO'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
		else''
	End  MainFunc,
	-----------------------------------------------------------------------------------------
	Case
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI', 'SI','CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD' )  then 'CP' --,'SI'
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст') then 'FO'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
		else''
	End  SndFunc
	-----------------------------------------------------------------------------------------
	From Pr_Personnels PR
	LEft JOIN Pr_PersPosts PST on PST.Pr_PersonnelID=PR.Pr_PersonnelID
	LEft JOIN Pr_StaffTrees STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
	left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
	left JOIN  Ap_AirPorts AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
	OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and getdate() >= Pr_Orders.DateBegin order by Pr_Orders.Datebegin Desc) ORD1 
	OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
	Where 
	getdate() >= ORD1.DateBegin and (getdate() <= ORD1.DateFinish or ORD1.DateFinish  is null)
	and (getdate() <= ORD2.DateFinish or ORD2.DateFinish  is null)
	and PR.TableNumber >''
	and (getdate() >= PST.DateBegin and (getdate() <=PST.DateEnd or PST.DateEnd is null))
	and case when @TYPECREW=0 then 1 when @TYPECREW=1 then 5 end =PR.Pr_CategoryTypeID 
	--and  PR.LastNameEng > ''
	ORDER BY PR.Pr_CategoryTypeID, PR.TableNumber
--======================================================================================
--================Завершение отбора всех Person по службе @TYPECREW=====================
--======================================================================================
--
--==============================================================================================================================
--===================================Отбор персональных DUTY (не путать GROUND DUTY)============================================
--==============================================================================================================================
INSERT INTO  @TBLGRN 
SELECT 
EV.Pr_EventID,
PR.Pr_PersonnelID,
TP.Pr_EventTypeID,
PR.TableNumber,
--TP.Name,
TP.CodeEng,
dateadd(hh,-3,EV.DateBegin),
dateadd(hh,-3,EV.DateEnd),
--EV.Comment,
Case
when charindex('СКОЛКОВО',EV.Comment,1)>0 then 'VKO'
when charindex('Аэрофлот',EV.Comment,1)>0 then 'SVO'
when charindex('Санкт-',EV.Comment,1)>0 then 'LED'
when charindex('Офис',EV.Comment,1)>0 then 'VKO'
when charindex('Амстердам',EV.Comment,1)>0 then 'AMS'
else 'VKO'
End AP

FROM Pr_Events EV
inner join Pr_EventTypes TP on TP.Pr_EventTypeID=EV.Pr_EventTypeID
inner join Pr_Personnels PR on PR.Pr_PersonnelID=EV.Pr_PersonnelID
inner join @TBLPER T on T.TableNumber=Pr.TableNumber
Where TP.CodeEng in  ('OFF','WOF','ROF','SICK','ULVE','RLVE','MLVE','LVE','LLAB','OFFW','MED')
and EV.RecordDeleted=0
and convert(date,EV.DateBegin) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
--==============================================================================================================================
--===================================Завершение Отбор персональных DUTY (не путать GROUND DUTY)=================================
--==============================================================================================================================

--=================================================================================================
--===================================Отбор GROUND DUTY ============================================
--=================================================================================================
INSERT INTO  @TBLGRN_1 
SELECT 
EV.Pr_EventID,
PR.Pr_PersonnelID,
TP.Pr_EventTypeID,
PR.TableNumber,
TP.CodeEng,
dateadd(hh,-3,EV.DateBegin),
dateadd(hh,-3,EV.DateEnd),
Case
when charindex('СКОЛКОВО',EV.Comment,1)>0 then 'VKO'
when charindex('Аэрофлот',EV.Comment,1)>0 then 'SVO'
when charindex('Санкт-',EV.Comment,1)>0 then 'LED'
when charindex('Офис',EV.Comment,1)>0 then 'VKO'
when charindex('Амстердам',EV.Comment,1)>0 then 'AMS'
else 'VKO'
End AP,
T.MainFunc

FROM Pr_Events EV
inner join Pr_EventTypes TP on TP.Pr_EventTypeID=EV.Pr_EventTypeID
inner join Pr_Personnels PR on PR.Pr_PersonnelID=EV.Pr_PersonnelID
inner join @TBLPER T on T.TableNumber=Pr.TableNumber
Where TP.CodeEng in ('HR','AVL','HBY','INIT','SCT','DIFF','ICPT','IIT','FFS','IFFS','FFSO','IFFSO','TSC','ENGLV','RDG','SEC','EPG','EPW','CRM','RST','RCC','TRC','IRC')
--('SICK', 'OFF', 'ROF',  'WOF','EPW','EPG','CRM','SEC','ENGLV','IRC','RDG','TRC','RST','IIT','ICPT','DIFF','LVE','RLVE','MLVE','ULVE','HR','MED','IFFS','FFS','IFFSO','FFSO','OFFW','LLAB','INIT','SCT','HBY')
and EV.RecordDeleted=0
and convert(date,EV.DateBegin) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
--=================================================================================================
---select * From @TBLGRN_1
--=================================================================================================
--===================================Завершение Отбора GROUND DUTY=================================
--=================================================================================================


--======================================================================================
--================Формирование данных по LEGS===========================================
--======================================================================================
INSERT INTO @TVLEGS
Select DISTINCT 
PFTO.DateTakeoff,
AP_TO.CodeIATA AP_TO,
FL.Name,
PFLA.DateLanding,
AP_LA.CodeIATA AP_LA,
TSK.Pr_FlightTaskReestrID,
0,
PFTO.Ap_PlanFlightAirPortID
From Pr_FlightTaskReestrs TSK 
INNER JOIN  Ap_PlanFlightAirPorts PFTO ON PFTO.Pr_FlightTaskReestrID = TSK.Pr_FlightTaskReestrID and PFTO.AirPortNumber=1
OUTER APPLY (Select top 1 P.* From Ap_PlanFlightAirPorts P Where P.Ap_PlanFlightID=PFTO.Ap_PlanFlightID  Order by P.AirPortNumber desc) PFLA  ----
INNER JOIN  Ap_PlanFlights PFL ON PFTO.Ap_PlanFlightID=PFL.Ap_PlanFlightID
LEFT JOIN   Fl_Flights		FL ON PFL.Fl_FlightID=FL.Fl_FlightID
LEFT JOIN   Ap_Airports AP_TO  ON PFTO.Ap_AirportID = AP_TO.Ap_AirportID
LEFT JOIN   Ap_Airports AP_LA  ON PFLA.Ap_AirportID = AP_LA.Ap_AirportID
Where 
convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
AND PFL.OnlyFinanceCalculation = 0
AND PFL.Sh_ScheduleVariantTypeID IS NULL
and PFL.Status & 256 <> 256  ----отмененные рейсы
AND ISNULL(FL.FlightVariant,0) = 0 
AND PFL.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки
--======================================================================================
--================Завершение Формирования данных по LEGS================================
--======================================================================================


--====================================================================================================
--=======================Формирование полетных заданий================================================
--=====================================================================================================
Insert INTO @TBTSK
Select	DISTINCT	
		PFA.Pr_FlightTaskReestrID,
		isnull(PR.Pr_PersonnelID,1)  Ap_PlanFlightAirPortCrewID,--isnull(PR.Pr_PersonnelID,1) Ap_PlanFlightAirPortCrewID,
		isnull(PR.TableNumber,'1') Tabel,
		isnull(PFC.OrderNumber,0) TehNumber, 
		MAX(isnull(PTR.Ap_AirPortID,58)) BAP,  --PTR.Ap_AirPortID
		isnull(PFC.CrewType,11)
	FROM Ap_PlanFlights PFL
		INNER JOIN Ap_PlanFlightAirPorts PFA ON PFA.Ap_PlanFlightID = PFL.Ap_PlanFlightID and PFA.AirPortNumber=1
		INNER JOIN Fl_Flights			   FL ON PFL.Fl_FlightID=FL.Fl_FlightID
		LEFT JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
		LEFT JOIN Pr_Personnels PR on  PFC.Pr_PersonnelID=PR.Pr_PersonnelID
		LEFT JOIN Pr_PersPosts Post ON PR.Pr_PersonnelID = Post.Pr_PersonnelID AND (Post.DateBegin < getutcdate() and (Post.DateEnd is null or Post.DateEnd > getutcdate()))
		LEFT JOIN Pr_StaffTrees TR ON Post.Pr_StaffTreeID = TR.Pr_StaffTreeID  
		LEFT  JOIN Pr_StaffTrees PTR ON PTR.Pr_StaffTreeID = TR.Pr_StaffTreeIDParent 
		--LEFT JOIN Pr_ArmChairTypes ON PFC.Pr_ArmChairTypeID = Pr_ArmChairTypes.Pr_ArmChairTypeID 
		--LEFT JOIN Pr_ArmChairRoles ON Pr_ArmChairRoles.Pr_ArmChairRoleID = PFC.Pr_ArmChairRoleID 
	WHERE 
		convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
		AND PFL.OnlyFinanceCalculation = 0
		AND PFL.Sh_ScheduleVariantTypeID IS NULL
		and PFL.Status & 256 <> 256						 ----отмененные рейсы
		AND ISNULL(FL.FlightVariant,0) = 0 
		AND PFL.Fl_FlightMeanTypeID  IN (30)			 --Только пассажирские перевозки
		------------------------------------------------------------------
		and
		( 
			(PFC.CrewType=@TYPECREW) and (@TYPECREW=0) and (PFC.OrderNumber in (1,2,3)) and (PR.Pr_CategoryTypeID=1) ----указываем позицию АВИАБИТ
			or 
			(PFC.CrewType=@TYPECREW) and (@TYPECREW=1) and (PFC.OrderNumber in (1,2,3,4,5,6,7)) and (PR.Pr_CategoryTypeID=5) ----указываем позицию АВИАБИТ
			----------------------------------------- учет пассажиров в экипаже ------------------------------------------------------------------------------
			or
			(PFC.CrewType=4) and (PFC.Pr_ArmChairTypeID is null) and (@TYPECREW=0) and (PR.Pr_CategoryTypeID=1) ----указываем позицию АВИАБИТ пассажир летный and (PFC.Pr_ArmChairTypeID is null) 
			or
			(PFC.CrewType=4) and (PFC.Pr_ArmChairTypeID is null)  and (@TYPECREW=1) and (PR.Pr_CategoryTypeID=5) ----указываем позицию АВИАБИТ пассажир кабинныйand (PFC.Pr_ArmChairTypeID is null)
			---------------------------------------------------------------------------------------------------------------------------------------------------
		)
    GROUP BY  
	PFA.Pr_FlightTaskReestrID, 
	isnull(PR.Pr_PersonnelID,1), 
	isnull(PR.TableNumber,'1'), 
	isnull(PFC.OrderNumber,0), 
	isnull(PFC.CrewType,11)          --,  isnull(PR.Pr_PersonnelID,1)  , PTR.Ap_AirPortID --, --, PTR.Ap_AirPortID   ,isnull(PR.Pr_PersonnelID,1)

----================================================================================================================
---select * From @TBTSK T 
--====================================================================================================
--=======================Завершение Формирования полетных заданий======================================
--=====================================================================================================


--==================================================================================================================
--=====================Формирование Данных по первому и последнему рейса в  Полетном задании========================
--==================================================================================================================
INSERT INTO @TVLEG
Select 
T.FlightTaskReestrID,
T.Ap_PlanFlightAirPortCrewID,
T.Tabel,
T.TehNumber,
ISNULL(T.Bap,58),
Case ISNULL(T.Bap,58)
-----------------------Определение флагов включения рейса в один TRIP ------------------------------------
WHEN 58 then ---VKO
		Case
		When (FR.CodeIATA='VKO' ) and (LR.CodeIATA='VKO' )		then 0 --BB вылет прилет из базы в базу
		When (FR.CodeIATA='VKO' ) and (LR.CodeIATA<>'VKO')		then 1 --BN
		When (FR.CodeIATA<>'VKO') and (LR.CodeIATA<>'VKO')		then 2 --NN
		When (FR.CodeIATA<>'VKO') and (LR.CodeIATA='VKO' )		then 3 --NB
		End
WHEN 86 then ---LED
		Case
		When (FR.CodeIATA='LED')  and (LR.CodeIATA='LED' )		then 0
		When (FR.CodeIATA='LED')  and (LR.CodeIATA<>'LED')		then 1
		When (FR.CodeIATA<>'LED') and (LR.CodeIATA<>'LED')		then 2
		When (FR.CodeIATA<>'LED') and (LR.CodeIATA='LED' )		then 3
		End
WHEN 77 then ---KJA
		Case
		When (FR.CodeIATA='KJA')    and (LR.CodeIATA='KJA' )    then 0
		When (FR.CodeIATA='KJA')    and (LR.CodeIATA<>'KJA')	then 1
		When (FR.CodeIATA<>'KJA')   and (LR.CodeIATA<>'KJA')	then 2
		When (FR.CodeIATA<>'KJA')   and (LR.CodeIATA='KJA' )    then 3
		End
WHEN 6 then ---KRR
		Case
		When (FR.CodeIATA='KRR')    and (LR.CodeIATA='KRR' )    then 0
		When (FR.CodeIATA='KRR')    and (LR.CodeIATA<>'KRR')	then 1
		When (FR.CodeIATA<>'KRR')	and (LR.CodeIATA<>'KRR')	then 2
		When (FR.CodeIATA<>'KRR')	and (LR.CodeIATA='KRR' )    then 3
		End
ELSE 
		Case
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3
		End
END ff,
----------------------------------------------------------------------------------------------
FR.DateTakeoff,
FR.CodeIATA,
FR.Ap_AirPortID,
FR.Name,
LR.DateLanding,
LR.Ap_AirPortID,
LR.CodeIATA,
LR.Name,
T.CrewType
From @TBTSK T
OUTER APPLY (select top 1
			 PFA.DateTakeoff,
			 FL.NAME,
			 PRT.Ap_AirPortID,
			 PRT.CodeIATA	
			 From Ap_PlanFlightAirPorts PFA
			 INNER JOIN Ap_PlanFlights PFL ON PFL.Ap_PlanFlightID=PFA.Ap_PlanFlightID
			 INNER JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
			 INNER JOIN Fl_Flights     FL  ON PFL.Fl_FlightID=FL.Fl_FlightID
			 INNER JOIN Ap_AirPorts    PRT  ON PRT.Ap_AirPortID=PFA.Ap_AirPortID
			 WHERE 
			 PFA.AirPortNumber=1
			 and PFC.Pr_PersonnelID=T.Ap_PlanFlightAirPortCrewID
			 and T.FlightTaskReestrID=PFA.Pr_FlightTaskReestrID
			 ORDER BY PFA.DateTakeoff ASC
			 ) FR
OUTER APPLY (select top 1
			 PFA1.DateLanding,
			 FL.NAME,
			 PRT.Ap_AirPortID,
			 PRT.CodeIATA	
			 From Ap_PlanFlightAirPorts PFA
			 OUTER APPLY (Select top 1 P.* From Ap_PlanFlightAirPorts P Where P.Ap_PlanFlightID=PFA.Ap_PlanFlightID and P.DateLanding  is not null Order by P.AirPortNumber desc) PFA1
			 INNER JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
			 INNER JOIN Ap_PlanFlights PFL ON PFL.Ap_PlanFlightID=PFA.Ap_PlanFlightID
			 INNER JOIN Fl_Flights     FL  ON PFL.Fl_FlightID=FL.Fl_FlightID
			 INNER JOIN Ap_AirPorts    PRT  ON PRT.Ap_AirPortID=PFA1.Ap_AirPortID
			 WHERE 
			 PFA.AirPortNumber=1
			 and PFC.Pr_PersonnelID=T.Ap_PlanFlightAirPortCrewID
			 and T.FlightTaskReestrID=PFA.Pr_FlightTaskReestrID
			 ORDER BY PFA1.DateLanding DESC
			 ) LR
Where FlightTaskReestrID is not null
ORDER BY 
FR.DateTakeoff,
T.Ap_PlanFlightAirPortCrewID,
FR.CodeIATA
--==================================================================================================================
----Select * From @TVLEG 
--==================================================================================================================
--=====================Завершение Формирования Данных по первому и последнему рейса в  Полетном задании=============
--==================================================================================================================

----==================================================================================
----================================Заголовок CTF======================================
DECLARE 
@NN integer=0,
@STR NVARCHAR(max)='',
@STR1 NVARCHAR(max)=''
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @PERIOD+' '+Convert(nvarchar(10),@DT_CRbegin,112)+' - '+Convert(nvarchar(10),@DTEnd,112))
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @PLAN_TYPE)
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @TIME_MODE)
Set @NN=@NN+1
----==================================================================================
----==================================================================================

DECLARE @CURFOOD CURSOR
DECLARE @CURLEG CURSOR

DECLARE 
@BeginParing integer=0,
@FlightTaskReestrID integer,
@PlanFlightAirPortCrewID integer,
@AP_BASE_ID integer,
@ff integer,
@DateTakeoff Datetime,
@F_CodeIATA nvarchar(5),
@F_Ap_AirPortID integer,
@F_Name nvarchar(5),
@DateLanding Datetime,
@L_Ap_AirPortID integer,
@L_CodeIATA nvarchar(5),
@L_Name nvarchar(5),
@Beg nvarchar(3),
@Bend nvarchar(3),
@Define integer,
-------------------
@Nrow integer,
@Nleg integer,
@NrowBegin integer,
@TO_DateTakeoff Datetime,
@TO_CodeIATA nvarchar(5),
@LegName nvarchar(5),
@LA_DateLanding DateTime,
@LA_CodeIATA  nvarchar(5),
@Tabel nvarchar(10),
@TehNumber integer,
@TYPE_CREW nvarchar(30),
@CrewType integer
------------------------------
Declare
@FlightTaskReestrID_1 integer,
@TehNumber_1 integer,
@TYPE_CREW_1 nvarchar(30)
------------------------------------
Declare
@GRDateBegin datetime,
@GRDateEnd Datetime,
@GRCodeEng nvarchar(10),
@GR@AP nvarchar(10)

--=========================================
---Select * From @TVLEG
--=========================================

--=================================================================================================
--=================================================================================================
--===============================Формирование (ТРИПОВ) TBLPAR  для CREW секции==============================
--=================================================================================================
IF @CRW=1
BEGIN
		--=============================== Начало Курсора  CREW1  ================================
		SET @CURFOOD  = CURSOR SCROLL
		FOR 
		---------
		Select
		TB.Beg,
		TB.Bend,
		TB.Define,
		TB.FlightTaskReestrID ,
		TB.Ap_PlanFlightAirPortCrewID,
		TB.Tabel,
		TB.TehNumber,
		TB.AP_BASE_ID,
		TB.DateTakeoff ,
		TB.F_CodeIATA ,
		TB.F_Ap_AirPortID ,
		TB.F_Name ,
		TB.DateLanding ,
		TB.L_Ap_AirPortID ,
		TB.L_CodeIATA ,
		TB.L_Name,
		TB.CrewType
		From (	Select 
				FlightTaskReestrID ,
				Ap_PlanFlightAirPortCrewID,
				Tabel,
				TehNumber,
				ROW_NUMBER() OVER(PARTITION BY Ap_PlanFlightAirPortCrewID Order by FlightTaskReestrID) Define,
				AP_BASE_ID ,
				isnull(H.HFF,-1) HFF,
				ff ,
				isnull(L.HLL,-1) HLL,
				Case
				When ff=0 then 'Beg'
				When ff=1 then 'Beg'
				When hff in (1,2) and ff=2  then ''
				When hff not in (1,2) and ff=2 then 'Beg'
				When hff not in (1,2) and ff=3 then 'Beg'
				end Beg,
				Case
				When ff=0 then 'End'
				When ff=1 and hll not in (2,3) then 'End'
				When ff=2 and hll in (2,3) then ''
				When ff=2 and hll not in (2,3) then 'End'
				When ff=3 then 'End'
				end Bend,
				DateTakeoff ,
				F_CodeIATA ,
				F_Ap_AirPortID ,
				F_Name ,
				DateLanding ,
				L_Ap_AirPortID ,
				L_CodeIATA ,
				L_Name,
				CrewType
					From @TVLEG T
					OUTER APPLY (Select top 1 
									FF HFF 
									From @TVLEG F 
									Where 
									F.DateTakeoff<T.DateTakeoff 
									and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
									Order by F.DateTakeoff Desc 
								) H
					OUTER APPLY (Select top 1 
									FF HLL 
									From @TVLEG F 
									Where 
									F.DateTakeoff>T.DateTakeoff 
									and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
									Order by F.DateTakeoff ASC 
								) L
					WHere
					((
						(@Parts=0 or @Parts=1)
						and 
						-----------------CaringIN-----------------------------------------------------------
						--=================================================================================
						Case
						When ff=0 or ff=1 then T.DateTakeoff
						When Ff=2 or ff=3 then (Select top 1 
												F.DateTakeoff 
												From @TVLEG F 
												Where 
												F.DateTakeoff<T.DateTakeoff 
												and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
												and F.ff=1 
												Order by F.DateTakeoff Desc 
												) 
						----------------------------------------------
						End between @DT_CRbegin and @DTbegin
						----------------------------------------------
						and 
						----------------------------------------------
						(
							Case
							When ff=0 or ff=3 then dateadd(hh,24,T.DateLanding)
							When Ff=2 or ff=1 then dateadd(hh,24,(Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff>T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=3 
													Order by F.DateTakeoff ASC 
													) )
							End  between @DT_CRbegin and @DTbegin
							or
							Case
							When ff=0 or ff=3 then dateadd(hh,24,T.DateLanding)
							When Ff=2 or ff=1 then dateadd(hh,24,(Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff>T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=3 
													Order by F.DateTakeoff ASC 
													) )
							End  >= @DTbegin
						)
						----------------------------------------------------------
						and t.FlightTaskReestrID is not null
					)
						or
						-----------------------Plan Period--------------
						--=================================================================================
					(
						(@Parts=0)
						and 
						Case
						When ff=0 or ff=1 then T.DateTakeoff
						When Ff=2 or ff=3 then (Select top 1 
												F.DateTakeoff 
												From @TVLEG F 
												Where 
												F.DateTakeoff<T.DateTakeoff 
												and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
												and F.ff=1 
												Order by F.DateTakeoff Desc 
												) 
						End between @DTbegin and @DTEnd
						and 
						Case
						When ff=0 or ff=3 then T.DateLanding
						When Ff=2 or ff=1 then (Select top 1 
												F.DateTakeoff 
												From @TVLEG F 
												Where 
												F.DateTakeoff>T.DateTakeoff 
												and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
												and F.ff=3 
												Order by F.DateTakeoff ASC 
												) 
						End  between @DTbegin and @DTEnd
						and t.FlightTaskReestrID is not null
						)
						or
						----------------------------------------CaringOUT-----------------------
						(
						(@Parts=0 or @Parts=2)
						and 
						Case
						When ff=0 or ff=1 then T.DateTakeoff
						When Ff=2 or ff=3 then (Select top 1 
												F.DateTakeoff 
												From @TVLEG F 
												Where 
												F.DateTakeoff<T.DateTakeoff 
												and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
												and F.ff=1 
												Order by F.DateTakeoff Desc 
												) 
						End between @DTbegin and @DTEnd
						and 
						Case
						When ff=0 or ff=3 then T.DateLanding
						When Ff=2 or ff=1 then (Select top 1 
												F.DateTakeoff 
												From @TVLEG F 
												Where 
												F.DateTakeoff>T.DateTakeoff 
												and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
												and F.ff=3 
												Order by F.DateTakeoff ASC 
												) 
						End  between @DTEnd and @DT_CREnd
						and t.FlightTaskReestrID is not null
						)
						) --and ff=3
					) TB
					ORDER BY
					TB.Ap_PlanFlightAirPortCrewID,
					TB.DateTakeoff
			
		-----------
		SET @BeginParing=1
		SET @NrowBegin=0
		OPEN @CURFOOD
		FETCH NEXT FROM @CURFOOD INTO 
			@Beg ,
			@Bend ,
			@Define ,
			@FlightTaskReestrID,
			@PlanFlightAirPortCrewID,
			@Tabel,
			@TehNumber, 
			@AP_BASE_ID ,
			@DateTakeoff ,
			@F_CodeIATA ,
			@F_Ap_AirPortID ,
			@F_Name ,
			@DateLanding ,
			@L_Ap_AirPortID ,
			@L_CodeIATA ,
			@L_Name,
			@CrewType
		WHILE @@FETCH_STATUS = 0
		  BEGIN
			SET @STR=''
			IF @Beg='Beg' 
			BEGIN
				Set @TYPE_CREW=Case
								When @TYPECREW=0 and @TehNumber=1 then '1/0/0/0/0/0/0 '
								When @TYPECREW=0 and @TehNumber=2 then '0/1/0/0/0/0/0 '
								When @TYPECREW=0 and @TehNumber=3 then '0/0/1/0/0/0/0 '
								---------------------------------------------------------
								When @TYPECREW=1 and @TehNumber=1 then '0/0/0/1/0/0/0 '
								When @TYPECREW=1 and @TehNumber=2 then '0/0/0/0/0/1/0 '
								When @TYPECREW=1 and @TehNumber=3 then '0/0/0/0/0/1/0 '
								When @TYPECREW=1 and @TehNumber=4 then '0/0/0/0/0/1/0 '
								When @TYPECREW=1 and @TehNumber=5 then '0/0/0/0/0/0/1 '
								When @TYPECREW=1 and @TehNumber=6 then '0/0/0/0/0/0/1 '
								When @TYPECREW=1 and @TehNumber=7 then '0/0/0/0/0/0/1 '
								else 
									CASE 
									When @TYPECREW=0 and @CrewType=4 then '0/0/1/0/0/0/0 '
									When @TYPECREW=1 and @CrewType=4 then '0/0/0/0/0/0/1 '
									else ''
									END
							   End
				SET @BeginParing=0
				--====================================
				Insert into @TBTPAR 
				values(	@Beg ,
						@Bend ,
						@Define ,
						@FlightTaskReestrID,
						@PlanFlightAirPortCrewID,
						@Tabel,
						@TehNumber, 
						@AP_BASE_ID ,
						@DateTakeoff ,
						@F_CodeIATA ,
						@F_Ap_AirPortID ,
						@F_Name ,
						@DateLanding ,
						@L_Ap_AirPortID ,
						@L_CodeIATA ,
						@L_Name,
						@TYPE_CREW,
						@CrewType
					  )
				--====================================
			END
		  FETCH NEXT FROM @CURFOOD INTO 
			@Beg ,
			@Bend ,
			@Define ,
			@FlightTaskReestrID,
			@PlanFlightAirPortCrewID,
			@Tabel,
			@TehNumber, 
			@AP_BASE_ID ,
			@DateTakeoff ,
			@F_CodeIATA ,
			@F_Ap_AirPortID ,
			@F_Name ,
			@DateLanding ,
			@L_Ap_AirPortID ,
			@L_CodeIATA ,
			@L_Name,
			@CrewType
		  END
		CLOSE @CURFOOD
		DEALLOCATE @CURFOOD 
--=================================================================================================
---select * From @TBTPAR
--=================================================================================================
--===============================Завершение Формирования TBLPAR (ТРИПОВ)  для CREW секции==============================
--=================================================================================================

--=================================================================================================
--===============================Формирование секции CREW==========================================
--=================================================================================================
-----=============================== Начало Курсора  CREW2  ================================

		SET @NN=@NN+1
		BEGIN TRANSACTION
			INSERT INTO @REZCRW
			select @NN, @SECTION_CREW
		COMMIT TRANSACTION

		DEclare
		@CrewID integer,  --PlanFlightAirPortCrewID
		@Tab nvarchar(10), --Tabel
		@cnt integer 
		SET @CURFOOD  = CURSOR SCROLL
		FOR 
			(
			select 
			isnull(t.PlanFlightAirPortCrewID,0), isnull(t.Tabel,isnull(P.TableNumber,P1.TableNumber)) tab, count(t.Tabel) cnt 
			From @TBTPAR T
			full join @TBLPER P on P.TableNumber=T.Tabel
			full join @TBLGRN_1 P1 on P1.TableNumber=P.TableNumber
			GROUP BY isnull(t.PlanFlightAirPortCrewID,0), t.Tabel, isnull(t.Tabel,isnull(P.TableNumber,P1.TableNumber))
			)
		OPEN @CURFOOD
		FETCH NEXT FROM @CURFOOD INTO 
			@CrewID, 
			@Tab, 
			@cnt
		WHILE @@FETCH_STATUS = 0
		  BEGIN
				--=============================Подсчет количества всех DUty================================================================
				Set @NNduty=0
				Set @NNduty=isnull((Select	count(*)
								From @TBLGRN T
								Where 
								T.TableNumber=@Tab
								and 
								(
								   ((T.DateBegin between @DT_CRbegin and @DTbegin)   and ((@Parts=1) or (@Parts=0)))  ------Увелечение периода
								or ((T.DateBegin between @DTbegin and @DTEnd) and (@Parts=0)) 
								or ((T.DateBegin between @DTEnd and @DT_CREnd) and (@Parts=2))   
								)),0)
								+
							Isnull((select count(*)
								From @TBTPAR T
								Where t.PlanFlightAirPortCrewID=@CrewID
								),0)
								+
							isnull((Select	count(*)
								From @TBLGRN_1 T
								Where 
								T.TableNumber=@Tab
								and 
								(
								   ((T.DateBegin between @DT_CRbegin and @DTbegin)   and ((@Parts=1) or (@Parts=0)))  ------Увелечение периода
								or ((T.DateBegin between @DTbegin and @DTEnd) and (@Parts=0)) 
								or ((T.DateBegin between @DTEnd and @DT_CREnd) and (@Parts=2))   
								)),0)
				--==============================================================================================
				SET @NN=@NN+1
				BEGIN TRANSACTION
				INSERT INTO @REZCRW
				select @NN, @CREW+' '+convert(nvarchar(4),@NNduty)+' "'+@Tab+'"' ---------------------------Запись
				COMMIT TRANSACTION
				--================================Наполнение наземными Duty===========================
							SET @CURLEG  = CURSOR SCROLL
							FOR 	
								Select	T.DateBegin,T.DateEnd,T.CodeEng,T.AP
								From @TBLGRN T
								Where 
								T.TableNumber=@Tab
								and 
								(
								   ((T.DateBegin between @DT_CRbegin and @DTbegin)  and ((@Parts=1) or (@Parts=0)))  ------Увелечение периода
								or ((T.DateBegin between @DTbegin and @DTEnd) and (@Parts=0)) 
								or ((T.DateBegin between @DTEnd and @DT_CREnd) and (@Parts=2))   
								)
							OPEN @CURLEG
							FETCH NEXT FROM @CURLEG INTO
								@GRDateBegin,
								@GRDateEnd,
								@GRCodeEng,
								@GR@AP
							WHILE @@FETCH_STATUS = 0
							BEGIN

								SET @NN=@NN+1
								BEGIN TRANSACTION
								INSERT INTO @REZCRW
								VALUES (@NN, ('L * 0 '+Convert(nvarchar(10),@GRDateBegin,112)+' '+@GR@AP+' '+ dbo.PD_fn_ShowDateAsHHMM(@GRDateBegin)+' '+@GRCodeEng+' * * '+ dbo.PD_fn_ShowDateAsHHMM(@GRDateEnd)+' '+@GR@AP+' '+Convert(nvarchar(10),@GRDateEnd,112)))  -----------------Запись
								COMMIT TRANSACTION

							FETCH NEXT FROM @CURLEG INTO 
								@GRDateBegin,
								@GRDateEnd,
								@GRCodeEng,
								@GR@AP
							END
						CLOSE @CURLEG
						DEALLOCATE @CURLEG 
				--================================Наполнение полетными Duty===========================
						SET @CURLEG  = CURSOR SCROLL
						FOR 			
							select 
							t.FlightTaskReestrID,
							t.TehNumber,
							t.TYPE_CREW,
							t.CrewType
							From @TBTPAR T
							Where t.PlanFlightAirPortCrewID=@CrewID
						OPEN @CURLEG
						FETCH NEXT FROM @CURLEG INTO
							@FlightTaskReestrID_1,
							@TehNumber_1,
							@TYPE_CREW_1,
							@CrewType
						WHILE @@FETCH_STATUS = 0
						BEGIN
							SET @NN=@NN+1
							BEGIN TRANSACTION
							INSERT INTO @REZCRW
							VALUES (@NN, ('1'+convert(nvarchar(20),@FlightTaskReestrID_1) + convert(nvarchar(2),@CrewType) + convert(nvarchar(2),@TehNumber_1)+' * 0 '+@TYPE_CREW_1)) -----------------Запись
														
							COMMIT TRANSACTION
						FETCH NEXT FROM @CURLEG INTO 
							@FlightTaskReestrID_1,
							@TehNumber_1,
							@TYPE_CREW_1,
							@CrewType
						END
					CLOSE @CURLEG
					DEALLOCATE @CURLEG 
				--====================================================================================
				--================================Наполнение GROUND Duty===========================
						SET @CURLEG  = CURSOR SCROLL
						FOR 	
							Select 
							max(T.Pr_EventID),
							Count(T.Pr_PersonnelID), 
							T.CodeEng ,
							dateadd(hh,-3,T.DateBegin),
							dateadd(hh,-3,T.DateEnd),
							T.AP,
							sum(Case when T.MainFunc='CP' and T.TableNumber=@Tab  then 1 else 0 end)  CP_cnt,
							sum(Case when T.MainFunc='FO' and T.TableNumber=@Tab  then 1 else 0 end)  FO_cnt,
							sum(Case when T.MainFunc='SCC'and T.TableNumber=@Tab  then 1 else 0 end)  SCC_cnt,
							sum(Case when T.MainFunc='CC' and T.TableNumber=@Tab  then 1 else 0 end)  CC_cnt 
							From @TBLGRN_1 T
							INNER JOIN (Select * From @TBLGRN_1 where TableNumber=@Tab) TT on TT.DateBegin=T.DateBegin and TT.DateEnd=T.DateEnd and TT.CodeEng=T.CodeEng 
							Where 
							(
							((T.DateBegin between @DT_CRbegin and @DTbegin)  and ((@Parts=1) or (@Parts=0)))  ------Увелечение периода
							or ((T.DateBegin between @DTbegin and @DTEnd) and (@Parts=0)) 
							or ((T.DateBegin between @DTEnd and @DT_CREnd) and (@Parts=2))   
							)  
							GROUP BY
							T.CodeEng,
							T.DateBegin,
							T.DateEnd,
							T.AP 
							Order By T.DateBegin
						OPEN @CURLEG
						FETCH NEXT FROM @CURLEG INTO
							@Pr_EventID,
							@CounPer,
							@DT_CodeEng,
							@DT_DateBegin,
							@DT_DateEnd,
							@DT_AP,
							@CP_cnt,
							@FO_cnt,
							@SCC_cnt,
							@CC_cnt 
						WHILE @@FETCH_STATUS = 0
						BEGIN

							SET @NN=@NN+1
							BEGIN TRANSACTION
							INSERT INTO @REZCRW
							VALUES (@NN, ('2'+convert(nvarchar(20),@Pr_EventID) + ' * 0 '+
									convert(nvarchar(3),@CP_cnt) +'/'+
									convert(nvarchar(3),@FO_cnt) +'/0/'+ 
									convert(nvarchar(3),@SCC_cnt)+'/0/'+ 
									convert(nvarchar(3),@CC_cnt) +'/0'))    
							COMMIT TRANSACTION
						FETCH NEXT FROM @CURLEG INTO 
							@Pr_EventID,
							@CounPer,
							@DT_CodeEng,
							@DT_DateBegin,
							@DT_DateEnd,
							@DT_AP,
							@CP_cnt,
							@FO_cnt,
							@SCC_cnt,
							@CC_cnt 
						END
					CLOSE @CURLEG
					DEALLOCATE @CURLEG 
				--====================================================================================
				SET @NN=@NN+1
				BEGIN TRANSACTION
				INSERT INTO @REZCRW
				select @NN, @EOCREW 
				COMMIT TRANSACTION
		  FETCH NEXT FROM @CURFOOD INTO 
			@CrewID, 
			@Tab, 
			@cnt
		  END
		CLOSE @CURFOOD
		DEALLOCATE @CURFOOD 

		SET @NN=@NN+1
		BEGIN TRANSACTION
			INSERT INTO @REZCRW
			select @NN,@EOSECTION 
		COMMIT TRANSACTION
END
--=================================================================================================
--===============================Завершение секции CREW============================================
--=================================================================================================


--=================================================================================================
--=============================== Начало Курсора 1  PAIRING  ======================================
--======================================Данные ====================================================
SET @NN=@NN+5000
INSERT INTO @REZCRW 
VALUES (@NN, @SECTION_PAIRING)

SET @CURFOOD  = CURSOR SCROLL
FOR 
---------
Select
TB.Beg,
TB.Bend,
TB.Define,
TB.FlightTaskReestrID ,
TB.Ap_PlanFlightAirPortCrewID,
TB.Tabel,
TB.TehNumber,
TB.AP_BASE_ID,
TB.DateTakeoff ,
TB.F_CodeIATA ,
TB.F_Ap_AirPortID ,
TB.F_Name ,
TB.DateLanding ,
TB.L_Ap_AirPortID ,
TB.L_CodeIATA ,
TB.L_Name,
TB.CrewType  
From (	Select 
		FlightTaskReestrID ,
		Ap_PlanFlightAirPortCrewID,
		Tabel,
		TehNumber,
		ROW_NUMBER() OVER(PARTITION BY Ap_PlanFlightAirPortCrewID Order by FlightTaskReestrID) Define,
		AP_BASE_ID ,
		isnull(H.HFF,-1) HFF,
		ff ,
		isnull(L.HLL,-1) HLL,
		Case
		When ff=0 then 'Beg'
		When ff=1 then 'Beg'
		When hff in (1,2) and ff=2  then ''
		When hff not in (1,2) and ff=2 then 'Beg'
		When hff not in (1,2) and ff=3 then 'Beg'
		end Beg,
		Case
		When ff=0 then 'End'
		When ff=1 and hll not in (2,3) then 'End'
		When ff=2 and hll in (2,3) then ''
		When ff=2 and hll not in (2,3) then 'End'
		When ff=3 then 'End'
		end Bend,
		DateTakeoff ,
		F_CodeIATA ,
		F_Ap_AirPortID ,
		F_Name ,
		DateLanding ,
		L_Ap_AirPortID ,
		L_CodeIATA ,
		L_Name,
		CrewType  
			From @TVLEG T
			OUTER APPLY (Select top 1 
							FF HFF 
							From @TVLEG F 
							Where 
							F.DateTakeoff<T.DateTakeoff 
							and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
							Order by F.DateTakeoff Desc 
						) H
			OUTER APPLY (Select top 1 
							FF HLL 
							From @TVLEG F 
							Where 
							F.DateTakeoff>T.DateTakeoff 
							and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
							Order by F.DateTakeoff ASC 
						) L
			WHere
			((
				(@Parts=0 or @Parts=1)
				and 
				-----------------CaringIN----------------------------
				Case
				When ff=0 or ff=1 then T.DateTakeoff
				When Ff=2 or ff=3 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff<T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=1 
										Order by F.DateTakeoff Desc 
										) 
				----------------------------------------------
				End between @DT_CRbegin and @DTbegin
				----------------------------------------------
				and 
				(
							Case
							When ff=0 or ff=3 then dateadd(hh,24,T.DateLanding)
							When Ff=2 or ff=1 then dateadd(hh,24,(Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff>T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=3 
													Order by F.DateTakeoff ASC 
													) )
							End  between @DT_CRbegin and @DTbegin
							or
							Case
							When ff=0 or ff=3 then dateadd(hh,24,T.DateLanding)
							When Ff=2 or ff=1 then dateadd(hh,24,(Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff>T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=3 
													Order by F.DateTakeoff ASC 
													) )
							End  >= @DTbegin
					)
				and t.FlightTaskReestrID is not null
			)
				or
				-----------------------Plan Period--------------
			(
				(@Parts=0)
				and 
				Case
				When ff=0 or ff=1 then T.DateTakeoff
				When Ff=2 or ff=3 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff<T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=1 
										Order by F.DateTakeoff Desc 
										) 
				End between @DTbegin and @DTEnd
				and 
				Case
				When ff=0 or ff=3 then T.DateLanding
				When Ff=2 or ff=1 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff>T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=3 
										Order by F.DateTakeoff ASC 
										) 
				End  between @DTbegin and @DTEnd
				and t.FlightTaskReestrID is not null
				)
				or
				----------------------------------------CaringOUT-----------------------
				(
				(@Parts=0 or @Parts=2)
				and 
				Case
				When ff=0 or ff=1 then T.DateTakeoff
				When Ff=2 or ff=3 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff<T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=1 
										Order by F.DateTakeoff Desc 
										) 
				End between @DTbegin and @DTEnd
				and 
				Case
				When ff=0 or ff=3 then T.DateLanding
				When Ff=2 or ff=1 then (Select top 1 
										F.DateTakeoff 
										From @TVLEG F 
										Where 
										F.DateTakeoff>T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										and F.ff=3 
										Order by F.DateTakeoff ASC 
										) 
				End  between @DTEnd and @DT_CREnd
				and t.FlightTaskReestrID is not null
				)
				) --and ff=3
			) TB
			ORDER BY
			TB.Ap_PlanFlightAirPortCrewID,
			TB.DateTakeoff
			
-----------
SET @BeginParing=1
SET @NrowBegin=0
OPEN @CURFOOD
FETCH NEXT FROM @CURFOOD INTO 
	@Beg ,
	@Bend ,
	@Define ,
	@FlightTaskReestrID,
	@PlanFlightAirPortCrewID,
	@Tabel,
	@TehNumber, 
	@AP_BASE_ID ,
	@DateTakeoff ,
	@F_CodeIATA ,
	@F_Ap_AirPortID ,
	@F_Name ,
	@DateLanding ,
	@L_Ap_AirPortID ,
	@L_CodeIATA ,
	@L_Name,
	@CrewType
WHILE @@FETCH_STATUS = 0
  BEGIN
	SET @STR=''
	IF @Beg='Beg' 
	BEGIN
	-------------------------------------------------
	---Select 	@Beg ,@Bend ,@Define ,@FlightTaskReestrID,@PlanFlightAirPortCrewID,@Tabel,@TehNumber, @AP_BASE_ID ,@DateTakeoff ,@F_CodeIATA ,@F_Ap_AirPortID ,@F_Name ,@DateLanding ,@L_Ap_AirPortID ,@L_CodeIATA ,@L_Name,@CrewType
	-------------------------------------------------
		Set @TYPE_CREW=Case
						When @TYPECREW=0 and @TehNumber=1 then '1/0/0/0/0/0/0 '
						When @TYPECREW=0 and @TehNumber=2 then '0/1/0/0/0/0/0 '
						When @TYPECREW=0 and @TehNumber=3 then '0/0/1/0/0/0/0 '
						---------------------------------------------------------
						When @TYPECREW=1 and @TehNumber=1 then '0/0/0/1/0/0/0 '
						When @TYPECREW=1 and @TehNumber=2 then '0/0/0/0/0/1/0 '
						When @TYPECREW=1 and @TehNumber=3 then '0/0/0/0/0/1/0 '
						When @TYPECREW=1 and @TehNumber=4 then '0/0/0/0/0/1/0 '
						When @TYPECREW=1 and @TehNumber=5 then '0/0/0/0/0/0/1 '
						When @TYPECREW=1 and @TehNumber=6 then '0/0/0/0/0/0/1 '
						When @TYPECREW=1 and @TehNumber=7 then '0/0/0/0/0/0/1 '
						else 
							CASE 
								When @TYPECREW=0 and @CrewType=4 then '0/0/1/0/0/0/0 '
								When @TYPECREW=1 and @CrewType=4 then '0/0/0/0/0/0/1 '
							else ''
							END
					   End
		SET @BeginParing=0
		SET @Nleg=0
		SET @NN=@NN+1
		IF (isnull(@PAIRING+CONVERT(nvarchar(25),@FlightTaskReestrID)+convert(nvarchar(2),@TehNumber)+' "'+CONVERT(nvarchar(25),@FlightTaskReestrID)+convert(nvarchar(2),@TehNumber)+'" '+@TYPE_CREW+LTRIM(RTRIM(@F_CodeIATA)) + ' #' + @Tabel +' ' + convert(nvarchar(2),@TehNumber),'')<>'')
			BEGIN
			-----------------------------------------------------------
			SET @STR=isnull(@PAIRING+'1'+CONVERT(nvarchar(25),@FlightTaskReestrID)+convert(nvarchar(2),@CrewType)+convert(nvarchar(2),@TehNumber)+
				' "FD'+CONVERT(nvarchar(25),@FlightTaskReestrID)+
				convert(nvarchar(2),@CrewType)+
				convert(nvarchar(2),@TehNumber)+'" '+
				@TYPE_CREW+LTRIM(RTRIM(@F_CodeIATA)) + 
				' #' + @Tabel +' ' + 
				convert(nvarchar(2),@TehNumber),'error-'+CONVERT(nvarchar(25),@FlightTaskReestrID))
			------------------------------------------------------------
			SET @NrowBegin=@NN
			END
		BEGIN TRANSACTION
		INSERT INTO @REZCRW
		select @NN,@STR 
		COMMIT TRANSACTION
	END
		----------------------------------------------------
		------Select 
		------	T.DateTakeoff,
		------	T.AP_TO,
		------	T.FlightName,
		------	T.DateLanding,
		------	T.AP_LA
		------	From @TVLEGS T
		------	INNER Join Ap_PlanFlightAirPortCrews CR ON CR.Ap_PlanFlightAirPortID=T.Ap_PlanFlightAirPortID 
		------	Where 
		------	T.Pr_FlightTaskReestrID=@FlightTaskReestrID
		------	and CR.Pr_PersonnelID=@PlanFlightAirPortCrewID
		------	Order by DateTakeoff
		-----------------------------------------------------
		SET @CURLEG  = CURSOR SCROLL
			FOR 			
			Select 
			T.DateTakeoff,
			T.AP_TO,
			T.FlightName,
			T.DateLanding,
			T.AP_LA
			From @TVLEGS T
			INNER Join Ap_PlanFlightAirPortCrews CR ON CR.Ap_PlanFlightAirPortID=T.Ap_PlanFlightAirPortID 
			Where 
			T.Pr_FlightTaskReestrID=@FlightTaskReestrID
			and CR.Pr_PersonnelID=@PlanFlightAirPortCrewID
			Order by DateTakeoff

			OPEN @CURLEG
			FETCH NEXT FROM @CURLEG INTO
				@TO_DateTakeoff ,
				@TO_CodeIATA ,
				@LegName ,
				@LA_DateLanding,
				@LA_CodeIATA 
			WHILE @@FETCH_STATUS = 0
			BEGIN
			If  @BeginParing=0
			BEGIN
				SET @STR1=''
				SET @Nleg=@Nleg+1
				SET @NN=@NN+1
				SET @STR1=@Activity_type+@DS+
							Case
							when (@CrewType < 4) Then Case
														When @TO_DateTakeoff between @DT_CRbegin and @DTbegin and (@LA_DateLanding between @DT_CRbegin and @DTbegin or @LA_DateLanding >=@DTbegin) Then @ActivitySub_type
														else '*'
													  End
							when (@CrewType = 4) Then @ActivitySub_typeDH
							end +@DS+
							--------------------------------------------------------------------------
							Case
							when @Nleg=1 then @Horizontal_lock1
							when @DateLanding =@LA_DateLanding and  @Bend='End'  then @Horizontal_lock3
							else @Horizontal_lock2
							end +@DS+
							------------------------------------------------------------------------------
							Convert(nvarchar(10),@TO_DateTakeoff,112)+@DS+@TO_CodeIATA +@DS+dbo.PD_fn_ShowDateAsHHMM(@TO_DateTakeoff )+@DS+
							@CorrierCode+@DS+convert(nvarchar(6),@LegName) +@DS+'*'+@DS+'1'+@DS+dbo.PD_fn_ShowDateAsHHMM(@LA_DateLanding)+@DS+
							@LA_CodeIATA+@DS+Convert(nvarchar(10),@LA_DateLanding,112)
							+ ' #duty'+ convert(nvarchar(6),@Define)+' '+convert(nvarchar(2),@CrewType) 
					BEGIN TRANSACTION
						INSERT INTO @REZCRW
						select @NN,@STR1 
					COMMIT TRANSACTION
					--------------------------
					BEGIN TRANSACTION
						UPDATE @TVLEGS 
						SET FL=1
						Where LTRIM(RTRIM(FlightName))=LTRIM(RTRIM(@LegName)) and convert(datetime, DateTakeoff)=convert(datetime,@TO_DateTakeoff)
						--Select * From @TVLEGS Where LTRIM(RTRIM(FlightName))='441' --and DateTakeoff=@TO_DateTakeoff
					COMMIT TRANSACTION
					-----------------------------------------------------
			END
			FETCH NEXT FROM @CURLEG INTO 
				@TO_DateTakeoff ,
				@TO_CodeIATA ,
				@LegName ,
				@LA_DateLanding,
				@LA_CodeIATA 
			END
		CLOSE @CURLEG
		DEALLOCATE @CURLEG 

    ---------------------------------------
	IF  @BeginParing=0 and  @Bend='End' 
	Begin
	---------------------------------------
		BEGIN TRANSACTION
			UPDATE @REZCRW 
			SET STR1=SUBSTRING(TT.STR1,charindex(@PAIRING,TT.STR1,1),LEN(@PAIRING)) + ' ' +  convert(nvarchar(5),@Nleg) + ' ' + SUBSTRING(TT.STR1,LEN(@PAIRING)+1,LEN(TT.STR1)-LEN(@PAIRING)+1)
			From @REZCRW TT where TT.NN=@NrowBegin 
		COMMIT TRANSACTION
		---------------------------------------
		BEGIN TRANSACTION
			SET  @BeginParing=1
			SET @NrowBegin=0
			SET @NN=@NN+1
			INSERT INTO @REZCRW
			select @NN,@EOPAIRING 
		COMMIT TRANSACTION
	End
	
  FETCH NEXT FROM @CURFOOD INTO 
	@Beg ,
	@Bend ,
	@Define ,
	@FlightTaskReestrID,
	@PlanFlightAirPortCrewID,
	@Tabel,
	@TehNumber, 
	@AP_BASE_ID ,
	@DateTakeoff ,
	@F_CodeIATA ,
	@F_Ap_AirPortID ,
	@F_Name ,
	@DateLanding ,
	@L_Ap_AirPortID ,
	@L_CodeIATA ,
	@L_Name,
	@CrewType
  END
CLOSE @CURFOOD
DEALLOCATE @CURFOOD 
-----------------
--====================================================================================================
--=======================Формирование PAIRING WTH GROUND DUTY=========================================
--=====================================================================================================
---------------------------------
SET @CURFOOD  = CURSOR SCROLL
FOR  	
	Select 
	--Pr_PersonnelID ,
	--Pr_EventTypeID ,
	--TableNumber ,
	max(Pr_EventID),
	Count(Pr_PersonnelID), 
	CodeEng ,
	dateadd(hh,-3,DateBegin),
	dateadd(hh,-3,DateEnd),
	AP,
	sum(Case when MainFunc='CP'  then 1 else 0 end)  CP_cnt,
	sum(Case when MainFunc='FO'  then 1 else 0 end)  FO_cnt,
	sum(Case when MainFunc='SCC' then 1 else 0 end)  SCC_cnt,
	sum(Case when MainFunc='CC'  then 1 else 0 end)  CC_cnt 
	From @TBLGRN_1
	Where 
	(
	((DateBegin between @DT_CRbegin and @DTbegin)  and   ((@Parts=1) or (@Parts=0)))  ------Увелечение периода
	or ((DateBegin between @DTbegin and @DTEnd) and (@Parts=0)) 
	or ((DateBegin between @DTEnd and @DT_CREnd) and (@Parts=2))   
	)  
	GROUP BY
	CodeEng,
	DateBegin,
	DateEnd,
	AP 
	Order By DateBegin

OPEN @CURFOOD
FETCH NEXT FROM @CURFOOD INTO 
	@Pr_EventID,
	@CounPer,
	@DT_CodeEng,
	@DT_DateBegin,
	@DT_DateEnd,
	@DT_AP,
	@CP_cnt,
	@FO_cnt,
	@SCC_cnt,
	@CC_cnt 
WHILE @@FETCH_STATUS = 0
  BEGIN
  ------------------------------
		SET @STR1=@PAIRING +' 1'+' ' +'2'+convert(nvarchar(30),@Pr_EventID) +' '+'"GD'+convert(nvarchar(30),@Pr_EventID)+'"'+' '+
		convert(nvarchar(3),@CP_cnt) +'/'+
		convert(nvarchar(3),@FO_cnt) +'/0/'+ 
		convert(nvarchar(3),@SCC_cnt)+'/0/'+ 
		convert(nvarchar(3),@CC_cnt) +'/0'+' '+@DT_AP                      
	BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@STR1 
	COMMIT TRANSACTION
  ---------------------------------
		---G * * 20030707 CPH 0800 OL1 * * 0 1400 CPH 20030707
		--SET @STR1='"'+@DT_CodeEng+'"'+' * '+ ' "'+@DT_AP+'" ' +dbo.PD_fn_ShowDateAsHHMM(@DT_DateBegin) + ' '+ convert(nvarchar(3),datediff(hh,@DT_DateBegin,@DT_DateEnd))+' 1234567 '+Convert(nvarchar(10),@DT_DateBegin,112)+' '+Convert(nvarchar(10),@DT_DateEnd,112)
		SET @STR1='G'+' * * ' + Convert(nvarchar(10),@DT_DateBegin,112)+' '+@DT_AP+' '+dbo.PD_fn_ShowDateAsHHMM(@DT_DateBegin)+' '+@DT_CodeEng+' * * 0 '+dbo.PD_fn_ShowDateAsHHMM(@DT_DateEnd)+' '+@DT_AP+' '+Convert(nvarchar(10),@DT_DateEnd,112) + ' #Duty1' 
	BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@STR1 
	COMMIT TRANSACTION
   -----------------------
	BEGIN TRANSACTION
		SET @NN=@NN+1
		INSERT INTO @REZCRW
		select @NN,@EOPAIRING 
	COMMIT TRANSACTION
	FETCH NEXT FROM @CURFOOD INTO
	@Pr_EventID,
	@CounPer, 
	@DT_CodeEng,
	@DT_DateBegin,
	@DT_DateEnd,
	@DT_AP,
	@CP_cnt,
	@FO_cnt,
	@SCC_cnt,
	@CC_cnt 
  END
CLOSE @CURFOOD
DEALLOCATE @CURFOOD 
----------------------

----------------------
--====================================================================================================
--=======================Завершение Формирования  PAIRING WITH GROUND DUTY============================
--====================================================================================================
-----------------
SET @NN=@NN+1
BEGIN TRANSACTION
	INSERT INTO @REZCRW
	select @NN,@EOSECTION 
COMMIT TRANSACTION
--====================================================================================================
--=======================Завершение Формирования секции PAIRING=======================================
--====================================================================================================


--====================================================================================================
--=======================Формирование секции GROUND DUTY==============================================
--====================================================================================================
BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@SECTION_GROUND_DUTY 
COMMIT TRANSACTION
------
SET @CURFOOD  = CURSOR SCROLL
FOR  
	Select 
	--Pr_PersonnelID ,
	--Pr_EventTypeID ,
	--TableNumber ,
	CodeEng ,
	dateadd(hh,-3,DateBegin),
	dateadd(hh,-3,DateEnd),
	AP 
	From @TBLGRN_1
	Where 
	(
	((DateBegin between @DT_CRbegin and @DTbegin)   and ((@Parts=1) or (@Parts=0)))  ------Увелечение периода
	or ((DateBegin between @DTbegin and @DTEnd) and (@Parts=0)) 
	or ((DateBegin between @DTEnd and @DT_CREnd) and (@Parts=2))   
	) 
	GROUP BY
	CodeEng,
	DateBegin,
	DateEnd,
	AP
    Order By DateBegin

OPEN @CURFOOD
FETCH NEXT FROM @CURFOOD INTO 
	@DT_CodeEng,
	@DT_DateBegin,
	@DT_DateEnd,
	@DT_AP
WHILE @@FETCH_STATUS = 0
  BEGIN
  --------------------------------------------------------------------------
	SET @STR1='"'+@DT_CodeEng+'"'+' * '+ ' "'+@DT_AP+'" ' +
	dbo.PD_fn_ShowDateAsHHMM(@DT_DateBegin) + ' '+ replace(dbo.ab_fn_ShowIntAsHHMM(datediff(ss,@DT_DateBegin,@DT_DateEnd)),':','')+
	' 1234567 '+Convert(nvarchar(10),@DT_DateBegin,112)+' '+Convert(nvarchar(10),@DT_DateBegin,112)  --@DT_DateEnd Заменили на @DT_DateBegin по требованию JP
	-----------------------------------------------------------------------
	BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@STR1 
	COMMIT TRANSACTION

	FETCH NEXT FROM @CURFOOD INTO 
	@DT_CodeEng,
	@DT_DateBegin,
	@DT_DateEnd,
	@DT_AP
  END
CLOSE @CURFOOD
DEALLOCATE @CURFOOD 
----------------------
BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@EOSECTION 
COMMIT TRANSACTION
----------------------
--====================================================================================================
--=======================Завершение Формирования секции GROUND DUTY====================================
--=====================================================================================================

--====================================================================================================
--=======================Формирование секции LEGS==============================================
--=====================================================================================================
DEclare @NE integer
SET @NN=@NN+10000
BEGIN TRANSACTION
INSERT INTO @REZCRW
EXEC PD_JPR_ImportCTF_Legs @DT_CRbegin, @DTEnd, @NN, @NE, 1
SET @NN=@NE+1
INSERT INTO @REZCRW
Select @NN,'EOSECTION'
COMMIT TRANSACTION
--====================================================================================================
--=======================Завершения Формирование секции LEGS====================================
--=====================================================================================================

--=====================================================================================================
--================================ВЫВОД=============================================================
Select STR1 from @REZCRW
--=========================================================================
Select * from @TVLEGS 
where 
FL=0
and FlightName not in ('001','002')
and
(
   ((DateTakeoff between @DT_CRbegin and @DTbegin)  and (@Parts=1))
or ((DateTakeoff between @DTbegin and @DTEnd) and (@Parts=0)) 
or ((DateTakeoff between @DTEnd and @DT_CREnd) and (@Parts=2))   
)
END