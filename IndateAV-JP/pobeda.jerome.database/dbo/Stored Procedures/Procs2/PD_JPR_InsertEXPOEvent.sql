﻿ ----=============================================
 ----Author:		<Author,,Name>
 ----Create date: <Create Date,,>
 ----Description:	<Description,,>
 ----=============================================
CREATE PROCEDURE [dbo].[PD_JPR_InsertEXPOEvent]
				@STRXML NVARCHAR(MAX)=''
AS
BEGIN

	--===========================================
	--Declare
	--@STRXML NVARCHAR(MAX)='<Flights><Id_Flight>12</Id_Flight><NumberFlight>0</NumberFlight><CarGo>WOF</CarGo><APTKF>VKO</APTKF><APLND>VKO</APLND><DataFlight>2019-01-12T21:00:00Z</DataFlight><STD>2019-01-12T21:00:00Z</STD><STA>2019-01-13T20:59:00Z</STA><TypeFlight>PERSACT</TypeFlight><IdParing>0</IdParing><IdTask>0</IdTask><TPCrew></TPCrew><Tabel>155</Tabel></Flights>'
	--=======================================================

	DECLARE @TTT Table
	(
	NumberFlight nvarchar(10) null,
	CarGo nvarchar(5)  null,
	AP_TKF nvarchar(5) null,
	AP_LND nvarchar(5) null,
	DataFlight DateTime null,
	STD DateTime null,
	STA DateTime null,
	IdParing bigint null,
	IdTask bigint null,
	TypeFlight nvarchar(10) null,
	TPCrew nvarchar(15) null,
	IDTaskCP bigint null,
	Tabel nvarchar(15) null,
	PPLS nvarchar(50) null
	)

	DECLARE @xml xml
	SELECT @xml = CAST(CAST(@STRXML AS VARBINARY(MAX)) AS XML) 
	DECLARE @AP integer
	DEclare @SHT integer
	INSERT INTO @TTT
	SELECT 
		x.Rec.query('./NumberFlight').value('.', 'nvarchar(10)') AS 'NumberFlight',
		x.Rec.query('./CarGo').value('.', 'nvarchar(5)') AS 'CarGo',
		x.Rec.query('./APTKF').value('.', 'nvarchar(5)') AS 'AP_TKF',
		x.Rec.query('./APLND').value('.', 'nvarchar(5)') AS 'AP_LND',
		x.Rec.query('./DataFlight').value('.', 'DateTime') AS 'DataFlight',
		x.Rec.query('./STD').value('.', 'DateTime') AS 'STD',
		x.Rec.query('./STA').value('.', 'DateTime') AS 'STA',
		x.Rec.query('./IdParing').value('.', 'bigint') AS 'IdParing',
		x.Rec.query('./IdTask').value('.', 'bigint') AS 'IdTask',
		x.Rec.query('./TypeFlight').value('.', 'nvarchar(10)') AS 'TypeFlight',
		x.Rec.query('./TPCrew').value('.', 'nvarchar(15)') AS 'TPCrew',
		x.Rec.query('./IDTaskCP').value('.', 'bigint') AS 'IDTaskCP',
		x.Rec.query('./Tabel').value('.', 'nvarchar(10)') AS 'Tabel',
		x.Rec.query('./PPLS').value('.', 'nvarchar(50)') AS 'PPLS'

	FROM @xml.nodes('/Flights') as x(Rec)
	--===============================================
	--select * From @TTT
	--===============================================

UPDATE Pr_Events
Set Comment='UPJP '+Comment
	-----------------------
From Pr_Events 
Where Pr_Events.Pr_EventID in
	(Select
	EV.Pr_EventID
	From @TTT TT
	LEFT  JOIN Pr_Personnels PR  ON PR.TableNumber=TT.Tabel and PR.TableNumber>'' and PR.Pr_CategoryTypeID in (1,5) ------------------Только из ЛС Защита от дублирующих Табельных номеров
	LEFT  JOIN Pr_EventTypes TEV ON TEV.CodeEng=TT.CarGo
	LEFT  JOIN Pr_Events EV      ON EV.Pr_PersonnelID = PR.Pr_PersonnelID and TEV.Pr_EventTypeID=EV.Pr_EventTypeID  and abs(datediff(hh,convert(smalldatetime,EV.DateBegin),convert(smalldatetime,dateadd(hh,3,TT.STD))))<=7 and abs(datediff(hh,convert(smalldatetime,EV.DateEnd),convert(smalldatetime,dateadd(hh,3,TT.STA))))<=7
	LEFT  JOIN Ap_AirPorts AP    ON AP.CodeIATA=TT.AP_TKF
	LEFT  JOIN Gn_Citys CT       ON CT.Gn_CityID=AP.Gn_CityID
	Where EV.Pr_EventID is not null 
	--and PR.Pr_CategoryTypeID in (5) --===============================================
	)
	--=================================================

	INSERT INTO Pr_Events
	(
	Pr_PersonnelID,
	DateBegin,
	DateEnd,
	Pr_EventTypeID,
	Gn_CityID,
	Comment,
	Executor,
	Inspector
	)
	select 
	PR.Pr_PersonnelID,
	dateadd(hh,3,TT.STD),
	dateadd(hh,3,TT.STA),
	TEV.Pr_EventTypeID,
	CT.Gn_CityID,
	'JP-'+convert(nvarchar(30),getutcdate(),120),
	0,
	0 
	-----------------------
	From @TTT TT
	LEFT  JOIN Pr_Personnels PR  ON PR.TableNumber=TT.Tabel and PR.TableNumber>''  and PR.Pr_CategoryTypeID in (1,5) ------------------Только из ЛС Защита от дублирующих Табельных номеров
	LEFT  JOIN Pr_EventTypes TEV ON TEV.CodeEng=TT.CarGo
	LEFT  JOIN Pr_Events EV      ON EV.Pr_PersonnelID = PR.Pr_PersonnelID and TEV.Pr_EventTypeID=EV.Pr_EventTypeID  and abs(datediff(hh,convert(smalldatetime,EV.DateBegin),convert(smalldatetime,dateadd(hh,3,TT.STD))))<=7 and abs(datediff(hh,convert(smalldatetime,EV.DateEnd),convert(smalldatetime,dateadd(hh,3,TT.STA))))<=7
	LEFT  JOIN Ap_AirPorts AP    ON AP.CodeIATA=TT.AP_TKF
	LEFT  JOIN Gn_Citys CT       ON CT.Gn_CityID=AP.Gn_CityID
	LEFT  JOIN Pr_Orders RD		 ON RD.Pr_PersonnelID=PR.Pr_PersonnelID and RD.OrderType in (5) and convert(date,RD.DateBegin)=convert(date,EV.DateBegin) and convert(date,RD.DateFinish)=convert(date,EV.DateEnd) and  TEV.CodeEng in  ('ULVE','RLVE','MLVE','LVE') 
	Where 
	EV.Pr_EventID is null 
	and RD.Pr_OrderID is null
	--and PR.Pr_CategoryTypeID in (5) --===============================================
	----------------------------------------------------------------------
END