﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ImportCTF_CaringOUT]
@DTbegin datetime,  
@DTEnd datetime
AS
BEGIN

--Declare 
--@DTbegin datetime='2018-04-01 00:00:00',  
--@DTEnd datetime='2018-04-30 23:59:00'


--====================================Таблицы Выход.==============================
Declare @REZCRW Table
(NN integer null,
 STR1 nvarchar(300) null)

--====================================Таблицы Внутр.===============================
Declare @TBTSK Table
(
	FlightTaskReestrID integer null,
	Personal_ID integer null,
	Bap integer
)

Declare @TVLEG table
(
FlightTaskReestrID integer,
Personal_ID integer,
AP_BASE_ID integer,
ff integer,
DateTakeoff Datetime,
F_CodeIATA nvarchar(5),
F_Ap_AirPortID integer,
F_Name nvarchar(5),
DateLanding Datetime,
L_Ap_AirPortID integer,
L_CodeIATA nvarchar(5),
L_Name nvarchar(5)
)


------------------------------------------------------------------------
---------------------------------------------------------------------------
DECLARE @PERIOD NVARCHAR(100)  ='PERIOD:'
DECLARE @PLAN_TYPE NVARCHAR(100)='PLAN TYPE: DATED' 
DECLARE @TIME_MODE NVARCHAR(100)='TIME MODE: UTC' 
---------------------------------------------------------------------------
DECLARE @SECTION_CREW NVARCHAR(100)  ='SECTION: CREW'
DECLARE @CREW NVARCHAR(100)='CREW:' 
DECLARE @EOCREW NVARCHAR(100)='EOCREW'
----------------------------------------------------------------------------
DECLARE @EOSECTION NVARCHAR(100)='EOSECTION'
---------------------------------------------
DECLARE @SECTION_PAIRING NVARCHAR(100)  ='SECTION: PAIRING'
DECLARE @PAIRING  NVARCHAR(100)='PAIRING:' 
DECLARE @EOPAIRING NVARCHAR(100)='EOPAIRING' 
----------------------------------------------------- 
DECLARE @SECTION_LEG NVARCHAR(100)  ='SECTION: LEG'
----------------------------------------------------- 
DECLARE @SECTION_GROUND_DUTY NVARCHAR(100)  ='SECTION: GROUND DUTY'
----------------------------------------------------- 
DECLARE @DS nvarchar(1)=' '
DECLARE @STRFlights NVARCHAR(100)=''
DECLARE @CorrierCode nvarchar(2)='DP'
----------------------------------------------------
DECLARE @Duty_code NVARCHAR(10)='*'
DECLARE @Lock_code NVARCHAR(10)='L' 
DECLARE @Environment_code NVARCHAR(10)='1' 
DECLARE @Activity_code NVARCHAR(10)='LV ' 
DECLARE @Activity_attribute NVARCHAR(10)='*' 
DECLARE @Activity_type NVARCHAR(1)='F' 
DECLARE @ActivitySub_type NVARCHAR(1)='L'
DECLARE @Horizontal_lock1 NVARCHAR(1)='X'
DECLARE @Horizontal_lock2 NVARCHAR(1)='N'
DECLARE @Horizontal_lock3 NVARCHAR(1)='L'


Declare
@DT_CRbegin datetime=dateadd(dd,0, @DTEnd),  
@DT_CREnd datetime=dateadd(dd,12, @DTEnd) 

---------------------------------------------
Insert INTO @TBTSK
Select		
		PFA.Pr_FlightTaskReestrID,
		PR.Pr_PersonnelID,
		PTR.Ap_AirPortID  Bap	
	FROM Ap_PlanFlights PFL
		INNER JOIN Ap_PlanFlightAirPorts PFA ON PFA.Ap_PlanFlightID = PFL.Ap_PlanFlightID ---and PFTO.AirPortNumber=1
		LEFT JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
		LEFT JOIN Pr_Personnels PR on  PFC.Pr_PersonnelID=PR.Pr_PersonnelID
		LEFT JOIN Pr_PersPosts Post ON PR.Pr_PersonnelID = Post.Pr_PersonnelID 
		LEFT JOIN Pr_StaffTrees TR ON Post.Pr_StaffTreeID = TR.Pr_StaffTreeID AND (Post.DateBegin < getutcdate() and (Post.DateEnd is null or Post.DateEnd > getutcdate())) 
		LEFT  JOIN Pr_StaffTrees PTR ON PTR.Pr_StaffTreeID = TR.Pr_StaffTreeIDParent 
		--LEFT JOIN Pr_ArmChairTypes ON PFC.Pr_ArmChairTypeID = Pr_ArmChairTypes.Pr_ArmChairTypeID 
		--LEFT JOIN Pr_ArmChairRoles ON Pr_ArmChairRoles.Pr_ArmChairRoleID = PFC.Pr_ArmChairRoleID 
		INNER JOIN Fl_Flights			   FL ON PFL.Fl_FlightID=FL.Fl_FlightID
	WHERE 
		convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
		AND PFL.OnlyFinanceCalculation = 0
		AND PFL.Sh_ScheduleVariantTypeID IS NULL
		and PFL.Status & 256 <> 256  ----отмененные рейсы
		AND ISNULL(FL.FlightVariant,0) = 0 
		AND PFL.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки
		----
		and (PFC.OrderNumber=1 or PFC.OrderNumber is null)
		and (PFC.CrewType=0 or PFC.CrewType is null)
    GROUP BY  PFA.Pr_FlightTaskReestrID, PR.Pr_PersonnelID , PTR.Ap_AirPortID
-------------------------------------------------
INSERT INTO @TVLEG
Select 
T.FlightTaskReestrID,
T.Personal_ID,
ISNULL(T.Bap,58),
Case ISNULL(T.Bap,58)
WHEN 58 then 
		Case
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3
		End
WHEN 86 then
		Case
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0
		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2
		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3
		End
END ff,
FR.DateTakeoff,
FR.CodeIATA,
FR.Ap_AirPortID,
FR.Name,
LR.DateLanding,
LR.Ap_AirPortID,
LR.CodeIATA,
LR.Name
From @TBTSK T
OUTER APPLY (select top 1
			 PFA.DateTakeoff,
			 FL.NAME,
			 PRT.Ap_AirPortID,
			 PRT.CodeIATA	
			 From Ap_PlanFlightAirPorts PFA
			 INNER JOIN Ap_PlanFlights PFL ON PFL.Ap_PlanFlightID=PFA.Ap_PlanFlightID
			 INNER JOIN Fl_Flights     FL  ON PFL.Fl_FlightID=FL.Fl_FlightID
			 INNER JOIN Ap_AirPorts    PRT  ON PRT.Ap_AirPortID=PFA.Ap_AirPortID
			 WHERE 
			 PFA.AirPortNumber=1
			 and T.FlightTaskReestrID=PFA.Pr_FlightTaskReestrID
			 ORDER BY PFA.DateTakeoff ASC
			 ) FR
OUTER APPLY (select top 1
			 PFA1.DateLanding,
			 FL.NAME,
			 PRT.Ap_AirPortID,
			 PRT.CodeIATA	
			 From Ap_PlanFlightAirPorts PFA
			 INNER JOIN Ap_PlanFlightAirPorts PFA1 ON PFA.Ap_PlanFlightID=PFA1.Ap_PlanFlightID and PFA.AirPortNumber+1=PFA1.AirPortNumber
			 INNER JOIN Ap_PlanFlights PFL ON PFL.Ap_PlanFlightID=PFA.Ap_PlanFlightID
			 INNER JOIN Fl_Flights     FL  ON PFL.Fl_FlightID=FL.Fl_FlightID
			 INNER JOIN Ap_AirPorts    PRT  ON PRT.Ap_AirPortID=PFA1.Ap_AirPortID
			 WHERE 
			 PFA.AirPortNumber=1
			 and T.FlightTaskReestrID=PFA.Pr_FlightTaskReestrID
			 ORDER BY PFA1.DateLanding DESC
			 ) LR
ORDER BY 
T.Personal_ID,
FR.DateTakeoff,
FR.CodeIATA
--==================================================================
--==================================================================
--==============================================
DECLARE @CURFOOD CURSOR
DECLARE @CURLEG CURSOR
DECLARE 
@NN integer=0,
@STR NVARCHAR(max)='',
@STR1 NVARCHAR(max)=''


DECLARE @BeginParing integer=0
DECLARE 
@FlightTaskReestrID integer,
@Personal_ID integer,
@AP_BASE_ID integer,
@ff integer,
@DateTakeoff Datetime,
@F_CodeIATA nvarchar(5),
@F_Ap_AirPortID integer,
@F_Name nvarchar(5),
@DateLanding Datetime,
@L_Ap_AirPortID integer,
@L_CodeIATA nvarchar(5),
@L_Name nvarchar(5),
@Beg nvarchar(3),
@Bend nvarchar(3),
@Define integer,
------------------
@Nrow integer,
@Nleg integer,
@NrowBegin integer,
@TO_DateTakeoff Datetime,
@TO_CodeIATA nvarchar(5),
@LegName nvarchar(5),
@LA_DateLanding DateTime,
@LA_CodeIATA  nvarchar(5)
--===========================================================
Set @NN=@NN+1
----INSERT INTO @REZCRW 
----VALUES (@NN, @PERIOD+' '+Convert(nvarchar(10),@DTbegin,112)+'-'+Convert(nvarchar(10),@DTEnd,112))
----Set @NN=@NN+1
----INSERT INTO @REZCRW 
----VALUES (@NN, @PLAN_TYPE)
----Set @NN=@NN+1
----INSERT INTO @REZCRW 
----VALUES (@NN, @TIME_MODE)
----Set @NN=@NN+1
----INSERT INTO @REZCRW 
----VALUES (@NN, @SECTION_PAIRING)
--===========================================================
SET @CURFOOD  = CURSOR SCROLL
FOR 
-------
Select
TB.Beg,
TB.Bend,
TB.Define,
TB.FlightTaskReestrID ,
TB.Personal_ID ,
TB.AP_BASE_ID,
TB.DateTakeoff ,
TB.F_CodeIATA ,
TB.F_Ap_AirPortID ,
TB.F_Name ,
TB.DateLanding ,
TB.L_Ap_AirPortID ,
TB.L_CodeIATA ,
TB.L_Name  
From 	(Select
		FlightTaskReestrID ,
		Personal_ID ,
		ROW_NUMBER() OVER(PARTITION BY Personal_ID Order by FlightTaskReestrID) Define,
		AP_BASE_ID ,
		isnull(H.HFF,-1) HFF,
		ff ,
		isnull(L.HLL,-1) HLL,
		Case
		When ff=0 then 'Beg'
		When ff=1 then 'Beg'
		When hff in (1,2) and ff=2  then ''
		When hff not in (1,2) and ff=2 then 'Beg'
		When hff not in (1,2) and ff=3 then 'Beg'
		end Beg,
		Case
		When ff=0 then 'End'
		When ff=1 and hll not in (2,3) then 'End'
		When ff=2 and hll in (2,3) then ''
		When ff=2 and hll not in (2,3) then 'End'
		When ff=3 then 'End'
		end Bend,
		DateTakeoff ,
		F_CodeIATA ,
		F_Ap_AirPortID ,
		F_Name ,
		DateLanding ,
		L_Ap_AirPortID ,
		L_CodeIATA ,
		L_Name  
	From @TVLEG T
			OUTER APPLY (Select top 1 
							FF HFF 
							From @TVLEG F 
							Where 
							F.DateTakeoff<T.DateTakeoff 
							and F.Personal_ID=T.Personal_ID 
							Order by F.DateTakeoff Desc 
						) H
			OUTER APPLY (Select top 1 
							FF HLL 
							From @TVLEG F 
							Where 
							F.DateTakeoff>T.DateTakeoff 
							and F.Personal_ID=T.Personal_ID 
							Order by F.DateTakeoff ASC 
						) L
	WHere
		Case
		When ff=0 or ff=1 then T.DateTakeoff
		When Ff=2 or ff=3 then (Select top 1 
						F.DateTakeoff 
						From @TVLEG F 
						Where 
						F.DateTakeoff<T.DateTakeoff 
						and F.Personal_ID=T.Personal_ID 
						and F.ff=1 
						Order by F.DateTakeoff Desc 
						) 
		End <= @DT_CRbegin  
		and 
		Case
		When ff=0 or ff=3 then T.DateLanding
		When Ff=2 or ff=1 then (Select top 1 
								F.DateTakeoff 
								From @TVLEG F 
								Where 
								F.DateTakeoff>T.DateTakeoff 
								and F.Personal_ID=T.Personal_ID 
								and F.ff=3 
								Order by F.DateTakeoff ASC 
								) 
		End  between @DT_CRbegin and @DT_CREnd
		and t.FlightTaskReestrID is not null
		) TB
		ORDER BY
		TB.Personal_ID,
		TB.DateTakeoff
---------
SET @BeginParing=1
SET @NrowBegin=0
OPEN @CURFOOD
FETCH NEXT FROM @CURFOOD INTO 
	@Beg ,
	@Bend ,
	@Define ,
	@FlightTaskReestrID ,
	@Personal_ID ,
	@AP_BASE_ID ,
	@DateTakeoff ,
	@F_CodeIATA ,
	@F_Ap_AirPortID ,
	@F_Name ,
	@DateLanding ,
	@L_Ap_AirPortID ,
	@L_CodeIATA ,
	@L_Name 
WHILE @@FETCH_STATUS = 0
  BEGIN
	SET @STR=''
	IF @Beg='Beg' 
	BEGIN
		SET @BeginParing=0
		SET @Nleg=0
		SET @NN=@NN+1
		SET @STR=@STR+@PAIRING+CONVERT(nvarchar(25),@FlightTaskReestrID)+' "'+CONVERT(nvarchar(25),@FlightTaskReestrID)+'" '+'1/1/0/1/0/3/0 '+LTRIM(RTRIM(@F_CodeIATA))
		SET @NrowBegin=@NN
		INSERT INTO @REZCRW
		select @NN,@STR 
	END
	------------------------------------------
	--------------------------------------
		SET @CURLEG  = CURSOR SCROLL
			FOR 			
			Select
			PFTO.DateTakeoff,
			AP_TO.CodeIATA AP_TO,
			FL.Name,
			PFLA.DateLanding,
			AP_LA.CodeIATA AP_LA
			From Pr_FlightTaskReestrs TSK 
			INNER JOIN  Ap_PlanFlightAirPorts PFTO ON PFTO.Pr_FlightTaskReestrID = TSK.Pr_FlightTaskReestrID and PFTO.AirPortNumber=1
			INNER JOIN  Ap_PlanFlightAirPorts PFLA ON PFTO.Ap_PlanFlightID = PFLA.Ap_PlanFlightID and PFTO.AirPortNumber+1=PFLA.AirPortNumber
			INNER JOIN  Ap_PlanFlights PFL ON PFTO.Ap_PlanFlightID=PFL.Ap_PlanFlightID
			LEFT JOIN   Fl_Flights		FL ON PFL.Fl_FlightID=FL.Fl_FlightID
			LEFT JOIN   Ap_Airports AP_TO  ON PFTO.Ap_AirportID = AP_TO.Ap_AirportID
			LEFT JOIN   Ap_Airports AP_LA  ON PFLA.Ap_AirportID = AP_LA.Ap_AirportID
			Where TSK.Pr_FlightTaskReestrID=@FlightTaskReestrID
			OPEN @CURLEG
			FETCH NEXT FROM @CURLEG INTO 
				@TO_DateTakeoff ,
				@TO_CodeIATA ,
				@LegName ,
				@LA_DateLanding,
				@LA_CodeIATA 
			WHILE @@FETCH_STATUS = 0
			BEGIN
			If  @BeginParing=0
			BEGIN
				SET @STR1=''
				SET @Nleg=@Nleg+1
				SET @NN=@NN+1
				SET @STR1=@Activity_type+@DS+@ActivitySub_type+@DS+
							Case
							when @Nleg=1 then @Horizontal_lock1
							when @DateLanding =@LA_DateLanding and  @Bend='End'  then @Horizontal_lock3
							else @Horizontal_lock2
							end +@DS+
							Convert(nvarchar(10),@TO_DateTakeoff,112)+@DS+@TO_CodeIATA +@DS+dbo.PD_fn_ShowDateAsHHMM(@TO_DateTakeoff )+@DS+
							@CorrierCode+@DS+convert(nvarchar(5),@LegName) +@DS+'*'+@DS+'1'+@DS+dbo.PD_fn_ShowDateAsHHMM(@LA_DateLanding)+@DS+@LA_CodeIATA+@DS+Convert(nvarchar(10),@LA_DateLanding,112)
							+ ' #duty'+ convert(nvarchar(3),@Define)
					INSERT INTO @REZCRW
					select @NN,@STR1 
			END
			FETCH NEXT FROM @CURLEG INTO 
				@TO_DateTakeoff ,
				@TO_CodeIATA ,
				@LegName ,
				@LA_DateLanding,
				@LA_CodeIATA 
			END
		CLOSE @CURLEG
		DEALLOCATE @CURLEG 

    ---------------------------------------
	IF  @BeginParing=0 and  @Bend='End' 
	Begin
		---------------------------------------
		UPDATE @REZCRW 
		SET STR1=SUBSTRING(TT.STR1,charindex(@PAIRING,TT.STR1,1),LEN(@PAIRING)) + ' ' +  convert(nvarchar(2),@Nleg) + ' ' + SUBSTRING(TT.STR1,LEN(@PAIRING)+1,LEN(TT.STR1)-LEN(@PAIRING)+1)
		From @REZCRW TT where TT.NN=@NrowBegin 
		---------------------------------------
		SET  @BeginParing=1
		SET @NrowBegin=0
		SET @NN=@NN+1
		INSERT INTO @REZCRW
		select @NN,@EOPAIRING 
	End

  FETCH NEXT FROM @CURFOOD INTO 
	@Beg ,
	@Bend ,
	@Define ,
	@FlightTaskReestrID ,
	@Personal_ID ,
	@AP_BASE_ID ,
	@DateTakeoff ,
	@F_CodeIATA ,
	@F_Ap_AirPortID ,
	@F_Name ,
	@DateLanding ,
	@L_Ap_AirPortID ,
	@L_CodeIATA ,
	@L_Name 
  END
CLOSE @CURFOOD
DEALLOCATE @CURFOOD 

--SET @NN=@NN+1
--INSERT INTO @REZCRW
--select @NN,@EOSECTION 

Select * from @REZCRW
END