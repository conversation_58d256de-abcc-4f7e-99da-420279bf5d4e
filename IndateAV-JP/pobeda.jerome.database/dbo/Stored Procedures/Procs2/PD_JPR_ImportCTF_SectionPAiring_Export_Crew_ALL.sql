﻿------ =============================================
------ Author:		<Author,,Name>
------ Create date: <Create Date,,>
------ Description:	<Description,,>
------ =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ImportCTF_SectionPAiring_Export_Crew_ALL]
@DTbegin datetime='2018-08-01 00:00:00',  
@DTEnd datetime='2018-08-31 23:59:00',
@TYPECREW integer = 0,		--0 пилоты 1 --БП --11 все
@Parts integer=0,			 -- 0-полный период--1 -Только IN--2 -ТОлько OUT	
@Sourth integer=0,
@CRW integer=0				--0--,tp CREW
AS
BEGIN

--=================================Для тестирования=======================================
------Declare 
------@DTbegin datetime='2019-04-01 00:00:00',  
------@DTEnd datetime=  '2019-04-30 23:59:00',
------@TYPECREW integer =11,   --0 пилоты 1 --БП --11 все
------@Sourth integer=0,
------@Parts integer=0,   -- 0-полный период
------					-- 1 -Только IN
------					---2 -ТОлько OUT	
------@CRW integer=1   --0--,tp CREW--1 c CREW	
--====================================================
Declare
@PartsGD integer,
@DT_CRbegin datetime=dateadd(MM,-1, @DTbegin),  
@DT_CREnd datetime=dateadd(MM,+1, @DTEnd),
@NN integer=0

Set @PartsGD=0
--=========================================================================================
----select @DT_CRbegin, @DT_CREnd
---select @Parts, @PartsGD, @TYPECREW
--========================================================================================
DECLARE @PERIOD NVARCHAR(100)  ='PERIOD:'
DECLARE @PLAN_TYPE NVARCHAR(100)='PLAN TYPE: DATED' 
DECLARE @TIME_MODE NVARCHAR(100)='TIME MODE: UTC' 
---------------------------------------------------------------------------
DECLARE @SECTION_CREW NVARCHAR(100)  ='SECTION: CREW'
DECLARE @CREW NVARCHAR(100)='CREW:' 
DECLARE @EOCREW NVARCHAR(100)='EOCREW'
----------------------------------------------------------------------------
DECLARE @EOSECTION NVARCHAR(100)='EOSECTION'
---------------------------------------------
DECLARE @SECTION_PAIRING NVARCHAR(100)  ='SECTION: PAIRING'
DECLARE @PAIRING  NVARCHAR(100)='PAIRING:' 
DECLARE @EOPAIRING NVARCHAR(100)='EOPAIRING' 
----------------------------------------------------- 
DECLARE @SECTION_LEG NVARCHAR(100)  ='SECTION: LEG'
----------------------------------------------------- 
DECLARE @SECTION_GROUND_DUTY NVARCHAR(100)  ='SECTION: GROUND DUTY'
----------------------------------------------------- 
DECLARE @DS nvarchar(1)=' '
DECLARE @STRFlights NVARCHAR(100)=''
DECLARE @CorrierCode nvarchar(2)='DP'
----------------------------------------------------
DECLARE @Duty_code NVARCHAR(10)='*'
DECLARE @Lock_code NVARCHAR(10)='L' 
DECLARE @Environment_code NVARCHAR(10)='1' 
DECLARE @Activity_code NVARCHAR(10)='LV ' 
DECLARE @Activity_attribute NVARCHAR(10)='*' 
DECLARE @Activity_type NVARCHAR(1)='F' 
DECLARE @ActivitySub_type NVARCHAR(1)='L'
DECLARE @ActivitySub_typeDH NVARCHAR(1)='D'
DECLARE @Horizontal_lock1 NVARCHAR(1)='X'
DECLARE @Horizontal_lock2 NVARCHAR(1)='N'
DECLARE @Horizontal_lock3 NVARCHAR(1)='L'
DECLARE @FLIGHTCREW nvarchar(15)='1/1/0/0/0/0/0 '
DECLARE @CABINCREW nvarchar(15)='0/0/0/1/0/3/0 '

--=========================================================================================
--====================================Таблицы Выход.=======================================
Declare @REZCRW Table
(NN integer null,
 STR1 nvarchar(300) null)
--==========================================================================================
--====================================Таблицы Внутр.========================================
DEclare @TVLEGS Table
(
DateTakeoff datetime,
AP_TO nvarchar(5),
FlightName nvarchar(10),
DateLanding Datetime,
AP_LA nvarchar(5),
Pr_FlightTaskReestrID integer,
fl integer,
Ap_PlanFlightAirPortID integer
)

Declare @TBLPER Table
(
Pr_PersonnelID integer,
TableNumber nvarchar(10),
MainFunc nvarchar(5),
SubFunc nvarchar(5),
HomeBase nvarchar(5)
)
Declare @TBLACT Table
(
Pr_EventID integer,
Pr_PersonnelID integer,
Pr_EventTypeID integer,
TableNumber nvarchar(10),
CodeEng nvarchar(10),
DateBegin datetime,
DateEnd datetime,
AP nvarchar(3)
)

Declare @TBLGRN Table
(
Pr_EventID integer,
Pr_PersonnelID integer,
Pr_EventTypeID integer,
TableNumber nvarchar(10),
CodeEng nvarchar(10),
DateBegin datetime,
DateEnd datetime,
AP nvarchar(3),
MainFunc nvarchar(5)
)

--========================Формируем ТРИПЫ=================================================
Declare
@DTb datetime=dateadd(MM,-2, @DTbegin),
@DTe datetime=dateadd(MM,+2, @DTEnd)
EXEC PD_JPR_ImportTripPeriod @DTb,  @DTe
--========================================================================================

--==============================================================================================================================
--===================================Отбор персональных Данных===========================================
--==============================================================================================================================
INSERT INTO @TBLPER
Select 
*
From
(Select 
	PR.Pr_PersonnelID,
	-------------------------------------
	replace(Pr.TableNumber,'/','00')  TableNumber,  
	-------------------------------------
	  Case
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI','CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD')  then 'CP' --,'SI' 
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст') then 'FO'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
		else''
	End  MainFunc,
	-----------------------------------------------------------------------------------------
	Case
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('CP','П-Инсп','FI','DOLR','СР','SPI','COPLS','CRMI','CPP', 'КВС','КВС-стажер','ПИ','Нач ОПЛС','ПИ ИБП','ИТ','CRMIFD' )  then 'CP' --,'SI'
		When PR.Pr_CategoryTypeID =1 and STF.Code in ('FO','2П', 'FOP','2П','2Пст') then 'FO'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('СПБ','ИПБ','СБП','БПИ','Нач ОКЭ','CRMI','ИПКЭ','ИПБ СУБП', 'РГСТ','нач. ОКЭ','ИПБ','CRMICC','РГСТ','ИПБ','ИПКЭ','ИПБ СУБП') then 'SCC'
		When PR.Pr_CategoryTypeID =5 and STF.Code in ('ПБ','ПБст','ПБ','ПБст') then 'CC'
		else''
	End  SndFunc,
	isnull(AP.CodeIATA,'VKO') HomeBase
	-----------------------------------------------------------------------------------------
	From Pr_Personnels PR
	OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DT_CREnd >= T.DateBegin) and (@DTbegin<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
	LEft JOIN Pr_StaffTrees STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
	left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
	left JOIN  Ap_AirPorts AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
	OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=Pr.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DT_CREnd>= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
	OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=Pr.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
	OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DT_CREnd) post
	OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DT_CREnd >= T.DateBegin) and (@DTbegin<=T.DateFinish or T.DateFinish is null))) CH 
	Where 
	(( post.DateEnd IS NULL OR post.DateEnd >= @DTbegin) )  ------работающие в плановом периоде
	and isnumeric(replace(Pr.TableNumber,'/','00')) >''
	and (Pr.Pr_CategoryTypeID in (1,5)) --5
	and CH.Pr_ArmChairTypeID  is not null
	and case 
	when @TYPECREW=0 and PR.Pr_CategoryTypeID =1 then 1 
	when @TYPECREW=1 and PR.Pr_CategoryTypeID =5 then 1
	when @TYPECREW=11 and PR.Pr_CategoryTypeID in(1,5) then 1
	else 0
	end = 1
) tt where TT.MainFunc>'' 
--ORDER BY TT.TableNumber
--==============================================================================================================================
--===================================Отбор персональных DUTY (не путать GROUND DUTY)============================================
--==============================================================================================================================
INSERT INTO  @TBLACT 
SELECT 
EV.Pr_EventID,
PR.Pr_PersonnelID,
TP.Pr_EventTypeID,
replace(Pr.TableNumber,'/','00') TableNumber,
--TP.Name,
TP.CodeEng,
dateadd(hh,-3,EV.DateBegin),
dateadd(hh,-3,EV.DateEnd),
--EV.Comment,
Case
when  charindex('СКОЛКОВО',EV.Comment,1)>0 then 'VKO'
when  charindex('Аэрофлот',EV.Comment,1)>0 then 'SVO'
when  charindex('Санкт-',EV.Comment,1)>0 then 'LED'
when  charindex('Офис',EV.Comment,1)>0 then 'VKO'
when  charindex('Амстердам',EV.Comment,1)>0 then 'AMS'
--=============================================================
when  charindex('Краснодар',EV.Comment,1)>0 then 'KRR'
when  charindex('Самара',EV.Comment,1)>0 then 'KUF'
when  charindex('Красноярск',EV.Comment,1)>0 then 'KJA'
else 'VKO'
End AP

FROM Pr_Events EV
inner join Pr_EventTypes TP on TP.Pr_EventTypeID=EV.Pr_EventTypeID
inner join Pr_Personnels PR on PR.Pr_PersonnelID=EV.Pr_PersonnelID
inner join @TBLPER T on T.TableNumber=Pr.TableNumber
Where TP.CodeEng in  ('OFF','WOF','ROF','SICK','ULVE','RLVE','MLVE','LVE','LLAB','OFFW','MED','MED6','MED1','MED3','AVL','MED1CC','MED3CC','MED6CC') ---добавили MED1?3?6
and convert(date,EV.DateBegin) between convert(date,dateadd(dd,-0,@DT_CRbegin))  and  convert(date,dateadd(dd,0,@DT_CREnd))
and EV.RecordDeleted=0
and isnumeric(replace(Pr.TableNumber,'/','00')) >''
-------=======================загрузка приказов только на отпуск====================
------------------------------------------------------------------------------------
INSERT INTO  @TBLACT 
SELECT
RD.Pr_OrderID*100,
PR.Pr_PersonnelID,
RD.OrderType,
replace(Pr.TableNumber,'/','00') TableNumber,
RR.CodeEng,
dateadd(hh,-3,RD.DateBegin),
dateadd(hh,-3,RD.DateFinish),
T.HomeBase AP

FROM Pr_Orders RD
inner join	Pr_OrderTypes RTP	on RTP.Pr_OrderTypeID =RD.OrderType
inner join Pr_Personnels  PR	on PR.Pr_PersonnelID=RD.Pr_PersonnelID
Inner Join Pr_RestTypes  RR	ON RR.Pr_RestTypeID = RD.Pr_RestTypeID
inner join @TBLPER T on T.TableNumber=Pr.TableNumber

Where 
RD.OrderType in  (5) 
and RR.CodeEng in  ('ULVE','RLVE','MLVE','LVE') 
and convert(date,RD.DateBegin) between convert(date,dateadd(dd,-0,@DT_CRbegin))  and  convert(date,dateadd(dd,0,@DT_CREnd))
and isnumeric(replace(Pr.TableNumber,'/','00')) >''

--=================================================================================================
--===================================Отбор GROUND DUTY ============================================
--=================================================================================================
INSERT INTO  @TBLGRN 
SELECT 
EV.Pr_EventID,
PR.Pr_PersonnelID,
TP.Pr_EventTypeID,
replace(Pr.TableNumber,'/','00') TableNumber,
TP.CodeEng,
dateadd(hh,-3,EV.DateBegin),
dateadd(hh,-3,EV.DateEnd),
Case
when TP.CodeEng not in ('HBY') and charindex('СКОЛКОВО',EV.Comment,1)>0 then 'VKO'
when TP.CodeEng not in ('HBY') and charindex('Аэрофлот',EV.Comment,1)>0 then 'SVO'
when TP.CodeEng not in ('HBY') and charindex('Санкт-',EV.Comment,1)>0 then 'LED'
when TP.CodeEng not in ('HBY') and charindex('Офис',EV.Comment,1)>0 then 'VKO'
when TP.CodeEng not in ('HBY') and charindex('Амстердам',EV.Comment,1)>0 then 'AMS'
else 
	case
	 -----------------------------------------------определения АП для домашнего резерва---------------------------------
     when TP.CodeEng in ('HBY') then (select top 1 PP.CodeIATA
									  From dbo.PD_JPR_fn_GET_TripPersonAviabit(PR.Pr_PersonnelID) F 
									  INNER JOIN Ap_AirPorts PP ON PP.Ap_AirPortID=F.APLA
									  where F.DateLanding <=dateadd(hh,-3,EV.DateBegin) order by F.DateLanding desc 
									  )
	 else 'VKO'
	end
End AP,
T.MainFunc

FROM Pr_Events EV
inner join Pr_EventTypes TP on TP.Pr_EventTypeID=EV.Pr_EventTypeID
inner join Pr_Personnels PR on PR.Pr_PersonnelID=EV.Pr_PersonnelID
inner join @TBLPER T on T.TableNumber=Pr.TableNumber
Where TP.CodeEng in ('HR','AVL','HBY','INIT','SCT','DIFF','ICPT','IIT','FFS','IFFS','FFSO','IFFSO','TSC','ENGLV','RDG','SEC','EPG','EPW','CRM','RST','RCC','TRC','IRC','PTT','PTTCC','RSTI','RST')
and convert(date,EV.DateBegin) between convert(date,dateadd(dd,-0,@DT_CRbegin))  and  convert(date,dateadd(dd,0,@DT_CREnd))
and EV.RecordDeleted=0
and  isnumeric(replace(Pr.TableNumber,'/','00')) >''

---Перечень duty,  выполняемые в внутри трипа
--('HR','AVL','HBY','INIT','SCT','DIFF','ICPT','IIT','FFS','IFFS','FFSO','IFFSO','TSC','ENGLV','RDG','SEC','EPG','EPW','CRM','RST','RCC','TRC','IRC')

--===============================================Отбор GROUND DUTY  по резервным рейсам==================================================
INSERT INTO  @TBLGRN 
			Select
			PFTO.Ap_PlanFlightAirPortID,
			PR.Pr_PersonnelID,
			0, 
			replace(Pr.TableNumber,'/','00') TableNumber,
			Case
			when FL.Name='001' then 'DBY'
			when FL.Name='002' then 'NBY'
			end CodeEng,
			dateadd(hh,+3,PFTO.DateTakeoff),
			dateadd(hh,+3,PFLA.DateLanding),
			AP_TO.CodeIATA AP_TO,
			T.MainFunc
			From Pr_FlightTaskReestrs TSK 
            INNER JOIN   Ap_PlanFlightAirPorts PFTO ON PFTO.Pr_FlightTaskReestrID = TSK.Pr_FlightTaskReestrID and PFTO.AirPortNumber=1
			INNER JOIN   Ap_PlanFlightAirPorts PFLA ON PFTO.Ap_PlanFlightID = PFLA.Ap_PlanFlightID and PFTO.AirPortNumber+1=PFLA.AirPortNumber
			INNER JOIN   Ap_PlanFlights PFL ON PFTO.Ap_PlanFlightID=PFL.Ap_PlanFlightID
			INNER JOIN   Fl_Flights		FL ON PFL.Fl_FlightID=FL.Fl_FlightID
			INNER JOIN   Ap_Airports AP_TO  ON PFTO.Ap_AirportID = AP_TO.Ap_AirportID
			INNER JOIN   Ap_Airports AP_LA  ON PFLA.Ap_AirportID = AP_LA.Ap_AirportID
			INNER JOIN   Ap_PlanFlightAirPortCrews CR ON CR.Ap_PlanFlightAirPortID=PFTO.Ap_PlanFlightAirPortID
			inner join   Pr_Personnels PR on PR.Pr_PersonnelID=CR.Pr_PersonnelID
			inner join   @TBLPER T on T.TableNumber=Pr.TableNumber
			Where 
			convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-0,@DT_CRbegin))  and  convert(date,dateadd(dd,0,@DT_CREnd))
			and FL.Name in ('001','002')
			AND PFL.OnlyFinanceCalculation = 0
			AND PFL.Sh_ScheduleVariantTypeID IS NULL
			and PFL.Status & 256 <> 256  ----отмененные рейсы
			and  isnumeric(replace(Pr.TableNumber,'/','00')) >''

--=================================================================================================
----Select * From @TBLPER
----select * From @TBLACT
----select * From @TBLGRN
--=================================================================================================
DECLARE @CURFOOD CURSOR
DECLARE @CURLEG CURSOR
DECLARE 
@BeginParing integer=0,
@FlightTaskReestrID integer,
@PlanFlightAirPortCrewID integer,
@AP_BASE_ID integer,
@ff integer,
@DateTakeoff Datetime,
@F_CodeIATA nvarchar(5),
@F_Ap_AirPortID integer,
@F_Name nvarchar(5),
@DateLanding Datetime,
@L_Ap_AirPortID integer,
@L_CodeIATA nvarchar(5),
@L_Name nvarchar(5),
@Beg nvarchar(3),
@Bend nvarchar(3),
@Define integer,

@Nrow integer,
@Nleg integer,
@NrowBegin integer,
@TO_DateTakeoff Datetime,
@TO_CodeIATA nvarchar(5),
@LegName nvarchar(5),
@LA_DateLanding DateTime,
@LA_CodeIATA  nvarchar(5),
@Tabel nvarchar(10),
@TehNumber integer,
@TYPE_CREW nvarchar(30),
@CrewType integer,

@FlightTaskReestrID_1 integer,
@TehNumber_1 integer,
@TYPE_CREW_1 nvarchar(30),

@GRDateBegin datetime,
@GRDateEnd Datetime,
@GRCodeEng nvarchar(10),
@GR@AP nvarchar(10),

@Pr_EventID integer,
@DT_Pr_PersonnelID integer,
----@DT_Pr_EventTypeID integer, убил я
@DT_TableNumber nvarchar(10),
@DT_CodeEng nvarchar(10),
@DT_DateBegin datetime,
@DT_DateEnd datetime,
@DT_AP nvarchar(3),
@MainFunc nvarchar(5),
@CounPer integer,
@CP_cnt integer,
@FO_cnt integer,
@SCC_cnt integer,
@CC_cnt integer,

@Trip integer,
@Posion nvarchar(30),
@Pr_CategoryTypeID integer,
@Nflight integer,
@NDuty integer

----==================================================================================
----================================Заголовок CTF======================================
DECLARE 
@STR NVARCHAR(max)='',
@STR1 NVARCHAR(max)=''
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @PERIOD+' '+Convert(nvarchar(10),@DT_CRbegin,112)+' - '+Convert(nvarchar(10),@DT_CREnd,112))
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @PLAN_TYPE)
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @TIME_MODE)
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, '')
Set @NN=@NN+1
--=================================================================================================
--===============================Формирование секции CREW==========================================
--=================================================================================================
-----=============================== Начало Курсора  CREW  ================================

		SET @NN=@NN+1
		BEGIN TRANSACTION
			INSERT INTO @REZCRW
			select @NN, @SECTION_CREW
		COMMIT TRANSACTION

		DEclare
		@CrewID integer,  --PlanFlightAirPortCrewID
		@Tab nvarchar(10), --Tabel 
		@NNduty integer,
		@cnt integer
		SET @CURFOOD  = CURSOR SCROLL
		FOR (select 
			t.Pr_PersonnelID, t.TableNumber tab, 0 cnt 
			From @TBLPER T)
		OPEN @CURFOOD
		FETCH NEXT FROM @CURFOOD INTO 
			@CrewID, 
			@Tab, 
			@cnt
		WHILE @@FETCH_STATUS = 0
		BEGIN
				--=============================Подсчет количества всех DUty================================================================
				Set @NNduty=0
				Set @NNduty=isnull((Select	count(*)
								From @TBLACT T
								Where 
								T.TableNumber=@Tab
								and 
								(
								   (
										(T.DateBegin between @DT_CRbegin and @DTbegin)   
										and ((@PartsGD=1) or (@PartsGD=0))
								   )  ------Увелечение периода
								or (
										(T.DateBegin between @DTbegin and @DTEnd) and (@PartsGD=0)
								   ) 
								or (
										(T.DateBegin between @DTEnd and @DT_CREnd) and ((@PartsGD=2) or (@PartsGD=0))
								   )   
								)
								),0)
								+
							Isnull((select count(*) From  dbo.PD_JPR_fn_GET_TripPersonAviabit(@CrewID) T
									Where
									   ((T.DateTakeoff between @DT_CRbegin and @DTbegin) and ((@Parts=1) or (@Parts=0)))  ------Увелечение периода
									or ((T.DateTakeoff between @DTbegin and @DTEnd) and (@Parts=0)) 
									or ((T.DateTakeoff between @DTEnd and @DT_CREnd) and ((@Parts=2) or (@Parts=0))) 
									),0)
								+
							isnull((Select	count(*)
								From @TBLGRN T
								Where 
								T.TableNumber=@Tab
								and 
								(
								   ((T.DateBegin between @DT_CRbegin and @DTbegin)   and ((@PartsGD=1) or (@PartsGD=0)))  ------Увелечение периода
								or ((T.DateBegin between @DTbegin and @DTEnd) and (@PartsGD=0)) 
								or ((T.DateBegin between @DTEnd and @DT_CREnd) and ((@PartsGD=2) or (@PartsGD=0)))   
								)),0)
				--==============================================================================================
				SET @NN=@NN+1
				BEGIN TRANSACTION
				INSERT INTO @REZCRW
				select @NN, @CREW+' '+convert(nvarchar(4),@NNduty)+' "'+@Tab+'"' ---------------------------Запись
				COMMIT TRANSACTION
				--================================Наполнение наземными Duty===========================
							SET @CURLEG  = CURSOR SCROLL
							FOR 	
								Select	T.DateBegin,T.DateEnd,T.CodeEng,T.AP
								From @TBLACT T
								Where 
								T.TableNumber=@Tab
								and 
								(
								   ((T.DateBegin between @DT_CRbegin and @DTbegin)  and ((@PartsGD=1) or (@PartsGD=0)))  ------Увелечение периода
								or ((T.DateBegin between @DTbegin and @DTEnd) and (@PartsGD=0)) 
								or ((T.DateBegin between @DTEnd and @DT_CREnd) and ((@PartsGD=2) or (@PartsGD=0)))    
								)
							OPEN @CURLEG
							FETCH NEXT FROM @CURLEG INTO
								@GRDateBegin,
								@GRDateEnd,
								@GRCodeEng,
								@GR@AP
							WHILE @@FETCH_STATUS = 0
							BEGIN

								SET @NN=@NN+1
								BEGIN TRANSACTION
								INSERT INTO @REZCRW
								VALUES (@NN, ('L * 0 '+Convert(nvarchar(10),@GRDateBegin,112)+' '+@GR@AP+' '+ dbo.PD_fn_ShowDateAsHHMM(@GRDateBegin)+' '+@GRCodeEng+' * * '+ dbo.PD_fn_ShowDateAsHHMM(@GRDateEnd)+' '+@GR@AP+' '+Convert(nvarchar(10),@GRDateEnd,112)))  -----------------Запись
								COMMIT TRANSACTION

							FETCH NEXT FROM @CURLEG INTO 
								@GRDateBegin,
								@GRDateEnd,
								@GRCodeEng,
								@GR@AP
							END
						CLOSE @CURLEG
						DEALLOCATE @CURLEG 
				--================================Наполнение полетными Duty===========================
				--====================================================================================
					SET @CURLEG  = CURSOR SCROLL
						FOR 	
							select 
							T.Trip   Trip,
							T.Posion Posion,
							T.Tabel	 Tabel, 
							T.TehNumber TehNumber,
							T.Pr_CategoryTypeID Pr_CategoryTypeID
							From  dbo.PD_JPR_fn_GET_TripPersonAviabit(@CrewID) T
							Where
								(
									(T.DateTakeoff between @DT_CRbegin and @DTbegin) 
									and ((@Parts=1) or (@Parts=0))
								)  ------Увелечение периода
							or 
							   (
									(T.DateTakeoff between @DTbegin and @DTEnd) 
									and (@Parts=0)
							   ) 
							or 
							   (
							     (T.DateTakeoff between @DTEnd and @DT_CREnd) 
								 and ((@Parts=2) or (@Parts=0))
							   ) 
							Order By T.DateTakeoff							
						OPEN @CURLEG
						FETCH NEXT FROM @CURLEG INTO
							@Trip,
							@Posion,
							@Tabel,
							@TehNumber,
							@Pr_CategoryTypeID 
						WHILE @@FETCH_STATUS = 0
						BEGIN
							SET @NN=@NN+1
							BEGIN TRANSACTION
							INSERT INTO @REZCRW
							VALUES (@NN, ('1'+convert(nvarchar(20),@Trip)+@Tabel + convert(nvarchar(2),@Pr_CategoryTypeID)+convert(nvarchar(2),@TehNumber) + ' * 0 '+ @Posion))   
							COMMIT TRANSACTION
						FETCH NEXT FROM @CURLEG INTO 
							@Trip,
							@Posion,
							@Tabel,
							@TehNumber,
							@Pr_CategoryTypeID 
						END
					CLOSE @CURLEG
					DEALLOCATE @CURLEG 		
				--====================================================================================
				--================================Наполнение GROUND Duty===========================
						SET @CURLEG  = CURSOR SCROLL
						FOR 	
							Select 
							max(T.Pr_EventID),
							Count(T.Pr_PersonnelID), 
							T.CodeEng ,
							dateadd(hh,-3,T.DateBegin),
							dateadd(hh,-3,T.DateEnd),
							T.AP,
							sum(Case when T.MainFunc='CP' and T.TableNumber=@Tab  then 1 else 0 end)  CP_cnt,
							sum(Case when T.MainFunc='FO' and T.TableNumber=@Tab  then 1 else 0 end)  FO_cnt,
							sum(Case when T.MainFunc='SCC'and T.TableNumber=@Tab  then 1 else 0 end)  SCC_cnt,
							sum(Case when T.MainFunc='CC' and T.TableNumber=@Tab  then 1 else 0 end)  CC_cnt 
							From @TBLGRN T
							INNER JOIN (Select * From @TBLGRN where Pr_PersonnelID=@CrewID) TT on TT.DateBegin=T.DateBegin and TT.DateEnd=T.DateEnd and TT.CodeEng=T.CodeEng 
							Where 
							(
							T.Pr_PersonnelID=@CrewID 
							and
							((T.DateBegin between @DT_CRbegin and @DTbegin)  and ((@PartsGD=1) or (@PartsGD=0)))  ------Увелечение периода
							or ((T.DateBegin between @DTbegin and @DTEnd) and (@PartsGD=0)) 
							or ((T.DateBegin between @DTEnd and @DT_CREnd) and ((@PartsGD=2) or (@PartsGD=0)))   
							)  
							GROUP BY
							T.CodeEng,
							T.DateBegin,
							T.DateEnd,
							T.AP 
							Order By T.DateBegin
						OPEN @CURLEG
						FETCH NEXT FROM @CURLEG INTO
							@Pr_EventID,
							@CounPer,
							@DT_CodeEng,
							@DT_DateBegin,
							@DT_DateEnd,
							@DT_AP,
							@CP_cnt,
							@FO_cnt,
							@SCC_cnt,
							@CC_cnt 
						WHILE @@FETCH_STATUS = 0
						BEGIN
							SET @NN=@NN+1
							BEGIN TRANSACTION
							INSERT INTO @REZCRW
							VALUES (@NN, ('2'+convert(nvarchar(20),@Pr_EventID) + ' * 0 '+
									convert(nvarchar(5),@CP_cnt) +'/'+
									convert(nvarchar(5),@FO_cnt) +'/0/'+ 
									convert(nvarchar(5),@SCC_cnt)+'/0/'+ 
									convert(nvarchar(5),@CC_cnt) +'/0'))    
							COMMIT TRANSACTION
						FETCH NEXT FROM @CURLEG INTO 
							@Pr_EventID,
							@CounPer,
							@DT_CodeEng,
							@DT_DateBegin,
							@DT_DateEnd,
							@DT_AP,
							@CP_cnt,
							@FO_cnt,
							@SCC_cnt,
							@CC_cnt 
						END
					CLOSE @CURLEG
					DEALLOCATE @CURLEG 
				--====================================================================================
				SET @NN=@NN+1
				BEGIN TRANSACTION
				INSERT INTO @REZCRW
				select @NN, @EOCREW 
				COMMIT TRANSACTION
		FETCH NEXT FROM @CURFOOD INTO 
		@CrewID, 
		@Tab, 
		@cnt
		END
		CLOSE @CURFOOD
		DEALLOCATE @CURFOOD 

		SET @NN=@NN+1
		BEGIN TRANSACTION
			INSERT INTO @REZCRW
			select @NN,@EOSECTION 
		COMMIT TRANSACTION
--=================================================================================================
--===============================Завершение секции CREW============================================
--=================================================================================================

SET @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @SECTION_PAIRING)
--=================================================================================================
--=======================Формирование секции PAIRING ==============================================
--=================================================================================================
--=================================================================================================
--=======================Формирование Полетных PAIRING ============================================
--=================================================================================================

SET @CURFOOD  = CURSOR SCROLL
	FOR 	
		select 
		T.Trip   Trip,
		T.Posion Posion,
		T.Tabel	 Tabel, 
		T.TehNumber TehNumber,
		T.Pr_CategoryTypeID Pr_CategoryTypeID,
		T.Pr_PersonnelID Pr_PersonnelID,
		AP.CodeIATA AP_CodeIATA,
		T.Nflight Nflight,
		T.DateTakeoff,
		T.DateLanding

		From  dbo.PD_JPR_fn_GET_TripPersonAviabit(null) T
		INNER JOIN @TBLPER PP on PP.Pr_PersonnelID=T.Pr_PersonnelID
		LEFT JOIN Ap_AirPorts AP on AP.Ap_AirPortID=T.HBD_AP 
		Where
		(T.Trip is not null and T.Tabel	is not null)
		and
		(
			(
				(T.DateTakeoff between @DT_CRbegin and @DTbegin) 
				and ((@Parts=1) or (@Parts=0))
			)  ------Увелечение периода
			or 
			(
				(T.DateTakeoff between @DTbegin and @DTEnd) 
				and (@Parts=0)
			) 
			or 
			(
				(T.DateTakeoff between @DTEnd and @DT_CREnd) 
				and ((@Parts=2) or (@Parts=0))
			)
		)
		Order By 
		T.Tabel,
		T.DateTakeoff
									
	OPEN @CURFOOD
	FETCH NEXT FROM @CURFOOD INTO
		@Trip,
		@Posion,
		@Tabel,
		@TehNumber,
		@Pr_CategoryTypeID,
		@CrewId,
		@F_CodeIATA,
		@Nflight,
		@DateTakeoff,
		@DateLanding		 
	WHILE @@FETCH_STATUS = 0
	BEGIN
		SET @NN=@NN+1
		SET @STR=''
		+ @PAIRING + ' '+convert(nvarchar(3),@Nflight)+' '
		+ '1'+convert(nvarchar(20),@Trip)+@Tabel + convert(nvarchar(2),@Pr_CategoryTypeID)+convert(nvarchar(2),@TehNumber) 
		+' "FD'+ '1'+convert(nvarchar(20),@Trip)+@Tabel + convert(nvarchar(2),@Pr_CategoryTypeID)+convert(nvarchar(2),@TehNumber)+'"'
		+ ' '+ LTRIM(RTRIM(@Posion))
		+ ' '+ LTRIM(RTRIM(@F_CodeIATA))
		+ ' #' + @Tabel +' ' +convert(nvarchar(2),@TehNumber)
		BEGIN TRANSACTION
		INSERT INTO @REZCRW
		VALUES (@NN, @STR)
		COMMIT TRANSACTION
		---------------------------------------------
		---F L X 20180825 VKO 0620 DP 829 * 1 0935 TIV 20180825 #duty11 0
						SET @CURLEG  = CURSOR SCROLL
						FOR 	
							Select 
								T.DateTakeoff,
								APTO.CodeIATA AP_TO,
								T.FL_Name,
								T.DateLanding,
								APLA.CodeIATA AP_LA,
								T.NDuty,
								T.CrewType,
								ROW_NUMBER() over(PARTITION BY T.Pr_PersonnelID,T.Trip order by T.DateTakeoff) Nleg
							From PD_JPR_TripFlightPerson T
							INNER JOIN @TBLPER PP on PP.Pr_PersonnelID=T.Pr_PersonnelID
							LEFT JOIN Ap_AirPorts APTO on APTO.Ap_AirPortID=T.APTO
							LEFT JOIN Ap_AirPorts APLA on APLA.Ap_AirPortID=T.APLA
							Where 
							T.Pr_PersonnelID=@CrewID 
							and Trip=@Trip
							Order By T.DateTakeoff
						OPEN @CURLEG
						FETCH NEXT FROM @CURLEG INTO
							@TO_DateTakeoff,
							@TO_CodeIATA, 
							@LegName,
							@LA_DateLanding,
							@LA_CodeIATA, 
							@NDuty,
							@CrewType,
							@Nleg
						WHILE @@FETCH_STATUS = 0
						BEGIN
							SET @STR1=''
							SET @Nleg=@Nleg+1
							SET @NN=@NN+1
							SET @STR1=@Activity_type+@DS+
										Case
										when (@CrewType < 4) Then Case
																	When @TO_DateTakeoff between @DT_CRbegin and @DTbegin and (@LA_DateLanding between @DT_CRbegin and @DTbegin or @LA_DateLanding >=@DTbegin) Then @ActivitySub_type
																	else 'L' --'*'
																  End
										when (@CrewType = 4) Then @ActivitySub_typeDH
										end +@DS+
										--------------------------------------------------------------------------
										Case
										when @Nleg=1 then @Horizontal_lock1
										when @DateLanding =@LA_DateLanding then @Horizontal_lock3
										else @Horizontal_lock2
										end +@DS+
										------------------------------------------------------------------------------
										Convert(nvarchar(10),@TO_DateTakeoff,112)+@DS+@TO_CodeIATA +@DS+dbo.PD_fn_ShowDateAsHHMM(@TO_DateTakeoff )+@DS+
										@CorrierCode+@DS+convert(nvarchar(6),@LegName) +@DS+'*'+@DS+'1'+@DS+dbo.PD_fn_ShowDateAsHHMM(@LA_DateLanding)+@DS+
										@LA_CodeIATA+@DS+Convert(nvarchar(10),@LA_DateLanding,112)
										+ ' #duty'+ convert(nvarchar(6),@NDuty)+' '+convert(nvarchar(2),@CrewType) 
								BEGIN TRANSACTION
									INSERT INTO @REZCRW
									select @NN,@STR1 
								COMMIT TRANSACTION
								--BEGIN TRANSACTION
								--UPDATE @TVLEGS 
								--SET FL=1
								--Where LTRIM(RTRIM(FlightName))=LTRIM(RTRIM(@LegName)) and convert(datetime, DateTakeoff)=convert(datetime,@TO_DateTakeoff)
								----Select * From @TVLEGS Where LTRIM(RTRIM(FlightName))='441' --and DateTakeoff=@TO_DateTakeoff
								--COMMIT TRANSACTION
						---------------------------------------------------
						FETCH NEXT FROM @CURLEG INTO 
							@TO_DateTakeoff,
							@TO_CodeIATA, 
							@LegName,
							@LA_DateLanding,
							@LA_CodeIATA, 
							@NDuty,
							@CrewType,
							@Nleg
						END
					CLOSE @CURLEG
					DEALLOCATE @CURLEG 
		--------------------------------------------
		BEGIN TRANSACTION
			SET @NN=@NN+1
			INSERT INTO @REZCRW
			select @NN,@EOPAIRING 
		COMMIT TRANSACTION
	FETCH NEXT FROM @CURFOOD INTO 
		@Trip,
		@Posion,
		@Tabel,
		@TehNumber,
		@Pr_CategoryTypeID,
		@CrewId,
		@F_CodeIATA,
		@Nflight,
		@DateTakeoff,
		@DateLanding  
	END
CLOSE @CURFOOD
DEALLOCATE @CURFOOD 	

--=======================Формирование PAIRING WTH GROUND DUTY=========================================
DEclare @FFS nvarchar(10)=''
SET @CURFOOD  = CURSOR SCROLL
FOR  	
	Select 
	max(T.Pr_EventID),
	Count(T.Pr_PersonnelID), 
	CodeEng ,
	dateadd(hh,-3,T.DateBegin),
	dateadd(hh,-3,T.DateEnd),
	T.AP, 
	sum(Case when T.MainFunc='CP'  then 1 else 0 end)  CP_cnt,
	sum(Case when T.MainFunc='FO'  then 1 else 0 end)  FO_cnt,
	sum(Case when T.MainFunc='SCC' then 1 else 0 end)  SCC_cnt,
	sum(Case when T.MainFunc='CC'  then 1 else 0 end)  CC_cnt 
	From @TBLGRN T
	INNER JOIN @TBLPER P on P.Pr_PersonnelID=T.Pr_PersonnelID
	Where 
	(
	((T.DateBegin between @DT_CRbegin and @DTbegin)  and   ((@PartsGD=1) or (@PartsGD=0)))  ------Увелечение периода
	or ((T.DateBegin between @DTbegin and @DTEnd) and (@PartsGD=0)) 
	or ((T.DateBegin between @DTEnd and @DT_CREnd) and ((@PartsGD=2) or (@PartsGD=0)))    
	)  
	GROUP BY
	T.CodeEng,
	T.DateBegin,
	T.DateEnd,
	T.AP

	Order By DateBegin
OPEN @CURFOOD
FETCH NEXT FROM @CURFOOD INTO 
	@Pr_EventID,
	@CounPer,
	@DT_CodeEng,
	@DT_DateBegin,
	@DT_DateEnd,
	@DT_AP,
	@CP_cnt,
	@FO_cnt,
	@SCC_cnt,
	@CC_cnt 
WHILE @@FETCH_STATUS = 0
  BEGIN
  ------------------------------
		--=====================тренажеры===================
		IF (@DT_CodeEng='FFSO' or @DT_CodeEng='FFS')
		begin
			SET @FFS='"FFS"'
		end
		else
		begin
			SET @FFS=''
		end
		--=================================================
		SET @STR1=@PAIRING +' 1'+' ' +'2'+convert(nvarchar(30),@Pr_EventID) +' '+'"GD'+convert(nvarchar(30),@Pr_EventID)+'"'+' '+
		convert(nvarchar(3),@CP_cnt) +'/'+
		convert(nvarchar(3),@FO_cnt) +'/0/'+ 
		convert(nvarchar(3),@SCC_cnt)+'/0/'+ 
		convert(nvarchar(3),@CC_cnt) +'/0'+
		' '+@DT_AP+
		' '+@FFS                     
	BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@STR1 
	COMMIT TRANSACTION
        ---------------------------------
		---G * * 20030707 CPH 0800 OL1 * * 0 1400 CPH 20030707
		--SET @STR1='"'+@DT_CodeEng+'"'+' * '+ ' "'+@DT_AP+'" ' +dbo.PD_fn_ShowDateAsHHMM(@DT_DateBegin) + ' '+ convert(nvarchar(3),datediff(hh,@DT_DateBegin,@DT_DateEnd))+' 1234567 '+Convert(nvarchar(10),@DT_DateBegin,112)+' '+Convert(nvarchar(10),@DT_DateEnd,112)
		------------
		SET @STR1='G'+' * L ' + Convert(nvarchar(10),@DT_DateBegin,112)+' '+@DT_AP+' '+dbo.PD_fn_ShowDateAsHHMM(@DT_DateBegin)+' '+@DT_CodeEng+' * * 0 '+dbo.PD_fn_ShowDateAsHHMM(@DT_DateEnd)+' '+@DT_AP+' '+Convert(nvarchar(10),@DT_DateEnd,112) + ' #Duty1' 
	BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@STR1 
	COMMIT TRANSACTION
   -----------------------
	BEGIN TRANSACTION
		SET @NN=@NN+1
		INSERT INTO @REZCRW
		select @NN,@EOPAIRING 
	COMMIT TRANSACTION
	FETCH NEXT FROM @CURFOOD INTO
	@Pr_EventID,
	@CounPer, 
	@DT_CodeEng,
	@DT_DateBegin,
	@DT_DateEnd,
	@DT_AP,
	@CP_cnt,
	@FO_cnt,
	@SCC_cnt,
	@CC_cnt 
  END
CLOSE @CURFOOD
DEALLOCATE @CURFOOD 

--=======================Завершение PAIRING WTH GROUND DUTY=========================================
--=======================================================================================================

--====================================================================================================
--=======================Завершение Формирования секции PAIRING =================================================
--=====================================================================================================
SET @NN=@NN+1
BEGIN TRANSACTION
	INSERT INTO @REZCRW
	select @NN,'' 
COMMIT TRANSACTION
SET @NN=@NN+1
BEGIN TRANSACTION
	INSERT INTO @REZCRW
	select @NN,@EOSECTION 
COMMIT TRANSACTION
SET @NN=@NN+1
BEGIN TRANSACTION
	INSERT INTO @REZCRW
	select @NN,'' 
COMMIT TRANSACTION
--====================================================================================================
--=======================Формирование секции GROUND DUTY==============================================
--====================================================================================================
BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@SECTION_GROUND_DUTY 
COMMIT TRANSACTION
------
SET @CURFOOD  = CURSOR SCROLL
FOR  
	Select 
	CodeEng ,
	dateadd(hh,-3,DateBegin),
	dateadd(hh,-3,DateEnd),
	AP 
	From @TBLGRN
	Where 
	(
	((DateBegin between @DT_CRbegin and @DTbegin)   and ((@PartsGD=1) or (@PartsGD=0)))  ------Увелечение периода
	or ((DateBegin between @DT_CRbegin and @DT_CREnd) and (@PartsGD=0)) 
	or ((DateBegin between @DTEnd and @DT_CREnd) and (@PartsGD=2))   
	) 
	GROUP BY
	CodeEng,
	DateBegin,
	DateEnd,
	AP
    Order By DateBegin

OPEN @CURFOOD
FETCH NEXT FROM @CURFOOD INTO 
	@DT_CodeEng,
	@DT_DateBegin,
	@DT_DateEnd,
	@DT_AP
WHILE @@FETCH_STATUS = 0
  BEGIN
  --------------------------------------------------------------------------
	SET @STR1='"'+@DT_CodeEng+'"'+' * '+ ' "'+@DT_AP+'" ' +
	dbo.PD_fn_ShowDateAsHHMM(@DT_DateBegin) + ' '+ replace(dbo.ab_fn_ShowIntAsHHMM(datediff(ss,@DT_DateBegin,@DT_DateEnd)),':','')+
	' 1234567 '+Convert(nvarchar(10),@DT_DateBegin,112)+' '+Convert(nvarchar(10),@DT_DateBegin,112)  --@DT_DateEnd Заменили на @DT_DateBegin по требованию JP
	-----------------------------------------------------------------------
	BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@STR1 
	COMMIT TRANSACTION

	FETCH NEXT FROM @CURFOOD INTO 
	@DT_CodeEng,
	@DT_DateBegin,
	@DT_DateEnd,
	@DT_AP
  END
CLOSE @CURFOOD
DEALLOCATE @CURFOOD 
----------------------
BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@EOSECTION 
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,'' 
COMMIT TRANSACTION
----------------------
--====================================================================================================
--=======================Завершение Формирования секции GROUND DUTY====================================
--=====================================================================================================


--====================================================================================================
--=======================Формирование секции LEGS==============================================
--=====================================================================================================
DEclare @NE integer
SET @NN=@NN+10000
BEGIN TRANSACTION
INSERT INTO @REZCRW
EXEC PD_JPR_ImportCTF_Legs @DT_CRbegin, @DT_CREnd, @NN, @NE, 1
SET @NN=@NE+1
----INSERT INTO @REZCRW
----Select @NN,'EOSECTION'
COMMIT TRANSACTION

--================================ВЫВОД=============================================================
--====================================================================================================
Select T.STR1 From @REZCRW t order by t.NN asc
--==================================================
Select * from @TVLEGS 
where 
FL=0
and FlightName not in ('001','002')
and
(
   ((DateTakeoff between @DT_CRbegin and @DTbegin)  and (@Parts=1))
or ((DateTakeoff between @DTbegin and @DTEnd) and (@Parts=0)) 
or ((DateTakeoff between @DTEnd and @DT_CREnd) and (@Parts=2))   
)
END

