﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Qualification_restrication ]
@DTbegin datetime,  
@DTEnd datetime


AS
BEGIN
Declare
@DTending nvarchar(10)='30DEC2075',
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))

Declare @CC nvarchar(max)=
'3 
Srestriction_key "Restriction definition" ?"For AIRPORT: arrival station. For LANGUAGE: arrival country. FOR ETOPS: route (departure-arrival)", 
Erestriction_type "Restriction type" [ "LANGUAGE" ; "AIRPORT" ; "ETOPS" ], 
Srestriction_detail "Restriction detail" [ "?" ; "" ; "?" ] ?"Language code for LANGUAGE",'
--========================================
--========================================
DECLARE @TT table
(
TableNumber nvarchar(30),
Value nvarchar(30),
NameValue  nvarchar(300), 
STRLN nvarchar(max)
)

INSERT INTO @TT
VALUES('','','',@CC)

INSERT INTO @TT
SELECT 
'N/D' Tabel,
AP.CodeIATA APIATA,
MAX(AP.Name) Name,
'"'+AP.CodeIATA+'", '+'"AIRPORT", "" ;'
--FROM PD_JPR_TripFlightPerson ATT
--INNER JOIN Ap_AirPorts AP ON AP.Ap_AirPortID=ATT.APTO
FROM  Ap_AirPorts AP
Where 
--DateTakeoff between @DTACT and @DTACT2
--and 
AP.Class>=2
GROUP BY AP.CodeIATA
Order by AP.CodeIATA

Select * From @TT
End