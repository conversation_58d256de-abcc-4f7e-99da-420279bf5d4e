﻿ --=============================================
 ----Author:		<Author,,Name>
 ----Create date: <Create Date,,>
 ----Description:	<Description,,>
 --=============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Crew_not_fly_with]
@DTbegin datetime,  
@DTEnd datetime
AS
BEGIN
--======================================================
------Declare
------@DTbegin datetime='2019-06-01 00:00:00',  
------@DTEnd datetime='2019-06-30 23:59:00'
--======================================================
Declare
@DTending nvarchar(10)='30DEC2075',
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))

Declare @CC nvarchar(max)=
'/*

Table defining crew pairs that are not allowed to work together on the same leg. Each row in the table has two crew IDs that defines a forbidden pair.

*/
2
Screw1 "Crew ID 1" ?"Crew ID of first crew in forbidden pair",
Screw2 "Crew ID 2" ?"Crew ID of second crew in forbidden pair",
'

--=======================================================
DECLARE @TT table
(
TableNumber1 nvarchar(10),
TableNumber2 nvarchar(10),
STRLN nvarchar(max)
)

Declare @ATT table
(
TableNumber1 nvarchar(10),
TableNumber2 nvarchar(10)
)
--=======================================================

INSERT INTO @ATT
SELECT 
 Pers01.TableNumber TableNumber1,
 Pers02.TableNumber TableNumber2
 ----ISNULL(Pers01.LastName,'')+' '+ISNULL(Pers01.FirstName,'')+' '+ISNULL(Pers01.MiddleName,'') AS Record0,  
 ----ISNULL(Pers02.LastName,'')+' '+ISNULL(Pers02.FirstName,'')+' '+ISNULL(Pers02.MiddleName,'') AS Record1,  
 ----PCL.Priority AS Record2,
 ----PCL.Comment AS Comment, 
 ----PCL.Pr_CompatibilityLimitID AS RecordID, 
 ----PCL.Type 
 FROM Pr_CompatibilityLimits PCL 
 INNER JOIN Pr_Personnels Pers01 ON PCL.Pr_PersonnelID = Pers01.Pr_PersonnelID 
 INNER JOIN Pr_Personnels Pers02 ON PCL.Pr_PersonnelIDCompatibility = Pers02.Pr_PersonnelID 
 OUTER APPLY ab_fn_Pr_Personnel_Post(Pers01.Pr_PersonnelID, @DTbegin) post01 
 OUTER APPLY ab_fn_Pr_Personnel_Post(Pers02.Pr_PersonnelID, @DTbegin) post02 
 WHERE 
 PCL.Type =1
 and (  PCL.Pr_PersonnelID              IN ( SELECT Pr_PersonnelID FROM Pr_Personnels PPs OUTER APPLY ab_fn_Pr_Personnel_Post(PPs.Pr_PersonnelID, @DTbegin) post WHERE (1 = 1)  AND (( post.DateEnd IS NULL OR post.DateEnd > @DTbegin) )) 
 OR PCL.Pr_PersonnelIDCompatibility IN ( SELECT Pr_PersonnelID FROM Pr_Personnels PPs OUTER APPLY ab_fn_Pr_Personnel_Post(PPs.Pr_PersonnelID, @DTbegin) post WHERE (1 = 1)  AND (( post.DateEnd IS NULL OR post.DateEnd > @DTbegin) )))  AND (( post01.DateEnd IS NULL OR post01.DateEnd > @DTbegin) ) AND (( post02.DateEnd IS NULL OR post02.DateEnd > @DTbegin) ) 
 ORDER BY  Pers01.TableNumber,Pers02.TableNumber
--======================================
---Select * From  @ATT
--===========================================
INSERT INTO @TT
VALUES('','',@CC)
INSERT INTO @TT
Select 
	T.TableNumber1,
	T.TableNumber2,
	'"'+T.TableNumber1+'", '+
	'"'+T.TableNumber2+'";'
From @ATT T
Order by 	T.TableNumber1,T.TableNumber2
--==========================================
Select t.* FROM @TT T
--=============================================
--=============================================
END
			