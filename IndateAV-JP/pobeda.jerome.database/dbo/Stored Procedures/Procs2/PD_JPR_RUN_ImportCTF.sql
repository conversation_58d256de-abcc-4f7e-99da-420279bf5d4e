﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_RUN_ImportCTF]
@DTbegin datetime,  
@DTEnd datetime,
@TypeCrew integer=0


AS
BEGIN
------Declare 
------@DTbegin datetime='2018-05-26 00:00:00',  
------@DTEnd datetime='2018-06-12 23:59:00',
------@SCREW integer = 0,
------@CarIN integer = 1,
------@CarPeriod integer=1,
------@CarOUT integer=1,
------@SLEGS integer=1

--=========================================================
DECLARE @PERIOD NVARCHAR(100)  ='PERIOD:'
DECLARE @PLAN_TYPE NVARCHAR(100)='PLAN TYPE: DATED' 
DECLARE @TIME_MODE NVARCHAR(100)='TIME MODE: UTC' 
---------------------------------------------------------------------------
DECLARE @SECTION_CREW NVARCHAR(100)  ='SECTION: CREW'
DECLARE @CREW NVARCHAR(100)='CREW:' 
DECLARE @EOCREW NVARCHAR(100)='EOCREW'
----------------------------------------------------------------------------
DECLARE @EOSECTION NVARCHAR(100)='EOSECTION'
---------------------------------------------
DECLARE @SECTION_PAIRING NVARCHAR(100)  ='SECTION: PAIRING'
DECLARE @PAIRING  NVARCHAR(100)='PAIRING:' 
DECLARE @EOPAIRING NVARCHAR(100)='EOPAIRING' 
----------------------------------------------------- 
DECLARE @SECTION_LEG NVARCHAR(100)  ='SECTION: LEG'
----------------------------------------------------- 
DECLARE @SECTION_GROUND_DUTY NVARCHAR(100)  ='SECTION: GROUND DUTY'
----------------------------------------------------- 
DECLARE @DS nvarchar(1)=' '
DECLARE @STRFlights NVARCHAR(100)=''
DECLARE @CorrierCode nvarchar(2)='DP'
----------------------------------------------------
DECLARE @Duty_code NVARCHAR(10)='*'
DECLARE @Lock_code NVARCHAR(10)='L' 
DECLARE @Environment_code NVARCHAR(10)='1' 
DECLARE @Activity_code NVARCHAR(10)='LV ' 
DECLARE @Activity_attribute NVARCHAR(10)='*' 
DECLARE @Activity_type NVARCHAR(1)='F' 
DECLARE @ActivitySub_type NVARCHAR(1)='L'
DECLARE @Horizontal_lock1 NVARCHAR(1)='X'
DECLARE @Horizontal_lock2 NVARCHAR(1)='N'
DECLARE @Horizontal_lock3 NVARCHAR(1)='L'
--===============================================
Declare @NN integer=0
--===============================================
Declare @REZCRW Table
(NN integer null,
 STR1 nvarchar(300) null
 )
----========Заголовок CTF========================
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @PERIOD+' '+Convert(nvarchar(10),dateadd(dd,-6,@DTbegin),112)+' - '+Convert(nvarchar(10),dateadd(dd,12,@DTEnd),112))
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @PLAN_TYPE)
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @TIME_MODE)
--=====================Данные =====================
INSERT INTO @REZCRW
EXEC PD_JPR_ImportCTF_SectionPAiring  @DTbegin, @DTEnd, @TypeCrew

----INSERT INTO @REZCRW
----Select 10000,'EOSECTION'

INSERT INTO @REZCRW
EXEC PD_JPR_ImportCTF_Legs @DTbegin, @DTEnd
--================================================
INSERT INTO @REZCRW
Select 15000,'EOSECTION'
--================================================
Select * From @REZCRW		
End




