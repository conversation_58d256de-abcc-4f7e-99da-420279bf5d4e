﻿-- =============================================
-- Author:		Koroteev
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ImportTripPeriod]
@DTbeg datetime,
@DTEnd datetime,
@TimeVacant integer=11,
@CNTHomeBase integer
AS
BEGIN
---------------TEST-----------------------
----Declare
----@DTbeg datetime='2018-11-01 00:00:00',
----@DTEnd datetime='2019-05-31 23:59:00'
------------------------------------------
DEclare  @TR_FL TABLE
(	
Pr_PersonnelID integer,	
DateTakeoff datetime,
DateLanding datetime,
FL_Name nvarchar(10),
HBD_AP integer,
TRbegHB integer,
TRbegVKO integer,
Trip integer,
HB_Trip integer,
NDuty integer,
APTO integer,
APLA integer,
Ap_PlanFlightID integer,
Pr_FlightTaskReestrID integer,
Pred integer, 
Past integer,
Tabel nvarchar(10),
TehNumber integer, 	
CrewType integer,
Pr_CategoryTypeID integer,
Fl_FlightID integer  
)
--========================================================================================
--===============================Получаем данные==========================================
INSERT INTO @TR_FL
---Select * From dbo.PD_JPR_fn_GET_TripAviabit(@DTbeg,@DTEnd,11)
Select * From dbo.PD_JPR_fn_GET_TripAviabit_V1(@DTbeg,@DTEnd,11,@TimeVacant, @CNTHomeBase)
--==================================================================================
--===============================Обновляем данные==================================
UPDATE PD_JPR_TripFlightPerson 
Set
Pr_PersonnelID=TF.Pr_PersonnelID,	
DateTakeoff=TF.DateTakeoff,
DateLanding=TF.DateLanding ,
FL_Name=TF.FL_Name ,
HBD_AP=TF.HBD_AP ,
TRbegHB=TF.TRbegHB ,
TRbegVKO=TF.TRbegVKO ,
Trip=TF.Trip ,
HB_Trip=TF.HB_Trip ,
NDuty=TF.NDuty ,
APTO=TF.APTO ,
APLA=TF.APLA ,
Ap_PlanFlightID=TF.Ap_PlanFlightID ,
Pr_FlightTaskReestrID=TF.Pr_FlightTaskReestrID ,
Pred=TF.Pred , 
Past=TF.Past ,
Tabel=TF.Tabel ,
TehNumber=TF.TehNumber , 	
CrewType=TF.CrewType ,
Pr_CategoryTypeID=TF.Pr_CategoryTypeID ,
Fl_FlightID=TF.Fl_FlightID  
From PD_JPR_TripFlightPerson PTF
INNER join ( select * From @TR_FL tt where tt.Pr_PersonnelID is not null or tt.Pr_FlightTaskReestrID is not null ) TF 
									on  TF.Pr_PersonnelID=PTF.Pr_PersonnelID 
									and TF.Ap_PlanFlightID=PTF.Ap_PlanFlightID 
									--and TF.Pr_FlightTaskReestrID=PTF.Pr_FlightTaskReestrID 
Where  
PTF.Pr_PersonnelID is not null  
and PTF.Ap_PlanFlightID is not  null  
and TF.Pr_PersonnelID is not null  
and TF.Ap_PlanFlightID is not  null  

--========================================================================================
--===============================Вставляем новые данныее==================================
INSERT INTO PD_JPR_TripFlightPerson
Select
TF.Pr_PersonnelID,	
TF.DateTakeoff,
TF.DateLanding ,
TF.FL_Name ,
TF.HBD_AP ,
TF.TRbegHB ,
TF.TRbegVKO ,
TF.Trip ,
TF.HB_Trip ,
TF.NDuty ,
TF.APTO ,
TF.APLA ,
TF.Ap_PlanFlightID ,
TF.Pr_FlightTaskReestrID ,
TF.Pred , 
TF.Past ,
TF.Tabel ,
TF.TehNumber , 	
TF.CrewType ,
TF.Pr_CategoryTypeID ,
TF.Fl_FlightID    
From ( select * From @TR_FL tt where tt.Pr_PersonnelID is not null or tt.Ap_PlanFlightID is not null) TF  ---or tt.Pr_FlightTaskReestrID is not null 
Left join PD_JPR_TripFlightPerson PTF 
			on  TF.Pr_PersonnelID=PTF.Pr_PersonnelID 
			and TF.Ap_PlanFlightID=PTF.Ap_PlanFlightID 
			--and TF.Pr_FlightTaskReestrID=PTF.Pr_FlightTaskReestrID 
Where 
PTF.Pr_PersonnelID is null  
and PTF.Ap_PlanFlightID is null  
--and PTF.Pr_FlightTaskReestrID is null


--==========================================================================================
--=====================================================Удаление=============================
DELETE PD_JPR_TripFlightPerson 
Where 
Pr_PersonnelID is null  
or Ap_PlanFlightID is null  
----or Pr_FlightTaskReestrID is null
---------------------------------------------------------------------------------------------
DELETE PD_JPR_TripFlightPerson 
From PD_JPR_TripFlightPerson PTF
LEFT join ( select * From @TR_FL tt where tt.Pr_PersonnelID is not null or tt.Ap_PlanFlightID is not null ) TF --or tt.Ap_PlanFlightID is not null
			on  TF.Pr_PersonnelID=PTF.Pr_PersonnelID 
			and TF.Ap_PlanFlightID=PTF.Ap_PlanFlightID 
			--and TF.Pr_FlightTaskReestrID=PTF.Pr_FlightTaskReestrID 
Where 
TF.Pr_PersonnelID is null  
and TF.Ap_PlanFlightID is null  
--and TF.Pr_FlightTaskReestrID is null

--==============================ТЕСТ=======================================================
--======================Выбираем данные================================================
------Select Distinct 
------* 
------From PD_JPR_TripFlightPerson 
------Where Tabel='1259'
------ORDER BY
------Pr_PersonnelID,
------DateTakeoff, 
------HBD_AP, 
------Tabel,
------Ap_PlanFlightID,
------FL_Name,	
------TehNumber, 	
------CrewType,
------Pr_FlightTaskReestrID
END	