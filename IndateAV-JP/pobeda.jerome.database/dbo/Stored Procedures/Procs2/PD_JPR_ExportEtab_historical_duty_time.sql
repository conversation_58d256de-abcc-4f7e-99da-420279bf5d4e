﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_historical_duty_time]
@DTbegin datetime,  
@DTEnd datetime
AS
BEGIN
--=======================================================================
-- historical_duty_time.etab
--======================================================================
----3
----Screw_id "Crew ID",
----Aacc_date "Date for accumulated value",
----Rduty_time "Accumulated duty time between this time and start of planning period",

----"140051", 01JAN2016, 150:00;
----"140052", 01JAN2016, 180:00;
----"140055", 01JAN2016, 175:00;
----"140058", 01JAN2016, 90:00;
----"140059", 01JAN2016, 65:00;
----"140060", 01JAN2016, 160:00;
----"140061", 01JAN2016, 160:00;
----"140311", 01JAN2016, 160:00;
----"140343", 01JAN2016, 222:00;
----"140355", 01JAN2016, 150:00;
--=======================================================================

Declare @CC nvarchar(max)=
'3
Screw_id "Crew ID",
Aacc_date "Date for accumulated value",
Rduty_time "Accumulated duty time between this time and start of planning period",
'
--=========================================================================
DECLARE @TTSTR table
(
TableNumber nvarchar(30), 
Aacc_date nvarchar(30),  
Rblock_time nvarchar(30),
STRLN nvarchar(max) 
)

Declare @TT Table
(NN integer)
INSERT INTO @TT
VALUES(1),(2),(3),(4),(5),(6),(7),(8),(9),(10),(11),(12)
--===============================================================
Declare
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd))),
@DTBegin_1 datetime,
@DTend_1   datetime
------------------------
Set @DTBegin_1=DATETIMEFROMPARTS(year(@DTend),1,1,0,0,0,0)
Set @DTend_1=EOMONTH(dateadd(d,-1,@DTbegin))
--==============================================================

INSERT  INTO @TTSTR
VALUES('','','',@CC)
INSERT  INTO @TTSTR
Select
DT.crew_id, 
DT.Aacc_date,  
DT.Rduty_time,
--------------------------------------
'"'+DT.crew_id +'", '+
   DT.Aacc_date  +', '+
+  DT.Rduty_time +' ;' 
From
(SELECT
replace(P.TableNumber,'/','00') crew_id,
UPPER(isnull(format(FlightSum.AC_Date  ,'ddMMMyyyy'),'')) Aacc_date,
--dbo.ab_fn_ShowIntAsHHMM(FlightSum.TimeFlightWithEngine) Rblock_time
dbo.ab_fn_ShowIntAsHHMM(FlightSum.TimeWork) Rduty_time 
FROM (select * From Pr_Personnels cross join @TT t) P
OUTER APPLY (SELECT 
				dateadd(m,p.nn-1,@DTBegin_1) AC_Date,
				SUM(Landings) Landings, 
				SUM(LandingsOnDevices) LandingsOnDevices, 
				SUM(TimeOnDevices) TimeOnDevices, 
				SUM(TimeEngineWorkBefore + TimeFlight + TimeEngineWorkAfter) TimeFlightWithEngine, 
				SUM(TimeFlightNight) TimeFlightNight, 
				SUM(TimeFlightIndependent) TimeFlightIndependent, 
				SUM(TimeWork) TimeWork
				FROM Pr_PersonnelFlights fsum 
				WHERE 
				fsum.Pr_PersonnelID = P.Pr_PersonnelID 
				AND	fsum.DateTakeoffReal >= dateadd(m,p.nn-1,@DTBegin_1) and fsum.DateTakeoffReal <= EOMONTH(@DTend_1) 
				AND  fsum.Type <> 2  
				--AND  fsum.Type <> 3 
				AND  fsum.Type <> 1  
			) FlightSum 
OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=P.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
LEft JOIN Pr_StaffTrees STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
left JOIN  Ap_AirPorts AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=P.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DTACT2>= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=P.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
OUTER APPLY ab_fn_Pr_Personnel_Post(P.Pr_PersonnelID, @DTACT2) post 
OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=P.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null))) CH 

Where 
FlightSum.TimeFlightWithEngine  is not null 
and (( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
and isnumeric(replace(P.TableNumber,'/','00')) >''
and (P.Pr_CategoryTypeID in (1,5))
and CH.Pr_ArmChairTypeID  is not null

--======================================================
----------and (@DTACT<= ORD1.DateFinish or ORD1.DateFinish  is null) 
----------and (@DTACT <= ORD2.DateFinish or ORD2.DateFinish  is null)
----------and P.TableNumber >''
----------and ((@DTACT2 >= PST.DateBegin ) and (@DTACT<=PST.DateEnd or PST.DateEnd is null)) 
----------and (P.Pr_CategoryTypeID in (1,5))
------and (@DTACT<= ORD1.DateFinish or ORD1.DateFinish  is null) 
------and (@DTACT <= ORD2.Datebegin or ORD2.Datebegin  is null)
------and P.TableNumber >''
------and ((@DTACT2 >= PST.DateBegin) and (@DTACT<=PST.DateEnd or PST.DateEnd is null)) 
------and (P.Pr_CategoryTypeID in (1,5))
) DT
Order by 
DT.crew_id, 
DT.Aacc_date  
--=====================================
Select * From @TTSTR
END