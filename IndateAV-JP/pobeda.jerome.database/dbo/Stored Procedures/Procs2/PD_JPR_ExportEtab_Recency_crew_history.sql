﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ExportEtab_Recency_crew_history]
@DTbegin datetime,  
@DTEnd datetime

AS
BEGIN
--=======================================================================
--recency_crew_history
--=======================================================================
------3
------Screw_id "Crew Id",
------Sac_family "Aircraft family",
------Astart_date "Start Date",

------"001", "B737", 12JAN2016;
------"001", "B737", 10JAN2016;
------"001", "B737", 03JAN2016;
------"001", "B737", 03JAN2016;
------"001", "B737", 03JAN2016;
------"001", "B737", 23DEC2015;
--=======================================================================
--=======================================================================

----Declare
----@DTbegin datetime='2019-06-01 00:00:00',  
----@DTEnd datetime='2019-06-30 00:00:00'  

Declare
@DTending nvarchar(10)='30DEC2075',
@DTACT datetime=dateadd(d,1,EOMONTH(dateadd(m,-2,@DTbegin))),
@DTACT2 datetime=dateadd(d,1,EOMONTH(dateadd(m,1,@DTEnd)))

Declare @CC nvarchar(max)=
'3
Screw_id "Crew Id",
Sac_family "Aircraft family",
Astart_date "Start Date",'
DECLARE @TT table
(
TableNumber nvarchar(30),
Pr_CategoryTypeID integer,
Sac_family  nvarchar(30), 
Astart_date  nvarchar(30),
STRLN nvarchar(max)
)
--=========================================
Declare @ATT table
(
TableNumber nvarchar(30),
Pr_CategoryTypeID integer,
Sac_family  nvarchar(30), 
Astart_date  nvarchar(30)
)

INSERT INTO @ATT
Select ---DISTINCT
replace(Pr.TableNumber,'/','00') TableNumber,
Pr.Pr_CategoryTypeID,
isnull(AIR.PlnTypeIATA,' ') Sac_family,
UPPER(isnull(format(FlightSum.DateTakeoffReal ,'ddMMMyyyy'),'') ) Astart_date
-----------------------------------------------
From Pr_Personnels PR
OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
LEft JOIN Pr_StaffTrees   STF    on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
left JOIN  Pr_StaffTrees  PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
left JOIN  Ap_AirPorts    AP     on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2) and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin) order by Pr_Orders.Datebegin Desc) ORD1 
OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
OUTER APPLY (select top 1 * From Pr_PersChairs T where  T.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTACT2 >= T.DateBegin) and (@DTACT<=T.DateFinish or T.DateFinish is null))) CH 
OUTER APPLY (SELECT TOP (case when Pr.Pr_CategoryTypeID=1 then 3 else 1 end)
				fsum.DateTakeoffReal
				FROM Pr_PersonnelFlights fsum 
				WHERE 
				fsum.Pr_PersonnelID = Pr.Pr_PersonnelID 
				and fsum.DateTakeoffReal is not null
				and  fsum.Ap_PlanFlightAirportID >0
				AND	 convert(date,fsum.DateTakeoffReal) between convert(date,dateadd(d,-90,@DTbegin)) and convert(date,dateadd(d,0,dateadd(m,0,@DTEnd)))
				--AND	 convert(date,fsum.DateTakeoffReal) between convert(date,dateadd(d,1,EOMONTH(dateadd(dd,-120,getdate())))) and convert(date,dateadd(d,0,EOMONTH(dateadd(m,0,getdate()))))
				AND  fsum.Type <> 2  
				AND  fsum.Type <> 3 
				AND  fsum.Type <> 1 
				ORDER BY fsum.DateTakeoffReal Desc 
			) FlightSum 
OUTER APPLY (select top 1 isnull(CER.Pr_EducationTypeID,0) Fl, PL.PlnTypeIATA ,CER.DateBegin, CER.DateEnd  
			From Pr_Certifications CER 
			left join At_PlnTypes PL ON PL.Name=CER.PlnTypeModifName
			where 
			CER.Pr_PersonnelID=PR.Pr_PersonnelID 
			and  CER.Pr_EducationTypeID in (43, 150)
			and  PL.PlnTypeIATA >''
			and ((@DTACT >= CER.DateBegin or @DTACT <= CER.DateBegin) and (@DTACT <=CER.DateEnd or CER.DateEnd is null))
			ORDER BY CER.DateBegin DESC
			) AIR
OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTACT2) post 
Where 
(( post.DateEnd IS NULL OR post.DateEnd > @DTACT) )
and isnumeric(replace(Pr.TableNumber,'/','00')) >''
and (Pr.Pr_CategoryTypeID in (1,5))
and CH.Pr_ArmChairTypeID  is not null
--===================================================

------Where 
------(@DTACT<= ORD1.DateFinish or ORD1.DateFinish  is null) 
------and (@DTACT <= ORD2.Datebegin or ORD2.Datebegin  is null)
------and Pr.TableNumber >''
------and ((@DTACT2 >= PST.DateBegin) and (@DTACT<=PST.DateEnd or PST.DateEnd is null)) 
------and (Pr.Pr_CategoryTypeID in (5))
------and FlightSum.DateTakeoffReal  is not null
------===============================================
INSERT INTO @TT
VALUES('',null,'','',@CC)
INSERT INTO @TT
Select --DISTINCT
	T.TableNumber,
	T.Pr_CategoryTypeID,
	T.Sac_family,
	T.Astart_date, 
	'"'+T.TableNumber+'", '+
	'"'+T.Sac_family   +'", '+
		T.Astart_date  +';'
From @ATT T
Where T.Astart_date <> ''
Order by TableNumber
------------------------------------------
Select * FROM @TT T
Order by T.TableNumber, T.Astart_date
end
