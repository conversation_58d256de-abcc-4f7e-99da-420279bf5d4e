﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[PD_JPR_ImportCTF_SectionPAiring_Export]
@DTbegin datetime,  
@DTEnd datetime,
@TYPECREW integer = 0,
@Parts integer=0 ,  -- 0-полный период--1 -Только IN--2 -ТОлько OUT
@Sourth integer=0	
--====================================================================================
AS
BEGIN
--=============================TEST==============================
------Declare 
------@DTbegin datetime='2018-09-01 00:00:00',  
------@DTEnd datetime='2018-09-30 23:59:00',
------@TYPECREW integer = 0,
------@Parts integer=0,   -- 0-полный период -- 1 -Только IN
------@Sourth integer=0   --0 --ПЗ  1--JP 

--====================================Таблицы Выход.==============================
Declare @REZCRW Table
(NN integer null,
 STR1 nvarchar(300) null)

--====================================Таблицы Внутр.===============================
Declare @TBTSK Table
(
	----FlightTaskReestrID integer null,
	----Ap_PlanFlightAirPortCrewID integer null,
	----Bap integer

	FlightTaskReestrID integer null,
	Ap_PlanFlightAirPortCrewID integer null,
	Tabel nvarchar(10),
	TehNumber integer,
	Bap integer,
	CrewType integer
)

Declare @TVLEG table
(
FlightTaskReestrID integer,
Ap_PlanFlightAirPortCrewID integer,
Tabel nvarchar(10),
TehNumber integer,
AP_BASE_ID integer,
ff integer,
DateTakeoff Datetime,
F_CodeIATA nvarchar(5),
F_Ap_AirPortID integer,
F_Name nvarchar(5),
DateLanding Datetime,
L_Ap_AirPortID integer,
L_CodeIATA nvarchar(5),
L_Name nvarchar(5),
CrewType integer
)

DEclare @TVLEGS Table
(
DateTakeoff datetime,
AP_TO nvarchar(5),
FlightName nvarchar(10),
DateLanding Datetime,
AP_LA nvarchar(5),
Pr_FlightTaskReestrID integer,
fl integer,
Ap_PlanFlightAirPortID integer
)

------------------------------------------------------------------------
---------------------------------------------------------------------------
DECLARE @PERIOD NVARCHAR(100)  ='PERIOD:'
DECLARE @PLAN_TYPE NVARCHAR(100)='PLAN TYPE: DATED' 
DECLARE @TIME_MODE NVARCHAR(100)='TIME MODE: UTC' 
---------------------------------------------------------------------------
DECLARE @SECTION_CREW NVARCHAR(100)  ='SECTION: CREW'
DECLARE @CREW NVARCHAR(100)='CREW:' 
DECLARE @EOCREW NVARCHAR(100)='EOCREW'
----------------------------------------------------------------------------
DECLARE @EOSECTION NVARCHAR(100)='EOSECTION'
---------------------------------------------
DECLARE @SECTION_PAIRING NVARCHAR(100)  ='SECTION: PAIRING'
DECLARE @PAIRING  NVARCHAR(100)='PAIRING:' 
DECLARE @EOPAIRING NVARCHAR(100)='EOPAIRING' 
----------------------------------------------------- 
DECLARE @SECTION_LEG NVARCHAR(100)  ='SECTION: LEG'
----------------------------------------------------- 
DECLARE @SECTION_GROUND_DUTY NVARCHAR(100)  ='SECTION: GROUND DUTY'
----------------------------------------------------- 
DECLARE @DS nvarchar(1)=' '
DECLARE @STRFlights NVARCHAR(100)=''
DECLARE @CorrierCode nvarchar(2)='DP'
----------------------------------------------------
DECLARE @Duty_code NVARCHAR(10)='*'
DECLARE @Lock_code NVARCHAR(10)='L' 
DECLARE @Environment_code NVARCHAR(10)='1' 
DECLARE @Activity_code NVARCHAR(10)='LV ' 
DECLARE @Activity_attribute NVARCHAR(10)='*' 
DECLARE @Activity_type NVARCHAR(1)='F' 
DECLARE @ActivitySub_typeDH NVARCHAR(1)='D'
DECLARE @DH NVARCHAR(1)='D'
DECLARE @ActivitySub_type NVARCHAR(1)='L'
DECLARE @Horizontal_lock1 NVARCHAR(1)='X'
DECLARE @Horizontal_lock2 NVARCHAR(1)='N'
DECLARE @Horizontal_lock3 NVARCHAR(1)='L'
DECLARE @FLIGHTCREW nvarchar(15)='1/1/0/0/0/0/0 '
DECLARE @CABINCREW nvarchar(15)='0/0/0/1/0/3/0 '


DECLARE @CURFOOD CURSOR
DECLARE @CURLEG CURSOR
Declare
@DT_CRbegin datetime=dateadd(dd,-6, @DTbegin),  
@DT_CREnd datetime=dateadd(dd,+12, @DTEnd),
@CREWSTR nvarchar(15)=''
 
If @TYPECREW=0  Set @CREWSTR=@FLIGHTCREW
ELSE Set @CREWSTR=@CABINCREW

-----------------------------------------
--Select @DT_CRbegin, @DT_CREnd 
-----------------------------------------
DECLARE @JPTPAR table
(
JP_ChainPlnID  integer,
JP_DateBegin  datetime,
JP_DateEnd datetime,
JP_Pairing_ID integer,
JP_CodeIATA nvarchar(5)
)

DECLARE
@JP_ChainPlnID  integer,
@JP_DateBegin  datetime,
@JP_DateEnd datetime,
@JP_Pairing_ID nvarchar(30),
@JP_CodeIATA nvarchar(5),
@JP_CNT integer


--=======================================Подготовка данных для ТРИПОВ на основании JP================================
--===================================================================================================================
--===================================================================================================================
IF @Sourth=1
BEGIN
INSERT INTO @JPTPAR
Select
	CH.Ak_ChainPlnID, 
	CH.DateBegin,
	CH.DateEnd,
	substring(CH.Comment,charindex('JR--',CH.Comment,1)+5,len(CH.Comment)) Pairing_ID,
	APT.CodeIATA
	From Ak_ChainPlns CH
	OUTER APPLY (select top 1 AP.CodeIATA From Ak_ChainPlnFlightRoutes CHR
					Inner Join Ap_AirPorts AP ON AP.Ap_AirPortID=CHR.Ap_AirPortIDTakeOff
					Where CHR.Ak_ChainPlnID=CH.Ak_ChainPlnID
					ORDER BY CHR.DayOffset asc,TimeTakeOff asc 
				 ) APT
	Where 
	(@Parts=1) and (@Sourth=1) 
	and (convert(datetime,CH.DateBegin)  >= @DT_CRbegin and convert(datetime,CH.DateBegin)< @DTbegin)  
	and (convert(datetime,CH.DateEnd) >= @DTbegin)
END
---select * From @JPTPAR
--=======================================Завершение данных для ТРИПОВ на основании JP================================
--===================================================================================================================
--===================================================================================================================
---
---
---
--=======================================Подготовка данных для ТРИПОВ на основании ПЗ================================
--===================================================================================================================
--===================================================================================================================
IF @Sourth=0
BEGIN
			INSERT INTO @TVLEGS
			Select DISTINCT 
			PFTO.DateTakeoff,
			AP_TO.CodeIATA AP_TO,
			FL.Name,
			PFLA.DateLanding,
			AP_LA.CodeIATA AP_LA,
			TSK.Pr_FlightTaskReestrID,
			0,
			PFTO.Ap_PlanFlightAirPortID
			From Pr_FlightTaskReestrs TSK 
			INNER JOIN  Ap_PlanFlightAirPorts PFTO ON PFTO.Pr_FlightTaskReestrID = TSK.Pr_FlightTaskReestrID and PFTO.AirPortNumber=1
			INNER JOIN  Ap_PlanFlightAirPorts PFLA ON PFTO.Ap_PlanFlightID = PFLA.Ap_PlanFlightID and PFTO.AirPortNumber+1=PFLA.AirPortNumber
			INNER JOIN  Ap_PlanFlights PFL ON PFTO.Ap_PlanFlightID=PFL.Ap_PlanFlightID
			LEFT JOIN   Fl_Flights		FL ON PFL.Fl_FlightID=FL.Fl_FlightID
			LEFT JOIN   Ap_Airports AP_TO  ON PFTO.Ap_AirportID = AP_TO.Ap_AirportID
			LEFT JOIN   Ap_Airports AP_LA  ON PFLA.Ap_AirportID = AP_LA.Ap_AirportID
			Where 
			convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
			AND PFL.OnlyFinanceCalculation = 0
			AND PFL.Sh_ScheduleVariantTypeID IS NULL
			and PFL.Status & 256 <> 256  ----отмененные рейсы
			AND ISNULL(FL.FlightVariant,0) = 0 
			AND PFL.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки


			--=======================Полетные задания с обезличинным капитаном================================================
			--==================Проблема по обезличинным капитанам нет привязки к BaseHome
			--================================================================================================================
			Insert INTO @TBTSK
			Select	DISTINCT	
					------PFA.Pr_FlightTaskReestrID,
					------MAX(isnull(PR.Pr_PersonnelID,1))  Ap_PlanFlightAirPortCrewID,--isnull(PR.Pr_PersonnelID,1) Ap_PlanFlightAirPortCrewID,
					------MAX(PTR.Ap_AirPortID) Bap  --PTR.Ap_AirPortID
					--------PFC.*	
					PFA.Pr_FlightTaskReestrID,
					isnull(PR.Pr_PersonnelID,1)  Ap_PlanFlightAirPortCrewID,--isnull(PR.Pr_PersonnelID,1) Ap_PlanFlightAirPortCrewID,
					isnull(PR.TableNumber,'1') Tabel,
					isnull(PFC.OrderNumber,0) TehNumber, 
					MAX(isnull(PTR.Ap_AirPortID,58)) BAP,  --PTR.Ap_AirPortID
					isnull(PFC.CrewType,11)
				FROM Ap_PlanFlights PFL
					INNER JOIN Ap_PlanFlightAirPorts PFA ON PFA.Ap_PlanFlightID = PFL.Ap_PlanFlightID and PFA.AirPortNumber=1
					INNER JOIN Fl_Flights			   FL ON PFL.Fl_FlightID=FL.Fl_FlightID
					--OUTER APPLY (Select top 1 * From Ap_PlanFlightAirPortCrews TT where TT.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID and TT.OrderNumber=1  and tt.CrewType=0 Order by TT.CrewType) PFC
					LEFT JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
					LEFT JOIN Pr_Personnels PR on  PFC.Pr_PersonnelID=PR.Pr_PersonnelID
					LEFT JOIN Pr_PersPosts Post ON PR.Pr_PersonnelID = Post.Pr_PersonnelID AND (Post.DateBegin < getutcdate() and (Post.DateEnd is null or Post.DateEnd > getutcdate()))
					LEFT JOIN Pr_StaffTrees TR ON Post.Pr_StaffTreeID = TR.Pr_StaffTreeID  
					LEFT  JOIN Pr_StaffTrees PTR ON PTR.Pr_StaffTreeID = TR.Pr_StaffTreeIDParent 
					--LEFT JOIN Pr_ArmChairTypes ON PFC.Pr_ArmChairTypeID = Pr_ArmChairTypes.Pr_ArmChairTypeID 
					--LEFT JOIN Pr_ArmChairRoles ON Pr_ArmChairRoles.Pr_ArmChairRoleID = PFC.Pr_ArmChairRoleID 
				WHERE 
					convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
					AND PFL.OnlyFinanceCalculation = 0
					AND PFL.Sh_ScheduleVariantTypeID IS NULL
					and PFL.Status & 256 <> 256  ----отмененные рейсы
					AND ISNULL(FL.FlightVariant,0) = 0 
					AND PFL.Fl_FlightMeanTypeID  IN (30)  --Только пассажирские перевозки
				------	--------------------------------------
				------	and (PFC.OrderNumber=1)
				------	and (PFC.CrewType=@TYPECREW)
				------GROUP BY  PFA.Pr_FlightTaskReestrID --,  isnull(PR.Pr_PersonnelID,1)  , PTR.Ap_AirPortID --, --, PTR.Ap_AirPortID   ,isnull(PR.Pr_PersonnelID,1)
						------------------------------------------------------------------
					--====================================NEW=======================================
					and
					( 
						(PFC.CrewType=@TYPECREW) and (@TYPECREW=0) and (PFC.OrderNumber in (1,2,3)) and (PR.Pr_CategoryTypeID=1) ----указываем позицию АВИАБИТ
						or 
						(PFC.CrewType=@TYPECREW) and (@TYPECREW=1) and (PFC.OrderNumber in (1,2,3,4,5,6,7)) and (PR.Pr_CategoryTypeID=5) ----указываем позицию АВИАБИТ
						----------------------------------------- учет пассажиров в экипаже ------------------------------------------------------------------------------
						or
						(PFC.CrewType=4) and (PFC.Pr_ArmChairTypeID is null) and (@TYPECREW=0) and (PR.Pr_CategoryTypeID=1) ----указываем позицию АВИАБИТ пассажир летный and (PFC.Pr_ArmChairTypeID is null) 
						or
						(PFC.CrewType=4) and (PFC.Pr_ArmChairTypeID is null)  and (@TYPECREW=1) and (PR.Pr_CategoryTypeID=5) ----указываем позицию АВИАБИТ пассажир кабинныйand (PFC.Pr_ArmChairTypeID is null)
						---------------------------------------------------------------------------------------------------------------------------------------------------
					)
				GROUP BY  
				PFA.Pr_FlightTaskReestrID, 
				isnull(PR.Pr_PersonnelID,1), 
				isnull(PR.TableNumber,'1'), 
				isnull(PFC.OrderNumber,0), 
				isnull(PFC.CrewType,11)          
				--,  isnull(PR.Pr_PersonnelID,1)  , PTR.Ap_AirPortID --, --, PTR.Ap_AirPortID   ,isnull(PR.Pr_PersonnelID,1)
			--==============================================END NEW=============================================================
			----================================================================================================================
			---select * From @TBTSK
			--=========================================
			--=========================================
			--=====================Формирование Данных по первому и последнему рейса в  Полетном задании========================
			--==================================================================================================================
			INSERT INTO @TVLEG
			Select 
			T.FlightTaskReestrID,
			T.Ap_PlanFlightAirPortCrewID,
			T.Tabel,
			T.TehNumber,
			ISNULL(T.Bap,58),
			------Case ISNULL(T.Bap,58)
			-----------------------------Определение флагов включения рейса в один TRIP ------------------------------------
			------WHEN 58 then 
			------		Case
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0 --BB вылет прилет из базы в базу
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1 --BN
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2 --NN
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3 --NB
			------		End
			------WHEN 86 then
			------		Case
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3
			------		End
			------WHEN 77 then
			------		Case
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='KJA')    and (LR.CodeIATA='VKO' or LR.CodeIATA='KJA')    then 0
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='KJA')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'KJA') then 1
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'KJA') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'KJA') then 2
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'KJA') and (LR.CodeIATA='VKO' or LR.CodeIATA='KJA')    then 3
			------		End
			------WHEN 6 then
			------		Case
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='KRR')    and (LR.CodeIATA='VKO' or LR.CodeIATA='KRR')    then 0
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='KRR')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'KRR') then 1
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'KRR') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'KRR') then 2
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'KRR') and (LR.CodeIATA='VKO' or LR.CodeIATA='KRR')    then 3
			------		End
			------ELSE 
			------		Case
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0
			------		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2
			------		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3
			------		End
			------END ff,

			Case ISNULL(T.Bap,58)
			-----------------------Определение флагов включения рейса в один TRIP ------------------------------------
			WHEN 58 then ---VKO
					Case
					When (FR.CodeIATA='VKO' ) and (LR.CodeIATA='VKO' )		then 0 --BB вылет прилет из базы в базу
					When (FR.CodeIATA='VKO' ) and (LR.CodeIATA<>'VKO')		then 1 --BN
					When (FR.CodeIATA<>'VKO') and (LR.CodeIATA<>'VKO')		then 2 --NN
					When (FR.CodeIATA<>'VKO') and (LR.CodeIATA='VKO' )		then 3 --NB
					End
			WHEN 86 then ---LED
					Case
					When (FR.CodeIATA='LED')  and (LR.CodeIATA='LED' )		then 0
					When (FR.CodeIATA='LED')  and (LR.CodeIATA<>'LED')		then 1
					When (FR.CodeIATA<>'LED') and (LR.CodeIATA<>'LED')		then 2
					When (FR.CodeIATA<>'LED') and (LR.CodeIATA='LED' )		then 3
					End
			WHEN 77 then ---KJA
					Case
					When (FR.CodeIATA='KJA')    and (LR.CodeIATA='KJA' )    then 0
					When (FR.CodeIATA='KJA')    and (LR.CodeIATA<>'KJA')	then 1
					When (FR.CodeIATA<>'KJA')   and (LR.CodeIATA<>'KJA')	then 2
					When (FR.CodeIATA<>'KJA')   and (LR.CodeIATA='KJA' )    then 3
					End
			WHEN 6 then ---KRR
					Case
					When (FR.CodeIATA='KRR')    and (LR.CodeIATA='KRR' )    then 0
					When (FR.CodeIATA='KRR')    and (LR.CodeIATA<>'KRR')	then 1
					When (FR.CodeIATA<>'KRR')	and (LR.CodeIATA<>'KRR')	then 2
					When (FR.CodeIATA<>'KRR')	and (LR.CodeIATA='KRR' )    then 3
					End
			----ELSE 
			----		Case
			----		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 0
			----		When (FR.CodeIATA='VKO' or FR.CodeIATA='LED')    and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 1
			----		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA<>'VKO' and LR.CodeIATA<>'LED') then 2
			----		When (FR.CodeIATA<>'VKO' and FR.CodeIATA<>'LED') and (LR.CodeIATA='VKO' or LR.CodeIATA='LED')    then 3
			----		End
			ELSE 
					Case
					When (FR.CodeIATA='VKO' ) and (LR.CodeIATA='VKO' )		then 0 --BB вылет прилет из базы в базу
					When (FR.CodeIATA='VKO' ) and (LR.CodeIATA<>'VKO')		then 1 --BN
					When (FR.CodeIATA<>'VKO') and (LR.CodeIATA<>'VKO')		then 2 --NN
					When (FR.CodeIATA<>'VKO') and (LR.CodeIATA='VKO' )		then 3 --NB
					End
			END ff,
			----------------------------------------------------------------------------------------------
			FR.DateTakeoff,
			FR.CodeIATA,
			FR.Ap_AirPortID,
			FR.Name,
			LR.DateLanding,
			LR.Ap_AirPortID,
			LR.CodeIATA,
			LR.Name,
			T.CrewType
			From @TBTSK T
			OUTER APPLY (select top 1
						 PFA.DateTakeoff,
						 FL.NAME,
						 PRT.Ap_AirPortID,
						 PRT.CodeIATA	
						 From Ap_PlanFlightAirPorts PFA
						 INNER JOIN Ap_PlanFlights PFL ON PFL.Ap_PlanFlightID=PFA.Ap_PlanFlightID
						 INNER JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
						 INNER JOIN Fl_Flights     FL  ON PFL.Fl_FlightID=FL.Fl_FlightID
						 INNER JOIN Ap_AirPorts    PRT  ON PRT.Ap_AirPortID=PFA.Ap_AirPortID
						 WHERE 
						 PFA.AirPortNumber=1
						 and PFC.Pr_PersonnelID=T.Ap_PlanFlightAirPortCrewID
						 and T.FlightTaskReestrID=PFA.Pr_FlightTaskReestrID
						 ORDER BY PFA.DateTakeoff ASC
						 ) FR
			OUTER APPLY (select top 1
						 PFA1.DateLanding,
						 FL.NAME,
						 PRT.Ap_AirPortID,
						 PRT.CodeIATA	
						 From Ap_PlanFlightAirPorts PFA
						 OUTER APPLY (Select top 1 P.* From Ap_PlanFlightAirPorts P Where P.Ap_PlanFlightID=PFA.Ap_PlanFlightID and P.DateLanding  is not null Order by P.AirPortNumber desc) PFA1
						 INNER JOIN Ap_PlanFlightAirPortCrews PFC ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
						 INNER JOIN Ap_PlanFlights PFL ON PFL.Ap_PlanFlightID=PFA.Ap_PlanFlightID
						 INNER JOIN Fl_Flights     FL  ON PFL.Fl_FlightID=FL.Fl_FlightID
						 INNER JOIN Ap_AirPorts    PRT  ON PRT.Ap_AirPortID=PFA1.Ap_AirPortID
						 WHERE 
						 PFA.AirPortNumber=1
						 and PFC.Pr_PersonnelID=T.Ap_PlanFlightAirPortCrewID
						 and T.FlightTaskReestrID=PFA.Pr_FlightTaskReestrID
						 ORDER BY PFA1.DateLanding DESC
						 ) LR
			Where 
			FlightTaskReestrID is not null
			---and (( TehNumber=1 and CrewType=@TYPECREW) or (CrewType=4))  
			ORDER BY 
			FR.DateTakeoff, 
			T.Ap_PlanFlightAirPortCrewID,
			FR.CodeIATA
END
--===========================Завершение данных для ТРИПОВ на основании ПЗ===================
--==========================================================================================
			---Select * From @TVLEG
--=============================================================================-------------

----===========================================================
----===========================================================
----================================Заголовок CTF===============
DECLARE 
@NN integer=0,
@STR NVARCHAR(max)='',
@STR1 NVARCHAR(max)=''
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @PERIOD+' '+Convert(nvarchar(10),dateadd(dd,-6,@DTbegin),112)+' - '+Convert(nvarchar(10),dateadd(dd,12,@DTEnd),112))
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @PLAN_TYPE)
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @TIME_MODE)
Set @NN=@NN+1
INSERT INTO @REZCRW 
VALUES (@NN, @SECTION_PAIRING)
----===========================================================
----===========================================================



--=======================================================================================
--======================Формирование ТРИПОВ из ПЗ с пилотами=============================
--======================================Данные ==========================================
--=============================== Начало Курсора 1=======================================
IF @Sourth=0
BEGIN
			DECLARE @BeginParing integer=0
			DECLARE 
			@FlightTaskReestrID integer,
			@PlanFlightAirPortCrewID integer,
			@AP_BASE_ID integer,
			@ff integer,
			@DateTakeoff Datetime,
			@F_CodeIATA nvarchar(5),
			@F_Ap_AirPortID integer,
			@F_Name nvarchar(5),
			@DateLanding Datetime,
			@L_Ap_AirPortID integer,
			@L_CodeIATA nvarchar(5),
			@L_Name nvarchar(5),
			@Beg nvarchar(3),
			@Bend nvarchar(3),
			@Define integer,
			-------------------
			@Nrow integer,
			@Nleg integer,
			@NrowBegin integer,
			@TO_DateTakeoff Datetime,
			@TO_CodeIATA nvarchar(5),
			@LegName nvarchar(5),
			@LA_DateLanding DateTime,
			@LA_CodeIATA  nvarchar(5),
			@CrewType integer,
			@Tabel nvarchar(30),
			@TehNumber integer, 
			@TYPE_CREW nvarchar(30),
			@StartFL integer=0,
			@TabelInt integer=0

			SET @CURFOOD  = CURSOR SCROLL
			FOR 
			---------
			Select
			TB.Beg,
			TB.Bend,
			TB.Define,
			TB.FlightTaskReestrID ,
			TB.Ap_PlanFlightAirPortCrewID ,
			TB.Tabel,
			TB.TehNumber,
			TB.AP_BASE_ID,
			TB.DateTakeoff ,
			TB.F_CodeIATA ,
			TB.F_Ap_AirPortID ,
			TB.F_Name ,
			TB.DateLanding ,
			TB.L_Ap_AirPortID ,
			TB.L_CodeIATA ,
			TB.L_Name, 
			TB.CrewType 					  
			From 	(Select 
					FlightTaskReestrID ,
					Ap_PlanFlightAirPortCrewID,
					Tabel,
					TehNumber,
					ROW_NUMBER() OVER(PARTITION BY Ap_PlanFlightAirPortCrewID Order by FlightTaskReestrID) Define,
					AP_BASE_ID ,
					isnull(H.HFF,-1) HFF,
					ff ,
					isnull(L.HLL,-1) HLL,
					Case
					When ff=0 then 'Beg'
					When ff=1 then 'Beg'
					When hff in (1,2) and ff=2  then ''
					When hff not in (1,2) and ff=2 then 'Beg'
					When hff not in (1,2) and ff=3 then 'Beg'
					end Beg,
					Case
					When ff=0 then 'End'
					When ff=1 and hll not in (2,3) then 'End'
					When ff=2 and hll in (2,3) then ''
					When ff=2 and hll not in (2,3) then 'End'
					When ff=3 then 'End'
					end Bend,
					DateTakeoff ,
					F_CodeIATA ,
					F_Ap_AirPortID ,
					F_Name ,
					DateLanding ,
					L_Ap_AirPortID ,
					L_CodeIATA ,
					L_Name,
					CrewType  
						From @TVLEG T
						OUTER APPLY (Select top 1 
										FF HFF 
										From @TVLEG F 
										Where 
										F.DateTakeoff<T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										Order by F.DateTakeoff Desc 
									) H
						OUTER APPLY (Select top 1 
										FF HLL 
										From @TVLEG F 
										Where 
										F.DateTakeoff>T.DateTakeoff 
										and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
										Order by F.DateTakeoff ASC 
									) L
						WHere
						((
							(@Parts=0 or @Parts=1)
							and 
							-----------------CaringIN-----------------------------
							Case
							When ff=0 or ff=1 then T.DateTakeoff
							When Ff=2 or ff=3 then (Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff<T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=1 
													Order by F.DateTakeoff Desc 
													) 
							----------------------------------------------
							End between @DT_CRbegin and @DTbegin
							----------------------------------------------
							and 
							Case
							When ff=0 or ff=3 then dateadd(hh,24,T.DateLanding)
							When Ff=2 or ff=1 then dateadd(hh,24,(Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff>T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=3 
													Order by F.DateTakeoff ASC 
													) )
							End  >= @DTbegin
							and t.FlightTaskReestrID is not null
						)
							or
							-----------------------Plan Period--------------
						(
							(@Parts=0)
							and 
							Case
							When ff=0 or ff=1 then T.DateTakeoff
							When Ff=2 or ff=3 then (Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff<T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=1 
													Order by F.DateTakeoff Desc 
													) 
							End between @DTbegin and @DTEnd
							and 
							Case
							When ff=0 or ff=3 then T.DateLanding
							When Ff=2 or ff=1 then (Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff>T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=3 
													Order by F.DateTakeoff ASC 
													) 
							End  between @DTbegin and @DTEnd
							and t.FlightTaskReestrID is not null
							)
								or
							----------------------------------------CaringOUT-----------------------
							(
							(@Parts=0 or @Parts=2)
							and 
							Case
							When ff=0 or ff=1 then T.DateTakeoff
							When Ff=2 or ff=3 then (Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff<T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=1 
													Order by F.DateTakeoff Desc 
													) 
							End between @DTbegin and @DTEnd
							and 
							Case
							When ff=0 or ff=3 then T.DateLanding
							When Ff=2 or ff=1 then (Select top 1 
													F.DateTakeoff 
													From @TVLEG F 
													Where 
													F.DateTakeoff>T.DateTakeoff 
													and F.Ap_PlanFlightAirPortCrewID=T.Ap_PlanFlightAirPortCrewID 
													and F.ff=3 
													Order by F.DateTakeoff ASC 
													)
							End  between @DTEnd and @DT_CREnd
							and t.FlightTaskReestrID is not null
							)
							) --and (( TehNumber=1 and CrewType=@TYPECREW) or (CrewType=4))  ---
						) TB
						ORDER BY							
						TB.Ap_PlanFlightAirPortCrewID,
						TB.DateTakeoff	asc	
			-------------------------------------
			SET @BeginParing=1
			SET @NrowBegin=0
			OPEN @CURFOOD
			FETCH NEXT FROM @CURFOOD INTO 
			@Beg ,
			@Bend ,
			@Define ,
			@FlightTaskReestrID,
			@PlanFlightAirPortCrewID,
			@Tabel,
			@TehNumber, 
			@AP_BASE_ID ,
			@DateTakeoff ,
			@F_CodeIATA ,
			@F_Ap_AirPortID ,
			@F_Name ,
			@DateLanding ,
			@L_Ap_AirPortID ,
			@L_CodeIATA ,
			@L_Name,
			@CrewType
			WHILE @@FETCH_STATUS = 0
			  BEGIN
				SET @STR=''
				IF @Beg='Beg' 
				BEGIN
				IF @StartFL=1
					Begin
					BEGIN TRANSACTION
						SET @NN=@NN+1
						INSERT INTO @REZCRW
						select @NN,@EOPAIRING 
					COMMIT TRANSACTION
				end
				IF isnumeric(@Tabel)=1 Set @TabelInt=convert(integer,@Tabel)
				ELSE Set @TabelInt=1111
				Set @TYPE_CREW=Case
						When @TYPECREW=0 and @TehNumber=1 then '1/0/0/0/0/0/0 '
						When @TYPECREW=0 and @TehNumber=2 then '0/1/0/0/0/0/0 '
						When @TYPECREW=0 and @TehNumber=3 then '0/0/1/0/0/0/0 '
						---------------------------------------------------------
						When @TYPECREW=1 and @TehNumber=1 then '0/0/0/1/0/0/0 '
						When @TYPECREW=1 and @TehNumber=2 then '0/0/0/0/0/1/0 '
						When @TYPECREW=1 and @TehNumber=3 then '0/0/0/0/0/1/0 '
						When @TYPECREW=1 and @TehNumber=4 then '0/0/0/0/0/1/0 '
						When @TYPECREW=1 and @TehNumber=5 then '0/0/0/0/0/0/1 '
						When @TYPECREW=1 and @TehNumber=6 then '0/0/0/0/0/0/1 '
						When @TYPECREW=1 and @TehNumber=7 then '0/0/0/0/0/0/1 '
						else 
							CASE 
								When @TYPECREW=0 and @CrewType=4 then '0/0/1/0/0/0/0 '
								When @TYPECREW=1 and @CrewType=4 then '0/0/0/0/0/0/1 '
							else ''
							END
					   End
					SET @BeginParing=0
					SET @Nleg=0
					--=============================================
					
					--=============================================
					------SET @Nleg=(Select count(*) From @TVLEGS T
					------										INNER Join Ap_PlanFlightAirPortCrews CR ON CR.Ap_PlanFlightAirPortID=T.Ap_PlanFlightAirPortID 
					------										Where 
					------										T.Pr_FlightTaskReestrID=@FlightTaskReestrID
					------										and CR.Pr_PersonnelID=@PlanFlightAirPortCrewID)
					SET @NN=@NN+1
					--SET @STR=@STR+@PAIRING +' ' + convert(nvarchar(5),@Nleg) + ' ' + CONVERT(nvarchar(25),@FlightTaskReestrID)+' "'+CONVERT(nvarchar(25),@FlightTaskReestrID)+'" '+@CREWSTR+LTRIM(RTRIM(@F_CodeIATA))

					-------==================================================================
						IF (isnull(@PAIRING+CONVERT(nvarchar(25),@FlightTaskReestrID)+convert(nvarchar(2),@TehNumber)+' "'+CONVERT(nvarchar(25),@FlightTaskReestrID)+convert(nvarchar(2),@TehNumber)+'" '+@TYPE_CREW+LTRIM(RTRIM(@F_CodeIATA)) + ' #' + @Tabel +' ' + convert(nvarchar(2),@TehNumber),'')<>'')
						BEGIN
						-----------------------------------------------------------
							SET @STR=isnull(@PAIRING +'  1'+CONVERT(nvarchar(25),@FlightTaskReestrID)+convert(nvarchar(2),@CrewType)+convert(nvarchar(2),@TehNumber)+convert(nvarchar(10),@TabelInt)+
							' "FD'+CONVERT(nvarchar(25),@FlightTaskReestrID)+
							convert(nvarchar(2),@CrewType)+
							convert(nvarchar(2),@TehNumber)+convert(nvarchar(10),@TabelInt)+'" '+
							@TYPE_CREW+LTRIM(RTRIM(@F_CodeIATA)) + 
							' #' + @Tabel +' ' + 
							convert(nvarchar(2),@TehNumber),'error-'+CONVERT(nvarchar(25),@FlightTaskReestrID))
						------------------------------------------------------------
						SET @NrowBegin=@NN
						END
					------=============================================================================
					---SET @NrowBegin=@NN
					INSERT INTO @REZCRW
					select @NN,@STR 
				END
					----------------------------------------------------
					-----------------------------------------------------
					SET @CURLEG  = CURSOR SCROLL
						FOR 			
						------Select 
						------DateTakeoff,
						------AP_TO,
						------FlightName,
						------DateLanding,
						------AP_LA
						------From @TVLEGS
						------Where Pr_FlightTaskReestrID=@FlightTaskReestrID
						------Order by DateTakeoff
						Select 
						T.DateTakeoff,
						T.AP_TO,
						T.FlightName,
						T.DateLanding,
						T.AP_LA
						From @TVLEGS T
						INNER Join Ap_PlanFlightAirPortCrews CR ON CR.Ap_PlanFlightAirPortID=T.Ap_PlanFlightAirPortID 
						Where 
						T.Pr_FlightTaskReestrID=@FlightTaskReestrID
						and CR.Pr_PersonnelID=@PlanFlightAirPortCrewID
						Order by DateTakeoff

						OPEN @CURLEG
						FETCH NEXT FROM @CURLEG INTO
							@TO_DateTakeoff ,
							@TO_CodeIATA ,
							@LegName ,
							@LA_DateLanding,
							@LA_CodeIATA 
						WHILE @@FETCH_STATUS = 0
						BEGIN
						If  @BeginParing=0
						BEGIN
							------SET @STR1=''
							------SET @Nleg=@Nleg+1
							------SET @NN=@NN+1
							------SET @STR1=@Activity_type+@DS+@ActivitySub_type+@DS+
							------			Case
							------			when @Nleg=1 then @Horizontal_lock1
							------			when @DateLanding =@LA_DateLanding and  @Bend='End'  then @Horizontal_lock3
							------			else @Horizontal_lock2
							------			end +@DS+
							------			Convert(nvarchar(10),@TO_DateTakeoff,112)+@DS+@TO_CodeIATA +@DS+dbo.PD_fn_ShowDateAsHHMM(@TO_DateTakeoff )+@DS+
							------			@CorrierCode+@DS+convert(nvarchar(6),@LegName) +@DS+'*'+@DS+'1'+@DS+dbo.PD_fn_ShowDateAsHHMM(@LA_DateLanding)+@DS+@LA_CodeIATA+@DS+Convert(nvarchar(10),@LA_DateLanding,112)
							------			+ ' #duty'+ convert(nvarchar(6),@Define)
							SET @STR1=''
							SET @Nleg=@Nleg+1
							SET @NN=@NN+1
							SET @STR1=@Activity_type+@DS+
							Case
							when (@CrewType < 4) Then Case
														When @TO_DateTakeoff between @DT_CRbegin and @DTbegin and (@LA_DateLanding between @DT_CRbegin and @DTbegin or @LA_DateLanding >=@DTbegin) Then @ActivitySub_type
														else 'L'--'*'
													  End
							when (@CrewType = 4) Then @ActivitySub_typeDH
							end +@DS+
							--------------------------------------------------------------------------
							Case
							when @Nleg=1 then @Horizontal_lock1
							when @DateLanding =@LA_DateLanding and  @Bend='End'  then @Horizontal_lock3
							else @Horizontal_lock2
							end +@DS+
							------------------------------------------------------------------------------
							Convert(nvarchar(10),@TO_DateTakeoff,112)+@DS+@TO_CodeIATA +@DS+dbo.PD_fn_ShowDateAsHHMM(@TO_DateTakeoff )+@DS+
							@CorrierCode+@DS+convert(nvarchar(6),@LegName) +@DS+'*'+@DS+'1'+@DS+dbo.PD_fn_ShowDateAsHHMM(@LA_DateLanding)+@DS+
							@LA_CodeIATA+@DS+Convert(nvarchar(10),@LA_DateLanding,112)
							+ ' #duty'+ convert(nvarchar(6),@Define)+' '+convert(nvarchar(2),@CrewType) 
								BEGIN TRANSACTION
									INSERT INTO @REZCRW
									select @NN,@STR1 
								COMMIT TRANSACTION
								--------------------------
								BEGIN TRANSACTION
									UPDATE @TVLEGS 
									SET FL=1
									Where LTRIM(RTRIM(FlightName))=LTRIM(RTRIM(@LegName)) and convert(datetime, DateTakeoff)=convert(datetime,@TO_DateTakeoff)
									--Select * From @TVLEGS Where LTRIM(RTRIM(FlightName))='441' --and DateTakeoff=@TO_DateTakeoff
								COMMIT TRANSACTION
						END
						FETCH NEXT FROM @CURLEG INTO 
							@TO_DateTakeoff ,
							@TO_CodeIATA ,
							@LegName ,
							@LA_DateLanding,
							@LA_CodeIATA 
						END
					CLOSE @CURLEG
					DEALLOCATE @CURLEG 
				-----------------------------------------------------
				BEGIN TRANSACTION
					UPDATE @REZCRW 
					SET STR1=@PAIRING + ' ' +  convert(nvarchar(5),@Nleg) + ' ' + SUBSTRING(TT.STR1,charindex(' ',TT.STR1,charindex(' ',TT.STR1,1)+1)+1,LEN(TT.STR1))
					From @REZCRW TT where TT.NN=@NrowBegin 
				COMMIT TRANSACTION
				------------------------------------------------------
				---------------------------------------
				IF  @BeginParing=0 and  @Bend='End' 
				Begin
				---------------------------------------
					----BEGIN TRANSACTION
					----	UPDATE @REZCRW 
					----	SET STR1=SUBSTRING(TT.STR1,charindex(@PAIRING,TT.STR1,1),LEN(@PAIRING)) + ' ' +  convert(nvarchar(5),@Nleg) + ' ' + SUBSTRING(TT.STR1,LEN(@PAIRING)+1,LEN(TT.STR1)-LEN(@PAIRING)+1)
					----	From @REZCRW TT where TT.NN=@NrowBegin 
					----COMMIT TRANSACTION
					---------------------------------------
					SET @BeginParing=1
					SET @NrowBegin=0
					--IF @StartFL=0
					--Begin
					------BEGIN TRANSACTION
					------	SET @NN=@NN+1
					------	INSERT INTO @REZCRW
					------	select @NN,@EOPAIRING 
					------COMMIT TRANSACTION
					--end
					SET @StartFL=1
				End
	
			  FETCH NEXT FROM @CURFOOD INTO 
				@Beg ,
				@Bend ,
				@Define ,
				@FlightTaskReestrID,
				@PlanFlightAirPortCrewID,
				@Tabel,
				@TehNumber, 
				@AP_BASE_ID ,
				@DateTakeoff ,
				@F_CodeIATA ,
				@F_Ap_AirPortID ,
				@F_Name ,
				@DateLanding ,
				@L_Ap_AirPortID ,
				@L_CodeIATA ,
				@L_Name,
				@CrewType
			  END
			CLOSE @CURFOOD
			DEALLOCATE @CURFOOD 
END
--===========================================================Конец формирования ТРИПОВ на основании ПЗ============================================================
--================================================================================================================================================================
--
--
--=======================================Начало формирования  ТРИПОВ на основании JP================================
--===================================================================================================================
--===================================================================================================================
IF @Sourth=1
BEGIN
DECLARE 
@JP_STR nvarchar(max),
@JP_Duty integer,
@JP_DT integer

SET @CURFOOD  = CURSOR SCROLL
FOR Select * From @JPTPAR T
OPEN @CURFOOD
FETCH NEXT FROM @CURFOOD INTO 
		@JP_ChainPlnID,
		@JP_DateBegin,
		@JP_DateEnd,
		@JP_Pairing_ID,
	    @JP_CodeIATA 
	WHILE @@FETCH_STATUS = 0
	BEGIN
		SET @STR=''
		SET @JP_CNT=0
		SET @NN=@NN+1
		SET @JP_CNT = (select Count(CHR.Ak_ChainPlnID) From Ak_ChainPlnFlightRoutes CHR Where CHR.Ak_ChainPlnID=@JP_ChainPlnID)
		BEGIN TRANSACTION
		SET @STR=@STR+@PAIRING+' '+convert(nvarchar(3),@JP_CNT)+' '+@JP_Pairing_ID+' "'+@JP_Pairing_ID+'" '+@CREWSTR+LTRIM(RTRIM(@JP_CodeIATA))
		INSERT INTO @REZCRW
		select @NN,@STR 
		COMMIT TRANSACTION
		-------------------------------------------------------------
		SET @CURLEG  = CURSOR SCROLL
		FOR 
				Select
				Case 
				When CHR.Passenger=0 then @Activity_type+@DS+@ActivitySub_type+@DS 
				When CHR.Passenger=1 then @DH+@DS+@ActivitySub_type+@DS 
				end +'*'+
				--Case
				--when CHR.ChainPlnFlightRouteNumber=1 then @Horizontal_lock1
				--when CHR.ChainPlnFlightRouteNumber=@JP_CNT then @Horizontal_lock3
				--else @Horizontal_lock2
				--end + 
				@DS+ Convert(nvarchar(10),dateadd(ss,chr.TimeTakeOff,dateadd(dd,chr.DayOffset,@JP_DateBegin)),112) +
				@DS+(select ap.CodeIATA from Ap_AirPorts ap where ap.Ap_AirPortID=chr.Ap_AirPortIDTakeOff) +
				@DS+dbo.PD_fn_ShowDateAsHHMM(dateadd(ss,chr.TimeTakeOff,dateadd(dd,chr.DayOffset,@JP_DateBegin)))+
				@DS+@CorrierCode+@DS+(select f.Name From Fl_Flights F where f.Fl_FlightID=CHR.Fl_FlightID)+@DS+'*'+@DS+'1' +
				@DS+dbo.PD_fn_ShowDateAsHHMM(dateadd(ss,chr.TimeLanding,dateadd(dd,chr.DayOffset,@JP_DateBegin)))+
				@DS+(select ap.CodeIATA from Ap_AirPorts ap where ap.Ap_AirPortID=chr.Ap_AirPortIDLanding)+
				@DS+Convert(nvarchar(10),dateadd(ss,chr.TimeLanding,dateadd(dd,chr.DayOffset,@JP_DateBegin)),112)+
				' #duty',
				chr.WorkTimeBegin  
				From Ak_ChainPlnFlightRoutes CHR
				Where CHR.Ak_ChainPlnID=@JP_ChainPlnID
				ORDER BY CHR.DayOffset asc, CHR.TimeLanding asc
		OPEN @CURLEG
		FETCH NEXT FROM @CURLEG INTO
			@JP_STR ,
			@JP_Duty 
		SET @JP_DT=0
		WHILE @@FETCH_STATUS = 0
		BEGIN
			SET @JP_DT=@JP_DT+isnull(@JP_Duty,0)
		BEGIN TRANSACTION
		SET @STR=@JP_STR+convert(nvarchar(5),@JP_DT)
		INSERT INTO @REZCRW
		select @NN,@STR 
		COMMIT TRANSACTION
		FETCH NEXT FROM @CURLEG INTO 
			@JP_STR ,
			@JP_Duty 
		END
		CLOSE @CURLEG
		DEALLOCATE @CURLEG 
		--------------------------------------------------------------
		BEGIN TRANSACTION
		SET @NN=@NN+1
		INSERT INTO @REZCRW
		select @NN,@EOPAIRING 
		COMMIT TRANSACTION
	FETCH NEXT FROM @CURFOOD INTO 
		@JP_ChainPlnID,
		@JP_DateBegin,
		@JP_DateEnd,
		@JP_Pairing_ID,
		@JP_CodeIATA 
	END
END
--=======================================Завершение формирования ТРИПОВ на основании JP================================
--===================================================================================================================
--===================================================================================================================
BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@EOPAIRING 
COMMIT TRANSACTION
BEGIN TRANSACTION
	SET @NN=@NN+1
	INSERT INTO @REZCRW
	select @NN,@EOSECTION 
COMMIT TRANSACTION
--========================Секция ЛЕГОВ=================
--=====================================================
DEclare @NE integer=0
SET @NN=@NN+100000
BEGIN TRANSACTION
INSERT INTO @REZCRW
EXEC PD_JPR_ImportCTF_Legs @DTbegin, @DTEnd, @NN, @NE
SET @NN=@NE+1
----INSERT INTO @REZCRW
----Select @NN,'EOSECTION'
COMMIT TRANSACTION
--=====================================================
--=====================================================
--------------------------------------------------
Select T.STR1 from @REZCRW T
Order by T.NN
---------------------------------------------------
Select * from @TVLEGS 
where
FL=0 
and FlightName not in ('001','002')
and
(
   ((DateTakeoff between @DT_CRbegin and @DTbegin)  and (@Parts=1))
or ((DateTakeoff between @DTbegin and @DTEnd) and (@Parts=0)) 
or ((DateTakeoff between @DTEnd and @DT_CREnd) and (@Parts=2))   
)
END