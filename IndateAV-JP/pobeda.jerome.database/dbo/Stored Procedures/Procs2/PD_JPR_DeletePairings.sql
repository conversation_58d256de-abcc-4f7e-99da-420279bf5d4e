﻿CREATE PROCEDURE [dbo].[PD_JPR_DeletePairings]
@DTBegin date=null,
@DTEnd   date=null,
@NameComment nvarchar(100)=null,
@RZD integer=null
--======================================================== 
AS
BEGIN
	Delete from Ak_ChainPlnFlightRoutes
	Where
	Ak_ChainPlnID in (select CR.Ak_ChainPlnID From Ak_ChainPlnFlightRoutes CR 
						Inner Join Ak_ChainPlns  CH on CH.Ak_ChainPlnID=CR.Ak_ChainPlnID
						Where 
						CH.MaskType=@RZD
						and
						(
						((@DTBegin is null and @DTEnd is null and @NameComment is null) and charindex('JP--',CH.Comment)>0)
						OR --------------------------------------------------
						((CH.DateBegin between @DTBegin and @DTEnd) and (@DTBegin is not null and @DTEnd is not null and @NameComment is null))
						OR -------------------------------------------------
						((charindex(@NameComment,CH.Comment)>0) and (@DTBegin is null and @DTEnd is null and @NameComment is not null))
						)  
					  )--Long-Pairingv

	--=========================================
	Delete from Ak_ChainPlns 
	Where
		MaskType=@RZD
		and
		(
		((@DTBegin is null and @DTEnd is null and @NameComment is null) and charindex('JP--',Comment)>0)
		OR --------------------------------------------------
		((DateBegin between @DTBegin and @DTEnd) and (@DTBegin is not null and @DTEnd is not null and @NameComment is null) )
		OR -------------------------------------------------
		((charindex(@NameComment,Comment)>0) and (@DTBegin is null and @DTEnd is null and @NameComment is not null))
		)   
	--=========================================
END
