﻿-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE FUNCTION [dbo].[PD_JPR_fn_GET_TripPersonAviabit]
(	
@Pr_PersonnelID integer=null
)
RETURNS TABLE 
AS
RETURN 
(
SELECT 
	TRP.Pr_PersonnelID Pr_PersonnelID,
	TRP.Trip		   Trip,
	MIN(TRP.HBD_AP)        HBD_AP,
	MIN(FS_AP.APTO)	   APTO,
	MIN(SC_AP.APLA)	   APLA,
	MAX(TRP.Tabel)		   Tabel,
	COUNT(TRP.Fl_FlightID) Nflight,
	MIN(TRP.DateTakeoff) DateTakeoff,
	MAX(TRP.DateLanding) DateLanding,
	MIN(TRP.Pr_CategoryTypeID) Pr_CategoryTypeID,
	MIN(TRP.TehNumber)	   TehNumber,
	Case
	When MIN(TEH.Pr_CategoryTypeID)=1 and MIn(TEH.CrewType)=0 and MIN(TEH.TehNumber)=1 then '1/0/0/0/0/0/0 '
	When MIN(TEH.Pr_CategoryTypeID)=1 and MIn(TEH.CrewType)=0 and MIN(TEH.TehNumber)=2 then '0/1/0/0/0/0/0 '
	When MIN(TEH.Pr_CategoryTypeID)=1 and MIn(TEH.CrewType)=0 and MIN(TEH.TehNumber)=3 then '0/0/1/0/0/0/0 '
	When MIN(TEH.Pr_CategoryTypeID)=1 and MIn(TEH.CrewType)=0 and MIN(TEH.TehNumber)>3 then '0/0/1/0/0/0/0 '
	---------------------------------------------------------
	When MIN(TEH.Pr_CategoryTypeID)=5 and MIn(TEH.CrewType)=1 and MIN(TEH.TehNumber)=1 then '0/0/0/1/0/0/0 '
	When MIN(TEH.Pr_CategoryTypeID)=5 and MIn(TEH.CrewType)=1 and MIN(TEH.TehNumber)=2 then '0/0/0/0/0/1/0 '
	When MIN(TEH.Pr_CategoryTypeID)=5 and MIn(TEH.CrewType)=1 and MIN(TEH.TehNumber)=3 then '0/0/0/0/0/1/0 '
	When MIN(TEH.Pr_CategoryTypeID)=5 and MIn(TEH.CrewType)=1 and MIN(TEH.TehNumber)=4 then '0/0/0/0/0/1/0 '
	When MIN(TEH.Pr_CategoryTypeID)=5 and MIn(TEH.CrewType)=1 and MIN(TEH.TehNumber)=5 then '0/0/0/0/0/0/1 '
	When MIN(TEH.Pr_CategoryTypeID)=5 and MIn(TEH.CrewType)=1 and MIN(TEH.TehNumber)=6 then '0/0/0/0/0/0/1 '
	When MIN(TEH.Pr_CategoryTypeID)=5 and MIn(TEH.CrewType)=1 and MIN(TEH.TehNumber)=7 then '0/0/0/0/0/0/1 '
	When MIN(TEH.Pr_CategoryTypeID)=5 and MIn(TEH.CrewType)=1 and MIN(TEH.TehNumber)>7 then '0/0/0/0/0/0/1 '
	else 
		CASE 
			When MIN(DH.Pr_CategoryTypeID)=1 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=1 then '1/0/0/0/0/0/0 '
			When MIN(DH.Pr_CategoryTypeID)=1 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=2 then '0/1/0/0/0/0/0 '
			When MIN(DH.Pr_CategoryTypeID)=1 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=3 then '0/0/1/0/0/0/0 '
			When MIN(DH.Pr_CategoryTypeID)=1 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)>3 then '0/0/1/0/0/0/0 '
			---------------------------------------------------------
			When MIN(DH.Pr_CategoryTypeID)=5 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=1 then '0/0/0/1/0/0/0 '
			When MIN(DH.Pr_CategoryTypeID)=5 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=2 then '0/0/0/0/0/1/0 '
			When MIN(DH.Pr_CategoryTypeID)=5 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=3 then '0/0/0/0/0/1/0 '
			When MIN(DH.Pr_CategoryTypeID)=5 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=4 then '0/0/0/0/0/1/0 '
			When MIN(DH.Pr_CategoryTypeID)=5 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=5 then '0/0/0/0/0/0/1 '
			When MIN(DH.Pr_CategoryTypeID)=5 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=6 then '0/0/0/0/0/0/1 '
			When MIN(DH.Pr_CategoryTypeID)=5 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)=7 then '0/0/0/0/0/0/1 '
			When MIN(DH.Pr_CategoryTypeID)=5 and MIn(DH.CrewType)=4 and MIN(DH.TehNumber)>7 then '0/0/0/0/0/0/1 '
		else ''
		end
	END Posion,
	---MIN(TSK.Comment)
	'' PPLS

FROM PD_JPR_TripFlightPerson TRP
---------------------------Подключаем PPLS-----------------------------------------------------
----INNER JOIN Ap_PlanFlightAirPorts PTO On PTO.Ap_PlanFlightID=TRP.Ap_PlanFlightID and PTO.AirPortNumber=1
----LEFT JOIN  Ap_PlanFlightAirPortCrews CRW on CRW.Ap_PlanFlightAirPortID=PTO.Ap_PlanFlightAirPortID and CRW.Pr_PersonnelID=TRP.Pr_PersonnelID
----LEFT JOIN  Ak_PPLSTaskTypes TSK On TSK.Ak_PPLSTaskTypeID=CRW.Ak_PPLSTaskTypeID
------------------------------------------------------------------------------------------------
OUTER APPLY (select top 1 * From PD_JPR_TripFlightPerson TRP1 
					  where 
						TRP.Pr_PersonnelID=TRP1.Pr_PersonnelID 
						and TRP.Trip=TRP1.Trip 
					  Order by TRP1.DateTakeoff ASC
		    ) FS_AP
OUTER APPLY (select top 1 * From PD_JPR_TripFlightPerson TRP1 
					  where 
						TRP.Pr_PersonnelID=TRP1.Pr_PersonnelID 
						and TRP.Trip=TRP1.Trip
						and TRP1.CrewType <> 4
					  Order by TRP1.DateTakeoff ASC
		    ) TEH
OUTER APPLY (select top 1 * From PD_JPR_TripFlightPerson TRP1 
					  where 
						TRP.Pr_PersonnelID=TRP1.Pr_PersonnelID 
						and TRP.Trip=TRP1.Trip
						and TRP1.CrewType =4
					  Order by TRP1.DateTakeoff ASC
			) DH
OUTER APPLY (select top 1 * From PD_JPR_TripFlightPerson TRP1 
					  where 
						TRP.Pr_PersonnelID=TRP1.Pr_PersonnelID 
						and TRP.Trip=TRP1.Trip 
					  Order by TRP1.DateLanding DESC
		    ) SC_AP
Where 
TRP.Trip is not null
and ((TRP.Pr_PersonnelID=@Pr_PersonnelID and @Pr_PersonnelID is not null ) or (@Pr_PersonnelID is  null))
GROUP BY 
TRP.Pr_PersonnelID,
TRP.Trip
----Order by
----TRP.Pr_PersonnelID,
----MIN(TRP.DateTakeoff) 
)