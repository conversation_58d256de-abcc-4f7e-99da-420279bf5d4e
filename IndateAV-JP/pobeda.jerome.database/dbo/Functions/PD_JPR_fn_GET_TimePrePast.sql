﻿
CREATE FUNCTION [dbo].[PD_JPR_fn_GET_TimePrePast] 
(
@DateTakeoff datetime,
@DateLanding datetime,
@Pr_PersonnelID integer
)
RETURNS  @Table TABLE
(	Pred integer,
	Past integer,
	HomeBase integer)
AS 
Begin
----Declare
----@DateTakeoff datetime='2018-08-14 19:35:00.000',
----@DateLanding datetime='2018-08-15 00:05:00.000',
----@Pr_PersonnelID integer=1582

DEclare 
@Pred integer,
@Past integer,
@HomeBase integer
--=============================================Pre============================
		select top 1 
		@Pred=datediff(hh,PFLA1.DateLanding ,@DateTakeoff)		
		FROM Ap_PlanFlightAirPorts			PFA1 with (nolock)
		LEFT JOIN Ap_PlanFlights			PFL1 with (nolock) ON PFA1.Ap_PlanFlightID = PFL1.Ap_PlanFlightID 
		LEFT JOIN Ap_PlanFlightAirPortCrews PFC1 with (nolock) ON PFC1.Ap_PlanFlightAirPortID = PFA1.Ap_PlanFlightAirPortID
		OUTER APPLY (select top 1 PFT1.DateLanding From Ap_PlanFlightAirPorts  PFT1 with (nolock)
									where 
									PFT1.Ap_PlanFlightID=PFL1.Ap_PlanFlightID 
									order by 
									PFT1.DateLanding  DESC --AirPortNumber
					) PFLA1
		where 
			PFC1.Pr_PersonnelID=@Pr_PersonnelID
			--and PFA1.AirPortNumber=1
			and PFA1.DateTakeoff between dateadd(hh,-1600,@DateTakeoff) and  dateadd(hh,-2,@DateTakeoff)
			--and PFA1.DateTakeoff < @DateTakeoff
			AND PFL1.OnlyFinanceCalculation = 0
			AND PFL1.Sh_ScheduleVariantTypeID IS NULL
			and PFL1.Status & 256 <> 256						 ----отмененные рейсы
		Order by PFLA1.DateLanding  DESC
--===========================================Past=======================
		select top 1 
		@Past=datediff(hh,@DateLanding,PFA1.DateTakeoff) 
		FROM Ap_PlanFlightAirPorts			PFA1 with (nolock)
		LEFT JOIN Ap_PlanFlights			PFL1 with (nolock) ON PFA1.Ap_PlanFlightID = PFL1.Ap_PlanFlightID 
		LEFT JOIN Ap_PlanFlightAirPortCrews PFC1 with (nolock) ON PFC1.Ap_PlanFlightAirPortID = PFA1.Ap_PlanFlightAirPortID
		where 
			PFC1.Pr_PersonnelID=@Pr_PersonnelID
			--and PFA1.AirPortNumber=1 
			and PFA1.DateTakeoff > @DateLanding
			AND PFL1.OnlyFinanceCalculation = 0
			AND PFL1.Sh_ScheduleVariantTypeID IS NULL
			and PFL1.Status & 256 <> 256						 ----отмененные рейсы
		Order by PFA1.DateTakeoff  ASC
--============================================HomeBase======================
		Select top 1 
		@HomeBase=PTR1.Ap_AirPortID 
		From Pr_Personnels PR1 with (nolock)
		LEFT JOIN Pr_PersPosts      Post1 with (nolock) ON PR1.Pr_PersonnelID = Post1.Pr_PersonnelID 
		LEFT JOIN Pr_StaffTrees     TR1   with (nolock) ON Post1.Pr_StaffTreeID = TR1.Pr_StaffTreeID  
		LEFT  JOIN Pr_StaffTrees    PTR1  with (nolock) ON PTR1.Pr_StaffTreeID = TR1.Pr_StaffTreeIDParent 
		where 
			PR1.Pr_PersonnelID=@Pr_PersonnelID  
			AND (@DateTakeoff >= Post1.DateBegin and (@DateTakeoff <=Post1.DateEnd or Post1.DateEnd is null)) 
		Order by Post1.DateBegin   ASC
--=================================================================
	INSERT INTO @Table 
	Select
	@Pred Pred,
	@Past Past,
	@HomeBase HomeBase
	RETURN	
End								