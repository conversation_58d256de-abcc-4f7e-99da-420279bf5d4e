﻿
CREATE FUNCTION [dbo].[PD_JPR_fn_GET_TripAviabit] 
(	
	@DTbegin datetime,  
	@DTEnd datetime,
	@TYPECREW integer   --0 пилоты 1 --БП --11 все
)
RETURNS  @Table TABLE
(	
Pr_PersonnelID integer,	
DateTakeoff datetime,
DateLanding datetime,
FL_Name nvarchar(10),
HBD_AP integer,
TRbegHB integer,
TRbegVKO integer,
Trip integer,
HB_Trip integer,
NDuty integer,
APTO integer,
APLA integer,
Ap_PlanFlightID integer,
Pr_FlightTaskReestrID integer,
Pred integer, 
Past integer,
Tabel nvarchar(10),
TehNumber integer, 	
CrewType integer,
Pr_CategoryTypeID integer,
Fl_FlightID integer  
)
AS 
Begin
----DEclare
----@DTbegin datetime='2018-08-01 00:00:00',  
----@DTEnd datetime='2018-10-31 23:59:00',
----@TYPECREW integer = 11   --0 пилоты 1 --БП --11 все

Declare
@DT_CRbegin datetime=dateadd(dd,-30, @DTbegin),  
@DT_CREnd datetime=dateadd(dd,+30, @DTEnd)

--=============================================================
DEclare @FLT_TRP Table
(
Pr_PersonnelID  integer,	
Ap_PlanFlightID   integer,
DateTakeoff Datetime UNIQUE (Pr_PersonnelID,DateTakeoff),
DateLanding Datetime UNIQUE (Pr_PersonnelID,DateLanding),
FL_Name  nvarchar(10),
HBD_AP   integer,
APTO   integer,
APLA   integer,
Pr_FlightTaskReestrID   integer,
--Pred   integer, 
--Past   integer,
Tabel nvarchar(10),
TehNumber   integer, 	
CrewType   integer,
Pr_CategoryTypeID   integer,
Fl_FlightID integer,
UNIQUE (Pr_PersonnelID,Ap_PlanFlightID)
)
--========================================================
DEclare @FLT_TRP1 Table
(
Pr_PersonnelID  integer,	
Ap_PlanFlightID   integer,
DateTakeoff Datetime UNIQUE (Pr_PersonnelID,DateTakeoff),
DateLanding Datetime UNIQUE (Pr_PersonnelID,DateLanding),
FL_Name  nvarchar(10),
HBD_AP   integer,
APTO   integer,
APLA   integer,
Pr_FlightTaskReestrID   integer,
Pred   integer, 
Past   integer,
Tabel nvarchar(10),
TehNumber   integer, 	
CrewType   integer,
Pr_CategoryTypeID   integer,
Fl_FlightID integer,
UNIQUE (Pr_PersonnelID,Ap_PlanFlightID)
)
--===================================================================================
INSERT INTO @FLT_TRP
Select	DISTINCT	
		isnull(PR.Pr_PersonnelID,1) Pr_PersonnelID,	
		PFL.Ap_PlanFlightID,
		PFA.DateTakeoff,
		PFLA.DateLanding,
		FL.Name,
		HB.Ap_AirPortID  HBD_AP,
		PFA.Ap_AirPortID APTO,
		PFLA.Ap_AirPortID APLA,
		PFA.Pr_FlightTaskReestrID,
		--TM.Pred,
		--TM.Past,
		isnull(replace(Pr.TableNumber,'/','00'),'1') Tabel,
		isnull(PFC.OrderNumber,0) TehNumber, 	
		isnull(PFC.CrewType,11) CrewType,
		PR.Pr_CategoryTypeID,
		FL.Fl_FlightID

	FROM Ap_PlanFlights  PFL with (nolock)
		INNER JOIN Ap_PlanFlightAirPorts    PFA with (nolock) ON PFA.Ap_PlanFlightID = PFL.Ap_PlanFlightID and PFA.AirPortNumber=1
		OUTER APPLY (select top 1 * From Ap_PlanFlightAirPorts  PFT with (nolock)  where PFT.Ap_PlanFlightID=PFL.Ap_PlanFlightID order by PFT.AirPortNumber DESC) PFLA
		INNER JOIN Fl_Flights			    FL with (nolock) ON PFL.Fl_FlightID=FL.Fl_FlightID
		LEFT JOIN Ap_PlanFlightAirPortCrews PFC with (nolock) ON PFC.Ap_PlanFlightAirPortID = PFA.Ap_PlanFlightAirPortID
		LEFT JOIN Pr_Personnels				PR with (nolock) on  PFC.Pr_PersonnelID=PR.Pr_PersonnelID
		LEFT JOIN Pr_PersPosts				Post with (nolock) ON PR.Pr_PersonnelID = Post.Pr_PersonnelID AND (Post.DateBegin < getutcdate() and (Post.DateEnd is null or Post.DateEnd > getutcdate()))
		LEFT JOIN Pr_StaffTrees				TR with (nolock) ON Post.Pr_StaffTreeID = TR.Pr_StaffTreeID  
		LEFT  JOIN Pr_StaffTrees			PTR with (nolock) ON PTR.Pr_StaffTreeID = TR.Pr_StaffTreeIDParent 
		--OUTER APPLY (Select isnull(Pred,0) pred, isnull(Past,0) past,  isnull(HomeBase,58) HomeBase From dbo.PD_JPR_fn_GET_TimePrePast(PFA.DateTakeoff,PFLA.DateLanding,isnull(PR.Pr_PersonnelID,1))) TM    
		OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DT_CREnd) pst 
		OUTER APPLY 
		(
		Select top 1 
		isnull(PTR1.Ap_AirPortID ,58) Ap_AirPortID
		From Pr_Personnels PR1 with (nolock)
		LEFT JOIN Pr_PersPosts      Post1 with (nolock) ON PR1.Pr_PersonnelID = Post1.Pr_PersonnelID 
		LEFT JOIN Pr_StaffTrees     TR1   with (nolock) ON Post1.Pr_StaffTreeID = TR1.Pr_StaffTreeID  
		LEFT  JOIN Pr_StaffTrees    PTR1  with (nolock) ON PTR1.Pr_StaffTreeID = TR1.Pr_StaffTreeIDParent 
		where 
			PR1.Pr_PersonnelID=PR.Pr_PersonnelID  
			AND (PFA.DateTakeoff >= Post1.DateBegin and (PFA.DateTakeoff <=Post1.DateEnd or Post1.DateEnd is null)) 
		Order by Post1.DateBegin   ASC
		) HB
	WHERE 
		convert(date,PFL.FlightDate) between convert(date,dateadd(dd,-5,@DT_CRbegin))  and  convert(date,dateadd(dd,5,@DT_CREnd))
		AND PFL.OnlyFinanceCalculation = 0
		AND PFL.Sh_ScheduleVariantTypeID IS NULL
		and PFL.Status & 256 <> 256						 ----отмененные рейсы
		AND ISNULL(FL.FlightVariant,0) = 0 
		AND PFL.Fl_FlightMeanTypeID  IN (30)			 --Только пассажирские перевозки
		------------------------------------------------------------------
		and
		( 
			((PFC.CrewType=@TYPECREW) and (@TYPECREW=0)    and (PFC.OrderNumber in (1,2,3)) and (PR.Pr_CategoryTypeID=1)) ----указываем позицию АВИАБИТ
			or 
			((PFC.CrewType=@TYPECREW) and (@TYPECREW=1)    and (PFC.OrderNumber in (1,2,3,4,5,6,7)) and (PR.Pr_CategoryTypeID=5)) ----указываем позицию АВИАБИТ
			or
			((PFC.CrewType in (0,1))   and (@TYPECREW=11)  and (PFC.OrderNumber in (1,2,3,4,5,6,7)) and (PR.Pr_CategoryTypeID in (1,5))) ----ВСЕ
			----------------------------------------- учет пассажиров в экипаже ------------------------------------------------------------------------------
			or
			((PFC.CrewType=4) and (PFC.Pr_ArmChairTypeID is null)  and (@TYPECREW=0) and (PR.Pr_CategoryTypeID=1)) ----указываем позицию АВИАБИТ пассажир летный and (PFC.Pr_ArmChairTypeID is null) 
			or
			((PFC.CrewType=4) and (PFC.Pr_ArmChairTypeID is null)  and (@TYPECREW=1) and (PR.Pr_CategoryTypeID=5)) ----указываем позицию АВИАБИТ пассажир кабинныйand (PFC.Pr_ArmChairTypeID is null)
			or
			((PFC.CrewType=4) and (PFC.Pr_ArmChairTypeID is null)  and (@TYPECREW=11) and (PR.Pr_CategoryTypeID in (1,5))) ----ВСЕ
			-------------------------------------------------------------------------------------------------------------------------------------------------
		)
		and (( pst.DateEnd IS NULL OR pst.DateEnd > @DT_CRbegin) )
		and isnumeric(replace(Pr.TableNumber,'/','00')) >''
		--and (PR.TableNumber in ('1547','1563','1593','1621', '28', '270'))
		--ORDER BY
		--Pr_PersonnelID,
		--DateTakeoff 
--==========================================
INSERT INTO @FLT_TRP1
Select 
TT.Pr_PersonnelID,	
TT.Ap_PlanFlightID,
TT.DateTakeoff,
TT.DateLanding,
TT.FL_Name,
TT.HBD_AP,
TT.APTO,
TT.APLA,
TT.Pr_FlightTaskReestrID,
PRE.PRE,
PST.PST,
TT.Tabel,
TT.TehNumber, 	
TT.CrewType,
TT.Pr_CategoryTypeID,
TT.Fl_FlightID

From @FLT_TRP  TT
--=============================================Pre============================
OUTER APPLY 
(
		select top 1 
		datediff(hh,PR.DateLanding ,TT.DateTakeoff)	PRE	
		FROM @FLT_TRP PR
		where 
		PR.Pr_PersonnelID=TT.Pr_PersonnelID
		and TT.DateTakeoff>PR.DateLanding
		Order by PR.DateLanding  DESC
) PRE
OUTER APPLY
(
		select top 1 
		datediff(hh,TT.DateLanding, PR.DateTakeoff)	PST	
		FROM @FLT_TRP PR
		where 
		PR.Pr_PersonnelID=TT.Pr_PersonnelID
		and TT.DateLanding<PR.DateTakeoff
		Order by PR.DateTakeoff  ASC
) PST
Where PRE.PRE is not null
----ORDER BY
----Pr_PersonnelID,
----DateTakeoff 
--==========================================


--======================================================================================
--======================================================================================
INSERT INTO @Table 
Select 
TRP.Pr_PersonnelID,	
TRP.DateTakeoff,
TRP.DateLanding,
TRP.FL_Name,
TRP.HBD_AP,
TRP.TRbegHB,
TRP.TRbegVKO,
TRP.Trip,
TRP.HB_Trip,
--ROW_NUMBER() over(PARTITION BY TRP.Pr_PersonnelID,TRP.Trip, TRP.Pr_FlightTaskReestrID order by TRP.Pr_FlightTaskReestrID) NDuty,
TRP.Pr_FlightTaskReestrID NDuty,
TRP.APTO,
TRP.APLA,
TRP.Ap_PlanFlightID,
TRP.Pr_FlightTaskReestrID,
TRP.Pred, 
TRP.Past,
TRP.Tabel ,
TRP.TehNumber, 	
TRP.CrewType,
TRP.Pr_CategoryTypeID,
TRP.Fl_FlightID  
From
		--==============================================================================
		(Select 
		FT.Pr_PersonnelID,	
		FT.DateTakeoff,
		FT.DateLanding,
		FT.FL_Name,
		FT.HBD_AP,
		TRbegHB.Pr_FlightTaskReestrID TRbegHB,
		TRbegVKO.Pr_FlightTaskReestrID TRbegVKO,
		ISNULL(TRbegHB.Pr_FlightTaskReestrID, TRbegVKO.Pr_FlightTaskReestrID) Trip,
		Case 
		when TRbegHB.Pr_FlightTaskReestrID  is not null then FT.HBD_AP
		when TRbegHB.Pr_FlightTaskReestrID  is  null and TRbegVKO.Pr_FlightTaskReestrID is not null then 58
		else 58
		End HB_Trip,
		FT.APTO,
		FT.APLA,
		FT.Ap_PlanFlightID,
		FT.Pr_FlightTaskReestrID,
		FT.Pred, 
		FT.Past,
		FT.Tabel ,
		FT.TehNumber, 	
		FT.CrewType,
		FT.Pr_CategoryTypeID,
		FT.Fl_FlightID 
		From @FLT_TRP1  FT 
		outer apply (select TOP 1 * From @FLT_TRP1 TT 
					 WHERE
					 FT.Pr_PersonnelID=TT.Pr_PersonnelID	
					 and TT.DateTakeoff between dateadd(dd,-6,FT.DateTakeoff) and FT.DateTakeoff
					 and FT.HBD_AP=TT.APTO
					 and isnull(TT.Pred,0)>12
					 ORDER BY TT.DateLanding DESC
					 ) TRbegHB
		outer apply (select TOP 1 * From @FLT_TRP1 TT
					 WHERE
					 FT.Pr_PersonnelID=TT.Pr_PersonnelID	
					 and TT.DateTakeoff between dateadd(dd,-6,FT.DateTakeoff) and FT.DateTakeoff
					 and 58=TT.APTO
					 and isnull(TT.Pred,0)>12
					 ORDER BY TT.DateLanding DESC
					 ) TRbegVKO
		) TRP
Where TRP.Trip is not null
--ORDER BY
--TRP.Pr_PersonnelID,
--TRP.DateTakeoff, 
--TRP.HBD_AP, 
--TRP.Tabel,
--TRP.Ap_PlanFlightID,
--TRP.FL_Name,	
--TRP.TehNumber, 	
--TRP.CrewType,
--TRP.Pr_FlightTaskReestrID
		RETURN	
END

	