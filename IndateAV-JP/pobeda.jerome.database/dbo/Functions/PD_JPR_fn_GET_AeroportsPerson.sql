﻿
CREATE FUNCTION [dbo].[PD_JPR_fn_GET_AeroportsPerson] 
(	
	@DTbegin  datetime,  
	@DTEnd    datetime,
	@DTending datetime='2075-12-30 23:59:00',
	@Pr_PersonnelID integer 
)
RETURNS  @Table TABLE
(	
	Pr_PersonnelID integer,
	Tabel nvarchar(10),
	Fl integer,
	AP integer,
	DTBeg datetime,
	DTEnd datetime
)
AS 
Begin

--Testing---------------------------
------Declare 
------@DTbegin datetime='2018-03-01 00:00:00',  
------@DTEnd datetime=  '2018-12-31 23:59:00',
------@DTending datetime='2075-12-31 23:59:00',
------@Pr_PersonnelID integer =1582--1353--1633--1659 -- 1601--

Declare @APT Table
(
	Pr_PersonnelID integer
	,Tabel nvarchar(10)
	,DateTakeoff datetime
	,DateLanding datetime
	,HB_Trip integer
	,Fl integer
)

IF 
1=2  ---- статическое определение HB
--===================================
----EXISTS  ---- Динамическое определение HB
----	(SELECT 
----	   [Pr_PersonnelID]
----	  ,Tabel
----	  ,[DateTakeoff]
----	  ,[DateLanding]
----	  ,[HBD_AP]
----	  ,[HB_Trip]
----	 FROM [PD_JPR_TripFlightPerson]
----	 Where 
----	 [Pr_PersonnelID]=@Pr_PersonnelID
----	 and [DateTakeoff] between @DTbegin and @DTEnd
----	)
--=====================================
Begin
			DEclare @CURFOOD CURSOR
			Declare
			@Pr_Personnel integer,
			@Tabel nvarchar(10),
			@DateTakeoff datetime,
			@DateLanding  datetime,
			@HBD_AP integer,
			@HB_Trip integer,
			--------------------
			@CHT integer=0,
			@PR integer=0
			SET @CURFOOD  = CURSOR SCROLL
			FOR 
					SELECT 
						Pr_PersonnelID
						,Tabel
						,DateTakeoff
						,DateLanding
						,HBD_AP
						,HB_Trip
					FROM PD_JPR_TripFlightPerson
					Where 
					Pr_PersonnelID=@Pr_PersonnelID
					and DateTakeoff between @DTbegin and @DTEnd
					Order by Pr_PersonnelID,DateTakeoff	
			OPEN @CURFOOD
			FETCH NEXT FROM @CURFOOD INTO 
				@Pr_Personnel,
				@Tabel,
				@DateTakeoff,
				@DateLanding,
				@HBD_AP,
				@HB_Trip
			WHILE @@FETCH_STATUS = 0
			BEGIN
				IF @PR=0 
					Begin
					Set @CHT=1
					Set @PR=@HB_Trip
					End
				else
					Begin
					 IF @PR<>@HB_Trip
						Begin
							Set @CHT=@CHT+1
							Set @PR=@HB_Trip
						end
					end
				----------Запись---------------------------------------
				insert into @APT
				values
				(
				@Pr_Personnel,
				@Tabel,
				@DateTakeoff,
				@DateLanding,
				@HB_Trip,
				@CHT
				)
			FETCH NEXT FROM @CURFOOD INTO 
				@Pr_Personnel,
				@Tabel,
				@DateTakeoff,
				@DateLanding,
				@HBD_AP,
				@HB_Trip
			END
			CLOSE @CURFOOD
			DEALLOCATE @CURFOOD 
			--======================================================
			--======================================================
			INSERT INTO @Table
			Select 
			TT.Pr_PersonnelID,
			TT.Tabel,
			TT.Fl,
			max(TT.HB_Trip) AP,
			MIN(convert(date,TT.DateTakeoff)) DTBeg,
			MAX(convert(date,
				Case 
				when @CHT<>TT.Fl then TT.DateLanding
				else @DTending
				end
			   )) DTEnd
			From @APT TT
			GROUP by 
			TT.Pr_PersonnelID,
			TT.Tabel,
			TT.Fl
			--======================Сращиваем разрывы между записями по датам смены HB================================
			--======================================================
			UPDATE @Table
			SET DTBeg=dateadd(dd,1,ISNULL((Select TOP 1 PP.DTEnd From @Table PP Where  PP.Pr_PersonnelID=@Pr_PersonnelID and PP.DTEnd<T.DTBeg Order by PP.DTEnd Desc),T.DTBeg))
			From @Table T 
			Where 
			T.Pr_PersonnelID=@Pr_PersonnelID
			--======================================================
			--======================================================
End
ELSE
Begin				
			INSERT INTO @Table
			Select
			PR.Pr_PersonnelID,
			Pr.TableNumber,
			0,
			isnull(AP.Ap_AirPortID,58),
			PST.DateBegin,
			Isnull(PST.DateEnd,@DTending)
			From Pr_Personnels PR
			OUTER APPLY (select top 1 * From Pr_PersPosts t where t.Pr_PersonnelID=Pr.Pr_PersonnelID and ((@DTEnd >= T.DateBegin) and (@DTbegin<=T.DateEnd or T.DateEnd is null))  order by  t.DateBegin Desc) PST 
			LEft JOIN Pr_StaffTrees     STF on STF.Pr_StaffTreeID=PST.Pr_StaffTreeID
			left JOIN  Pr_StaffTrees PrnSTF ON PrnSTF.Pr_StaffTreeID=STF.Pr_StaffTreeIDParent
			left JOIN  Ap_AirPorts       AP on AP.Ap_AirPortID=PrnSTF.Ap_AirPortID
			OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (1,2)  order by Pr_Orders.Datebegin asc) ORD1  --and (@DTACT>= Pr_Orders.DateBegin or @DTACT <= Pr_Orders.DateBegin)
			OUTER APPLY (select top 1 * From Pr_Orders where Pr_Orders.Pr_PersonnelID=PR.Pr_PersonnelID and Pr_Orders.OrderType in (7)  order by Pr_Orders.Datebegin Desc) ORD2 
			OUTER APPLY ab_fn_Pr_Personnel_Post(PR.Pr_PersonnelID, @DTend) post 
			Where 
			(( post.DateEnd IS NULL OR post.DateEnd > @DTbegin) )
			and isnumeric(replace(Pr.TableNumber,'/','00')) >''
			and (Pr.Pr_CategoryTypeID in (1,5))
			and Pr.Pr_PersonnelID=@Pr_PersonnelID
End
RETURN	
END



