﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>pobeda.jerome.database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{8cab4aa9-2f62-4f27-9395-cbfb133716ae}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql130DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>pobeda.jerome.database</RootNamespace>
    <AssemblyName>pobeda.jerome.database</AssemblyName>
    <ModelCollation>1049,CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>Cyrillic_General_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="dbo\Views\" />
    <Folder Include="Security\" />
    <Folder Include="dbo\Functions\" />
    <Folder Include="dbo\Stored Procedures\" />
    <Folder Include="dbo\Stored Procedures\Procs1\" />
    <Folder Include="dbo\Stored Procedures\Procs2\" />
    <Folder Include="dbo\User Defined Types\" />
    <Folder Include="Assemblies\" />
    <Folder Include="dbo\Sequences\" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Functions\PD_JPR_fn_GET_TripAviabit_V1.sql" />
    <Build Include="dbo\Functions\PD_JPR_fn_GET_TimePrePast.sql" />
    <Build Include="dbo\Functions\PD_JPR_fn_GET_TripAviabit.sql" />
    <Build Include="dbo\Functions\PD_JPR_fn_GET_AeroportsPerson.sql" />
    <Build Include="dbo\Functions\PD_JPR_fn_GET_TripPersonAviabit.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_DeletePairings.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_AIRPORT.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_AIRPORT_M1.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_AIRPORT_M2.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_employment.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_info.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_not_fly_with.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_qualifications.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_qualifications_MOD1.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Crew_restrictions.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_historical_block_time.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_historical_duty_time.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Qualification_restrication .sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Recency_crew_history.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Recurrent_crew_expiry.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Recurrent_crew_expiry_mod1.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ExportEtab_Recurrent_crew_expiry_mod2.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_CaringIN.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_CaringOUT.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_Legs.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_SectionPAiring.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_SectionPAiring_Export.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_SectionPAiring_Export_Crew.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_SectionPAiring_Export_Crew_ALL.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_SectionPAiring_Export_Crew_ALL_1.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportCTF_SectionPAiring_Export_Crew_ALL_2.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportEtab_TurnoverAviaBoard.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_ImportTripPeriod.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_InsertAssignment.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_InsertEvent.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_InsertEXPOEvent.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_InsertEXPOMarker.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_InsertPairings.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_InsertTasK.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_InsertUniForms.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_Rep_Event_INNER.sql" />
    <Build Include="dbo\Stored Procedures\Procs2\PD_JPR_RUN_ImportCTF.sql" />
    <Build Include="ExtendedProperties.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assemblies\SitaMessages.dll" />
    <None Include="Assemblies\ReportTools.dll" />
    <None Include="Assemblies\Aviabit.Messages.dll" />
    <None Include="Assemblies\Aviabit.Messages.Destinations.Base.dll" />
    <None Include="Assemblies\Aviabit.Messages.CFMU.dll" />
    <None Include="Assemblies\Aviabit.Messages.Source.Base.dll" />
    <None Include="Assemblies\Aviabit.Messages.AHM.dll" />
    <None Include="Assemblies\Aviabit.Messages.Permissions.dll" />
    <None Include="Assemblies\Aviabit.Messages.Schedule.dll" />
    <None Include="Assemblies\Aviabit.Messages.SQL.dll" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="SitaMessages">
      <HintPath>Assemblies\SitaMessages.dll</HintPath>
      <SqlAssemblyName>SitaMessages</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="ReportTools">
      <HintPath>Assemblies\ReportTools.dll</HintPath>
      <SqlAssemblyName>ReportTools</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="Aviabit.Messages">
      <HintPath>Assemblies\Aviabit.Messages.dll</HintPath>
      <SqlAssemblyName>Aviabit.Messages</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="Aviabit.Messages.Destinations.Base">
      <HintPath>Assemblies\Aviabit.Messages.Destinations.Base.dll</HintPath>
      <SqlAssemblyName>Aviabit.Messages.Destinations.Base</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="Aviabit.Messages.CFMU">
      <HintPath>Assemblies\Aviabit.Messages.CFMU.dll</HintPath>
      <SqlAssemblyName>Aviabit.Messages.CFMU</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="Aviabit.Messages.Source.Base">
      <HintPath>Assemblies\Aviabit.Messages.Source.Base.dll</HintPath>
      <SqlAssemblyName>Aviabit.Messages.Source.Base</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="Aviabit.Messages.AHM">
      <HintPath>Assemblies\Aviabit.Messages.AHM.dll</HintPath>
      <SqlAssemblyName>Aviabit.Messages.AHM</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="Aviabit.Messages.Permissions">
      <HintPath>Assemblies\Aviabit.Messages.Permissions.dll</HintPath>
      <SqlAssemblyName>Aviabit.Messages.Permissions</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="Aviabit.Messages.Schedule">
      <HintPath>Assemblies\Aviabit.Messages.Schedule.dll</HintPath>
      <SqlAssemblyName>Aviabit.Messages.Schedule</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
    <Reference Include="Aviabit.Messages.SQL">
      <HintPath>Assemblies\Aviabit.Messages.SQL.dll</HintPath>
      <SqlAssemblyName>Aviabit.Messages.SQL</SqlAssemblyName>
      <IsModelAware>True</IsModelAware>
      <GenerateSqlClrDdl>False</GenerateSqlClrDdl>
      <SqlOwnerName>dbo</SqlOwnerName>
      <SqlPermissionSet>SAFE</SqlPermissionSet>
    </Reference>
  </ItemGroup>
</Project>