﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml;


namespace IndateAV_JP
{
    public partial class IMportForm : Form
    {
        public IMportForm()
        {
            InitializeComponent();
            StrConnectWWW = "Data Source = AeroDB03; Initial Catalog = aviacompany; Integrated Security = True";
            StrConnectTTT = "Data Source=AeroDB04;Initial Catalog=avia_gplr_4;Integrated Security=True"; //JP_Pairing avia_gplr_3
            StrConnect = StrConnectTTT;
            StrConnect1 = StrConnectTTT;
            StrConnect2 = StrConnectTTT;
        }
        private void button1_Click_1(object sender, EventArgs e)
        {
            Encoding utf8 = Encoding.UTF8;
            Encoding unicode = Encoding.Unicode;
            tabControl1.SelectTab(0);


            string fileName = null;
            char[] separators = new char[] { '\n', '\r' };


            if (openFileDialog1.ShowDialog() == DialogResult.OK)
            {
                fileName = openFileDialog1.FileName;
            }
            if (fileName != null)
            {
                FileText = File.ReadAllText(fileName, Encoding.UTF8);
                richTextBox1.Text = FileText;
                ListStr.Clear();

                progressBar1.Minimum = 0;
                progressBar1.Maximum = FileText.Split(separators).Count();
                progressBar1.Value = 0;
                foreach (string sub in FileText.Split(separators))
                {
                    progressBar1.Value = progressBar1.Value + 1;
                    progressBar1.Refresh();
                    StrLineFile Sline = new StrLineFile();

                    byte[] utf8Bytes = utf8.GetBytes(sub);
                    byte[] unicodeBytes = Encoding.Convert(utf8, unicode, utf8Bytes);

                    char[] unicodeChars = new char[unicode.GetCharCount(unicodeBytes, 0, unicodeBytes.Length)];
                    unicode.GetChars(unicodeBytes, 0, unicodeBytes.Length, unicodeChars, 0);

                    Sline.StrLine = new string(unicodeChars);
                    if (Sline.StrLine != "")
                    {
                        Sline.Nline = 1;
                        ListStr.Add(Sline);
                    }
                }
                progressBar1.Value = 0;
                progressBar1.Refresh();
                dataGridView1.DataSource = null;
                bindingSource1.DataSource = null;
                bindingSource1.DataSource = null;
                bindingNavigator1.BindingSource = null;
                dataGridView1.Rows.Clear();
                dataGridView1.Columns.Clear();

                bindingSource1.DataSource = ListStr.ToArray();
                bindingNavigator1.BindingSource = bindingSource1;
                dataGridView1.DataSource = bindingSource1;
                dataGridView1.AutoResizeColumns();
                dataGridView1.Columns[1].Visible = false;

                CreateModelCTF();
            }
        }

        private void CreateModelCTF()
        {
            List<string> PairingStr = new List<string>();
            List<string> FlightStr = new List<string>();
            List<string> TabelStr = new List<string>();
            List<string> DutyStr = new List<string>();
            List<string> PersonActyv = new List<string>();
            //=====================================================
            DataRow Drow;
            int Fl = 0;
            string StrTabel = "";
            string TypeCrew = "";
            string PPLS = "";
            Int64 Pare;
            string STRPARE;
            string STRPARE_NEW;
            string TypePairing = "";
            int Fchar = 0;
            int Nchar = 0;
            Boolean Bid = false;

            if (Paring.Rows.Count == 0)
            {
                Kind = 1;
                TabelCrew.Rows.Clear();
                CrewParing.Rows.Clear();
                Paring.Rows.Clear();
                Tasks.Rows.Clear();
                Flights.Rows.Clear();
            }
            else { Kind = Kind * 1000; }
            char[] separators = new char[] { ' ' };
            foreach (StrLineFile Sline in ListStr)
            {
                if (Sline.StrLine != "")
                {
                    if (Sline.StrLine.IndexOf("CREW:") == 0)
                    {
                        Fl = 1;
                        TabelStr.Clear();
                        foreach (string sub in Sline.StrLine.Split(separators))
                        {
                            TabelStr.Add(sub);
                        }
                        StrTabel = TabelStr[2].ToString();
                        StrTabel = StrTabel.Replace('"', ' ').Trim(' ');
                        //MessageBox.Show(StrTabel);
                    }
                    if (Fl == 1)
                    {
                        DutyStr.Clear();
                        Pare = 0;
                        foreach (string sub in Sline.StrLine.Split(separators))
                        {
                            DutyStr.Add(sub);
                        }
                        //=============================PPLS========================
                        if ((DutyStr[0].ToString() != "*") & (DutyStr[0].ToString() != "L") & (DutyStr[0].ToString() != "CREW:") & ((DutyStr[0].ToString() != "EOCREW")) & (Pare == 0) & (DutyStr[0].ToString() != ""))   //(DutyStr[0].ToString() != "EOCREW\r") || 
                        {
                            Pare = Convert.ToInt64(DutyStr[0].ToString()) * Kind;  /////////модификация на K
                                                                                   //MessageBox.Show(DutyStr[3].ToString());
                            Bid = false;
                            try { TypeCrew = DutyStr[3].ToString(); }
                            catch { }
                            //finally { TypeCrew = "";}
                            PPLS = "";
                            if (DutyStr.Count() == 5)
                            {
                                if (DutyStr[4].ToString().IndexOf("PPLS") >= 0 || DutyStr[4].ToString().IndexOf("PPKE") >= 0)
                                {
                                    PPLS = DutyStr[4].ToString();
                                    PPLS = PPLS.Replace("#", "");
                                }
                                Bid = false;
                                if (DutyStr[4].ToString().IndexOf("GrantedActivityBid") >= 0)
                                {
                                    Bid = true;
                                }
                            }
                            //==================Tabel=====================
                            Drow = TabelCrew.NewRow();
                            Drow.BeginEdit();
                            Drow.SetField("Tabel", StrTabel.ToString());
                            Drow.SetField("IdParing", Pare);
                            Drow.SetField("TypeCrew", TypeCrew.ToString());
                            Drow.SetField("PPLS", PPLS.ToString());
                            Drow.SetField("Bid", Bid);
                            Drow.EndEdit();
                            TabelCrew.Rows.Add(Drow);
                            Bid = false;
                        }
                        //=====================Personal==================================
                        //L * 0 20190116 VKO 0600 MED1 * * 1400 VKO 20190116
                        //* * 0 20190116 VKO 2100 OFF * * 2100 VKO 20190118
                        if (((DutyStr[0].ToString() == "*") || (DutyStr[0].ToString() == "L")) & (DutyStr[0].ToString() != "CREW:") & (DutyStr[0].ToString() != "EOCREW") & (Pare == 0))
                        {
                            Bid = false;
                            if (DutyStr.Count() == 13)
                            {
                                if (DutyStr[12].ToString() == "#GrantedTimeOffBid")
                                {
                                    Bid = true;
                                }
                            }

                            DateTime PDT1 = DateTime.ParseExact(DutyStr[3].ToString(), "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture);
                            PDT1 = PDT1.AddHours(Convert.ToDouble(DutyStr[5].ToString().Substring(0, 2)));
                            PDT1 = PDT1.AddHours(Convert.ToDouble(DutyStr[5].ToString().Substring(2, 2)) / 60.0);

                            DateTime PDT2 = DateTime.ParseExact(DutyStr[11].ToString(), "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture);
                            PDT2 = PDT2.AddHours(Convert.ToDouble(DutyStr[9].ToString().Substring(0, 2)));
                            PDT2 = PDT2.AddHours(Convert.ToDouble(DutyStr[9].ToString().Substring(2, 2)) / 60.0);

                            Drow = Flights.NewRow();
                            Drow.BeginEdit();
                            Drow.SetField("DataFlight", PDT1);
                            Drow.SetField("NumberFlight", 0);
                            Drow.SetField("CarGo", DutyStr[6].ToString());
                            Drow.SetField("APTKF", DutyStr[4].ToString());
                            Drow.SetField("APLND", DutyStr[10].ToString());
                            Drow.SetField("TypeFlight", "PERSACT");
                            Drow.SetField("IdParing", 0);  /////////модификация на K
                            Drow.SetField("IdTask", 0);
                            Drow.SetField("TPCrew", "");
                            Drow.SetField("STD", PDT1);
                            Drow.SetField("STA", PDT2);
                            Drow.SetField("Tabel", StrTabel.ToString());
                            Drow.SetField("Bid", Bid);
                            Drow.EndEdit();
                            Flights.Rows.Add(Drow);
                            Bid = false;
                        }
                    }
                    if (Sline.StrLine.IndexOf("EOCREW") == 0)
                    {
                        Fl = 0;
                        StrTabel = "";
                    }


                    if (Sline.StrLine.IndexOf("PAIRING:") == 0 & (Fl == 0))
                    {
                        Fchar = 0;
                        Nchar = 0;
                        try
                        {
                            Fchar = Sline.StrLine.IndexOf('"');
                            Nchar = Sline.StrLine.IndexOf('"', Fchar + 1);
                            STRPARE = Sline.StrLine.Substring(Fchar + 1, Nchar - Fchar - 1);
                            STRPARE_NEW = STRPARE.Replace(' ', '_');
                            Sline.StrLine = Sline.StrLine.Replace(STRPARE, STRPARE_NEW);
                        }
                        catch
                        {
                            //MessageBox.Show(Sline.StrLine.ToString());
                        }
                        finally
                        {
                            //MessageBox.Show(Sline.StrLine.ToString());
                        }


                        ////if (STRPARE.IndexOf("VKO")+1 > 0)
                        ////{ MessageBox.Show(STRPARE + " " + STRPARE_NEW); }
                        PairingStr.Clear();
                        foreach (string sub in Sline.StrLine.Split(separators))
                        {
                            PairingStr.Add(sub);
                        }
                        //====================Paring==================
                        try
                        {
                            Drow = Paring.NewRow();
                            Drow.BeginEdit();
                            Drow.SetField("Id_Paring", Convert.ToInt64(PairingStr[2].ToString()) * Kind);  /////////модификация на K
                            Drow.SetField("NameParing", PairingStr[3].ToString());
                            Drow.SetField("CountParing", Convert.ToInt32(PairingStr[1].ToString()));
                            try
                            { Drow.SetField("APBH", PairingStr[5].ToString()); }
                            catch
                            { }
                            Drow.EndEdit();
                            Paring.Rows.Add(Drow);
                        }
                        catch (System.Data.ConstraintException ee)
                        {
                            MessageBox.Show(ee.Message, "Error", MessageBoxButtons.OK);
                        }

                        //====================CrewParing====================
                        Drow = CrewParing.NewRow();
                        Drow.BeginEdit();
                        Drow.SetField("Id_Crew", Convert.ToInt64(PairingStr[2].ToString()));
                        Drow.SetField("NameType", PairingStr[4].ToString());
                        Drow.EndEdit();
                        CrewParing.Rows.Add(Drow);
                        //=========================================
                    }
                    else if
                        (
                            (
                            (Sline.StrLine.IndexOf("F ") == 0)
                            || (Sline.StrLine.IndexOf("O ") == 0)
                            || (Sline.StrLine.IndexOf("G ") == 0)
                            ) & (Fl == 0)
                        )
                    {
                        TypePairing = "FLIGHT";
                        //Резервные экипажи
                        if (Sline.StrLine.IndexOf("DBY1") > 0)
                        {
                            STRPARE = "DBY1 *";
                            STRPARE_NEW = "DP 001";
                            Sline.StrLine = Sline.StrLine.Replace(STRPARE, STRPARE_NEW);
                        }

                        if (Sline.StrLine.IndexOf("NBY1") > 0)
                        {
                            STRPARE = "NBY1 *";
                            STRPARE_NEW = "DP 002";
                            Sline.StrLine = Sline.StrLine.Replace(STRPARE, STRPARE_NEW);
                        }

                        if (Sline.StrLine.IndexOf("SDR1") > 0)
                        {
                            STRPARE = "SDR1 *";
                            STRPARE_NEW = "DP 003";
                            Sline.StrLine = Sline.StrLine.Replace(STRPARE, STRPARE_NEW);
                        }

                        if (Sline.StrLine.IndexOf("SNR1") > 0)
                        {
                            STRPARE = "SNR1 *";
                            STRPARE_NEW = "DP 004";
                            Sline.StrLine = Sline.StrLine.Replace(STRPARE, STRPARE_NEW);
                        }

                        FlightStr.Clear();
                        foreach (string sub in Sline.StrLine.Split(separators))
                        {
                            FlightStr.Add(sub);
                        }

                        //==============================Tasks===============================
                        Drow = Tasks.NewRow();
                        Drow.BeginEdit();
                        Drow.SetField("NameTask", PairingStr[2].ToString() + "-" + FlightStr[13].ToString());
                        Drow.SetField("Id_Pairing", Convert.ToInt64(PairingStr[2].ToString()) * Kind); /////////модификация на K
                        Drow.EndEdit();
                        if (Tasks.Rows.Count == 0)
                        {
                            Tasks.Rows.Add(Drow);
                        }
                        else if (Tasks.Rows[Tasks.Rows.Count - 1]["NameTask"].ToString() != PairingStr[2].ToString() + "-" + FlightStr[13].ToString())
                        {
                            Tasks.Rows.Add(Drow);
                        }

                        //========================Flight====================================
                        // F L * 20180428 LED 2055 DP 0515 * 1 2340 SVX 20180428 #duty1
                        // G * X 20181225 VKO 0600 DBY * * 1 1800 VKO 20181225 #duty1
                        // G * N 20190114 LED 0938 IFFS * * 1 1650 LED 20190114 #duty1
                        //=====================Personal==================================
                        //L * 0 20190116 VKO 0600 MED1 * * 1400 VKO 20190116
                        //* * 0 20190116 VKO 2100 OFF * *2100 VKO 20190118

                        //Резервные экипажи
                        if ((Sline.StrLine.IndexOf("G ") == 0) & !((Sline.StrLine.IndexOf("DP 001") > 0) || (Sline.StrLine.IndexOf("DP 002") > 0) || (Sline.StrLine.IndexOf("DP 003") > 0) || (Sline.StrLine.IndexOf("DP 004") > 0)))
                        {
                            FlightStr[7] = "0";
                            TypePairing = "GRDUTY";
                        }

                        DateTime DT1 = DateTime.ParseExact(FlightStr[3].ToString(), "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture);
                        DateTime DT2 = DateTime.ParseExact(FlightStr[12].ToString(), "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture);
                        TabelCrew.DefaultView.RowFilter = "IdParing=" + Convert.ToInt64(PairingStr[2].ToString()) * Kind;
                        int n = 0;
                        do
                        {
                            Drow = Flights.NewRow();
                            Drow.BeginEdit();
                            Drow.SetField("NumberFlight", Convert.ToInt32(FlightStr[7].ToString().ToString()));
                            ////if (FlightStr[6].ToString() == "FFS" & (PairingStr[4].ToString() == "1/0/0/0/0/0/0/0" || PairingStr[4].ToString() == "0/1/0/0/0/0/0/0"))
                            ////{
                            ////    Drow.SetField("CarGo", "FFS");
                            ////}
                            ////if (FlightStr[6].ToString() == "FFS" & PairingStr[4].ToString() == "0/0/1/0/0/0/0/0")
                            ////{
                            ////    Drow.SetField("CarGo", "IFFS");
                            ////}
                            ////else
                            ////{
                            ////    Drow.SetField("CarGo", FlightStr[6].ToString());
                            ////}
                            Drow.SetField("CarGo", FlightStr[6].ToString());
                            Drow.SetField("APTKF", FlightStr[4].ToString());
                            Drow.SetField("APLND", FlightStr[11].ToString());
                            Drow.SetField("TypeFlight", FlightStr[1].ToString());
                            Drow.SetField("IdParing", Convert.ToInt64(PairingStr[2].ToString()) * Kind);  /////////модификация на K
                            Drow.SetField("IdTask", Convert.ToInt64(Tasks.Rows[Tasks.Rows.Count - 1]["Id_Task"].ToString()));
                            Drow.SetField("TPCrew", PairingStr[4].ToString());
                            ///включение Табеля -------------------------------------
                            if (TabelCrew.DefaultView.Count > 0)
                            {
                                Drow.SetField("Tabel", TabelCrew.DefaultView[n]["Tabel"].ToString());
                                if (TabelCrew.DefaultView[n]["TypeCrew"].ToString() != null)
                                {
                                    Drow.SetField("TPCrew", TabelCrew.DefaultView[n]["TypeCrew"].ToString());
                                    //==================================FFS====================================
                                    if (FlightStr[6].ToString() == "FFS" & (TabelCrew.DefaultView[n]["TypeCrew"].ToString() == "1/0/0/0/0/0/0/0" || TabelCrew.DefaultView[n]["TypeCrew"].ToString() == "0/1/0/0/0/0/0/0"))
                                    {
                                        Drow.SetField("CarGo", "FFS");
                                    }
                                    if (FlightStr[6].ToString() == "FFS" & TabelCrew.DefaultView[n]["TypeCrew"].ToString() == "0/0/1/0/0/0/0/0")
                                    {
                                        Drow.SetField("CarGo", "IFFS");
                                    }
                                    //===================================FFSO=============================
                                    if (FlightStr[6].ToString() == "FFSO" & (TabelCrew.DefaultView[n]["TypeCrew"].ToString() == "1/0/0/0/0/0/0/0" || TabelCrew.DefaultView[n]["TypeCrew"].ToString() == "0/1/0/0/0/0/0/0"))
                                    {
                                        Drow.SetField("CarGo", "FFSO");
                                    }
                                    if (FlightStr[6].ToString() == "FFSO" & TabelCrew.DefaultView[n]["TypeCrew"].ToString() == "0/0/1/0/0/0/0/0")
                                    {
                                        Drow.SetField("CarGo", "IFFSO");
                                    }
                                }
                                //-------------------------------------------------------------------------------
                                if (TabelCrew.DefaultView[n]["PPLS"].ToString() != null)
                                { Drow.SetField("PPLS", TabelCrew.DefaultView[n]["PPLS"].ToString()); }
                                Boolean ff = (Boolean)TabelCrew.DefaultView[n]["Bid"];
                                if (ff)
                                { Drow.SetField("Bid", true); }
                            }
                            DT1 = DateTime.ParseExact(FlightStr[3].ToString(), "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture);
                            Drow.SetField("DataFlight", DT1);
                            DT1 = DT1.AddHours(Convert.ToDouble(FlightStr[5].ToString().Substring(0, 2)));
                            DT1 = DT1.AddHours(Convert.ToDouble(FlightStr[5].ToString().Substring(2, 2)) / 60.0);
                            Drow.SetField("STD", DT1);

                            DT2 = DateTime.ParseExact(FlightStr[12].ToString(), "yyyyMMdd", System.Globalization.CultureInfo.InvariantCulture);
                            Drow.SetField("STA", DT2);
                            DT2 = DT2.AddHours(Convert.ToDouble(FlightStr[10].ToString().Substring(0, 2)));
                            DT2 = DT2.AddHours(Convert.ToDouble(FlightStr[10].ToString().Substring(2, 2)) / 60.0);
                            Drow.SetField("STA", DT2);
                            Drow.EndEdit();
                            n++;
                            Flights.Rows.Add(Drow);
                        }
                        while (n < TabelCrew.DefaultView.Count);
                        ////------------------------------------------------------

                        /////////////////////////
                        if (Tasks.Rows[Tasks.Rows.Count - 1]["DataBeg"] is DBNull)
                        {
                            Tasks.Rows[Tasks.Rows.Count - 1].BeginEdit();
                            Tasks.Rows[Tasks.Rows.Count - 1].SetField("DataBeg", DT1);
                            Tasks.Rows[Tasks.Rows.Count - 1].EndEdit();
                        }
                        ////////////////////////
                        if (Paring.Rows[Paring.Rows.Count - 1]["DateBeg"] is DBNull)
                        {
                            Paring.Rows[Paring.Rows.Count - 1].BeginEdit();
                            Paring.Rows[Paring.Rows.Count - 1].SetField("DateBeg", DT1);
                            Paring.Rows[Paring.Rows.Count - 1].EndEdit();
                        }
                        ////////////////////////

                        ///////////////////////
                        Tasks.Rows[Tasks.Rows.Count - 1].BeginEdit();
                        Tasks.Rows[Tasks.Rows.Count - 1].SetField("DataEnd", DT2);
                        Tasks.Rows[Tasks.Rows.Count - 1].EndEdit();
                        ///////////////////////
                        Paring.Rows[Paring.Rows.Count - 1].BeginEdit();
                        Paring.Rows[Paring.Rows.Count - 1].SetField("DateEnd", DT2);
                        Paring.Rows[Paring.Rows.Count - 1].SetField("TypeParing", TypePairing);
                        Paring.Rows[Paring.Rows.Count - 1].EndEdit();

                        //Flights.Rows.Add(Drow);
                        //===============================================================
                    }
                }
            }
            //===============================
            DV_FlightCP = new DataView(Flights);
            DV_FlightMEM = new DataView(Flights);
            DV_Task = new DataView(Tasks);
            DV_PersAKT = new DataView(Flights);
            //=========================================

            //==========================================
            //==============================
            bindingSource2.DataSource = Paring;
            bindingNavigator2.BindingSource = bindingSource2;
            dataGridView2.DataSource = bindingSource2;
            dataGridView2.AutoResizeColumns();
            dataGridView2.Refresh();

            bindingSource3.DataSource = Tasks;
            bindingNavigator3.BindingSource = bindingSource3;
            dataGridView3.DataSource = bindingSource3;
            dataGridView3.AutoResizeColumns();
            dataGridView3.Refresh();

            DV_Flight = new DataView(Flights);
            bindingSource4.DataSource = DV_Flight;
            bindingNavigator4.BindingSource = bindingSource4;
            dataGridView5.DataSource = bindingSource4;
            dataGridView5.AutoResizeColumns();
            dataGridView5.Refresh();

            DV_PersAKT.RowFilter = "TypeFlight = 'PERSACT'";
            bindingSource7.DataSource = DV_PersAKT;
            bindingNavigator7.BindingSource = bindingSource7;
            dataGridView7.DataSource = bindingSource7;
            dataGridView7.AutoResizeColumns();
            dataGridView7.Refresh();
            //-----------------------------------------------
            //bindingSource4.DataSource = null;
            //Flights.DefaultView.RowFilter = "IdParing=" + dataGridView2.CurrentRow.Cells["Id_Paring"].Value.ToString();
            //bindingSource4.DataSource = Flights;
            //bindingNavigator4.BindingSource = bindingSource4;
            //dataGridView5.DataSource = bindingNavigator4; 
            //--------------------------------------------------------

            //tabControl1.SelectTab(1);
        }

        private void dataGridView2_RowEnter(object sender, DataGridViewCellEventArgs e)
        {
            if (Tasks.Rows.Count > 0)
                Tasks.DefaultView.RowFilter = "Id_Pairing=" + dataGridView2.Rows[e.RowIndex].Cells["Id_Paring"].Value.ToString();
            if (Flights.Rows.Count > 0)
                DV_Flight.RowFilter = "IdParing=" + dataGridView2.Rows[e.RowIndex].Cells["Id_Paring"].Value.ToString();
        }

        private void button4_Click(object sender, EventArgs e)
        {
            tabControl1.TabPages[2].Select();
        }

        private void dataGridView3_RowEnter_1(object sender, DataGridViewCellEventArgs e)
        {
            if (Flights.Rows.Count > 0)
                DV_Flight.RowFilter = "IdTask=" + dataGridView3.Rows[e.RowIndex].Cells["Id_Task"].Value.ToString();
        }

        private void button4_Click_1(object sender, EventArgs e)
        {
            if (Flights.Rows.Count > 0)
            {
                if (radioButton2.Checked)
                {
                    //выгрузить по фильтру
                    Paring.DefaultView.RowFilter = "TypeParing='FLIGHT' and DateBeg >= #" + Convert.ToDateTime(dateTimePicker1.Value).ToString("MM.dd.yyyy hh:mm:ss") + "# and DateBeg <=#" + Convert.ToDateTime(dateTimePicker2.Value).ToString("MM.dd.yyyy hh:mm:ss") + "#";
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать цепочки JP в АВИАБИТ", "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {
                        progressBar1.Maximum = Paring.DefaultView.Count + 1;
                        foreach (DataRowView Drow in Paring.DefaultView)
                        {
                            progressBar1.Value = i++;
                            if (Flights.Rows.Count > 0)
                            {
                                if (radioButton4.Checked)
                                {
                                    Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString() + " and TPCrew like('1/%')";
                                }
                                if (radioButton5.Checked)
                                {
                                    Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString() + " and TPCrew like('0/0/0/1/%')";
                                }
                                button4.UseWaitCursor = true;
                                InsertPairingAviabit();
                                Flights.DefaultView.RowFilter = "";
                            }
                        }
                        progressBar1.Value = 0;
                        button4.UseWaitCursor = false;
                    }
                    Paring.DefaultView.RowFilter = "";
                }

                if (radioButton1.Checked)
                {
                    //выгрузить все
                    Paring.DefaultView.RowFilter = "TypeParing='FLIGHT'";
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать цепочки JP в АВИАБИТ", "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {
                        progressBar1.Maximum = Paring.DefaultView.Count + 1;
                        foreach (DataRowView Drow in Paring.DefaultView)
                        {
                            progressBar1.Value = i++;
                            if (Flights.Rows.Count > 0)
                            {
                                if (radioButton4.Checked)
                                {
                                    Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString() + " and TPCrew like('1/%')";
                                }
                                if (radioButton5.Checked)
                                {
                                    Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString() + " and TPCrew like('0/0/0/1/%')";
                                }
                                button4.UseWaitCursor = true;
                                InsertPairingAviabit();
                                Flights.DefaultView.RowFilter = "";
                            }
                        }
                        progressBar1.Value = 0;
                        button4.UseWaitCursor = false;
                    }
                    Paring.DefaultView.RowFilter = "";
                }

                if (radioButton3.Checked)
                {
                    //выгрузить текущую
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать цепочки JP в АВИАБИТ", "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {
                        progressBar1.Maximum = 1;
                        progressBar1.Value = i++;
                        if ((Flights.Rows.Count > 0) & (dataGridView2.Rows[dataGridView2.CurrentRow.Index].Cells["TypeParing"].Value.ToString() == "FLIGHT"))
                        {
                            if (radioButton4.Checked)
                            {
                                Flights.DefaultView.RowFilter = "IdParing=" + dataGridView2.Rows[dataGridView2.CurrentRow.Index].Cells["Id_Paring"].Value.ToString() + " and TPCrew like('1/%')";
                            }
                            if (radioButton5.Checked)
                            {
                                Flights.DefaultView.RowFilter = "IdParing=" + dataGridView2.Rows[dataGridView2.CurrentRow.Index].Cells["Id_Paring"].Value.ToString() + " and TPCrew like('0/0/0/1/%')";
                            }
                            button4.UseWaitCursor = true;
                            InsertPairingAviabit();
                            Flights.DefaultView.RowFilter = "";
                        }
                        progressBar1.Value = 0;
                        button4.UseWaitCursor = false;
                        Flights.DefaultView.RowFilter = "";
                    }
                }
            }
        }

        private void DeleteAllEmptyFlightTaskReestrs()
        {
            StringBuilder ST;
            String ConStr;
            XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder());
            System.Data.SqlClient.SqlConnection Conect;
            System.Data.SqlClient.SqlCommand Command;
            //=========================================

            ConStr = StrConnect2;   /// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True"; //JP_Pairing //avia_gplr_3
            Conect = new System.Data.SqlClient.SqlConnection(ConStr);
            Command = Conect.CreateCommand();
            Command.CommandText = "PD_sp_DeleteAllEmptyFlightTaskReestrs";
            Command.CommandType = CommandType.StoredProcedure;
            Conect.Open();
            //======================================
            try
            {
                Command.ExecuteNonQuery();
            }
            catch
            { }

            //==============================================
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
            //====================================
        }
        private void InsertEventAviabit()
        {
            DataTable Dt;
            StringBuilder ST;
            String XMLStr;
            String DataXMLStr = "";
            String ConStr;
            XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder());

            System.Data.SqlClient.SqlConnection Conect;
            System.Data.SqlClient.SqlCommand Command;
            //=========================================
            ConStr = StrConnect;   /// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True"; //JP_Pairing //avia_gplr_3
            Conect = new System.Data.SqlClient.SqlConnection(ConStr);
            Command = Conect.CreateCommand();
            Command.CommandText = "PD_JPR_InsertEXPOEvent";
            Command.CommandType = CommandType.StoredProcedure;
            Command.Parameters.Add("@STRXML", SqlDbType.NVarChar);
            Conect.Open();
            //========================================
            //========================================
            Dt = Flights.DefaultView.ToTable();
            Dt.WriteXml(XMLWR);
            XMLStr = ST.ToString();
            int Nbeg = XMLStr.IndexOf("<Flights>");
            int NEnd = XMLStr.IndexOf("</DocumentElement>");
            if (Nbeg > 0)
            {
                DataXMLStr = XMLStr.Substring(Nbeg, NEnd - Nbeg);
            }
            File.WriteAllText("rrrrr.xml", DataXMLStr);
            Command.Parameters["@STRXML"].Value = DataXMLStr;
            try
            {
                Command.ExecuteNonQuery();
            }
            catch
            { }
            //==============================================
            Dt.Clear();
            Dt.Dispose();
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
            //====================================
        }

        private void InsertCrewBidsAviabit()
        {
            DataTable Dt;
            StringBuilder ST;
            String XMLStr;
            String DataXMLStr = "";
            String ConStr;
            XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder());

            System.Data.SqlClient.SqlConnection Conect;
            System.Data.SqlClient.SqlCommand Command;
            //=========================================

            ConStr = StrConnect;   /// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True"; //JP_Pairing //avia_gplr_3
            Conect = new System.Data.SqlClient.SqlConnection(ConStr);
            Command = Conect.CreateCommand();
            Command.CommandText = "PD_JPR_InsertEXPOMarker";
            Command.CommandType = CommandType.StoredProcedure;
            Command.Parameters.Add("@STRXML", SqlDbType.NVarChar);
            Conect.Open();
            //========================================
            //========================================
            Dt = Flights.DefaultView.ToTable();
            Dt.WriteXml(XMLWR);
            XMLStr = ST.ToString();
            int Nbeg = XMLStr.IndexOf("<Flights>");
            int NEnd = XMLStr.IndexOf("</DocumentElement>");
            if (Nbeg > 0)
            {
                DataXMLStr = XMLStr.Substring(Nbeg, NEnd - Nbeg);
            }
            File.WriteAllText("rrrrr.xml", DataXMLStr);
            Command.Parameters["@STRXML"].Value = DataXMLStr;
            try
            {
                Command.ExecuteNonQuery();
            }
            catch
            { }
            //==============================================
            Dt.Clear();
            Dt.Dispose();
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
            //====================================
        }

        private void InsertTaskAviabit()
        {
            DataTable Dt;
            StringBuilder ST;
            String XMLStr;
            String DataXMLStr = "";
            String ConStr;
            XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder());
            Int64 TYPECREW = 0;
            Int64 RG = 0;

            System.Data.SqlClient.SqlConnection Conect;
            System.Data.SqlClient.SqlCommand Command;
            //=========================================
            if (radioButton17.Checked)
            { TYPECREW = 11; }
            if (radioButton18.Checked)
            { TYPECREW = 1; }
            if (radioButton22.Checked)
            { TYPECREW = 0; }
            //===========================================
            if (radioButton23.Checked)
            { RG = 0; }
            if (radioButton24.Checked)
            { RG = 1; }
            if (radioButton25.Checked)
            { RG = 2; }
            //==========================================
            ConStr = StrConnect2;   /// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True"; //JP_Pairing //avia_gplr_3
            Conect = new System.Data.SqlClient.SqlConnection(ConStr);
            Command = Conect.CreateCommand();
            Command.CommandText = "PD_JPR_InsertTask";
            Command.CommandType = CommandType.StoredProcedure;
            Command.Parameters.Add("@STRXML", SqlDbType.NVarChar);
            Command.Parameters.Add("@TYPECREW", SqlDbType.Int);
            Command.Parameters.Add("@RG", SqlDbType.Int);
            Conect.Open();
            //======================================

            Dt = Flights.DefaultView.ToTable();
            Dt.WriteXml(XMLWR);
            XMLStr = ST.ToString();
            int Nbeg = XMLStr.IndexOf("<Flights>");
            int NEnd = XMLStr.IndexOf("</DocumentElement>");
            if (Nbeg > 0)
            {
                DataXMLStr = XMLStr.Substring(Nbeg, NEnd - Nbeg);
            }
            File.WriteAllText("rrrrr.xml", DataXMLStr);
            ////MessageBox.Show(DataXMLStr);
            Command.Parameters["@STRXML"].Value = DataXMLStr;
            Command.Parameters["@TYPECREW"].Value = TYPECREW;
            Command.Parameters["@RG"].Value = RG;
            try
            {
                Command.ExecuteNonQuery();
            }
            catch
            { }

            //==============================================
            Dt.Clear();
            Dt.Dispose();
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
            //====================================
        }

        private void InsertAssignmentAviabit(string Rem)
        {
            DataTable Dt;
            StringBuilder ST;
            String XMLStr;
            String DataXMLStr = "";
            String ConStr;
            XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder());
            Int64 TYPECREW = 0;
            System.Data.SqlClient.SqlConnection Conect;
            System.Data.SqlClient.SqlCommand Command;

            //=========================================
            if (radioButton17.Checked)
            { TYPECREW = 11; }
            if (radioButton18.Checked)
            { TYPECREW = 1; }
            if (radioButton22.Checked)
            { TYPECREW = 0; }
            //===========================================
            //=========================================

            ConStr = StrConnect;   /// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True"; //JP_Pairing //avia_gplr_3
            Conect = new System.Data.SqlClient.SqlConnection(ConStr);
            Command = Conect.CreateCommand();
            Command.CommandText = "PD_JPR_InsertAssignment";
            Command.CommandType = CommandType.StoredProcedure;
            Command.Parameters.Add("@STRXML", SqlDbType.NVarChar);
            Command.Parameters.Add("@TYPECREW", SqlDbType.Int);
            Command.Parameters.Add("@REM", SqlDbType.NVarChar);
            Conect.Open();
            //======================================

            Dt = Flights.DefaultView.ToTable();
            Dt.WriteXml(XMLWR);
            XMLStr = ST.ToString();
            int Nbeg = XMLStr.IndexOf("<Flights>");
            int NEnd = XMLStr.IndexOf("</DocumentElement>");
            if (Nbeg > 0)
            {
                DataXMLStr = XMLStr.Substring(Nbeg, NEnd - Nbeg);
            }

            //File.WriteAllText("rrrrr.xml", DataXMLStr);
            //MessageBox.Show(DataXMLStr);
            Command.Parameters["@STRXML"].Value = DataXMLStr;
            Command.Parameters["@TYPECREW"].Value = TYPECREW;
            Command.Parameters["@REM"].Value = Rem;
            Command.ExecuteNonQuery();
            //==============================================
            Dt.Clear();
            Dt.Dispose();
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
            //====================================
        }

        private void InsertPairingAviabit()
        {
            DataTable Dt;
            StringBuilder ST;
            String XMLStr;
            String DataXMLStr = "";
            String ConStr;
            XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder());
            System.Data.SqlClient.SqlConnection Conect;
            System.Data.SqlClient.SqlCommand Command;
            //=========================================

            ConStr = StrConnect;   /// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True"; //JP_Pairing //avia_gplr_3
            Conect = new System.Data.SqlClient.SqlConnection(ConStr);
            Command = Conect.CreateCommand();
            Command.CommandText = "PD_JPR_InsertPairings";
            Command.CommandType = CommandType.StoredProcedure;
            Command.Parameters.Add("@STRXML", SqlDbType.NVarChar);
            Command.Parameters.Add("@SENS", SqlDbType.Int);
            Conect.Open();
            //======================================

            Dt = Flights.DefaultView.ToTable();
            Dt.WriteXml(XMLWR);
            XMLStr = ST.ToString();
            int Nbeg = XMLStr.IndexOf("<Flights>");
            int NEnd = XMLStr.IndexOf("</DocumentElement>");
            if (Nbeg > 0)
            {
                DataXMLStr = XMLStr.Substring(Nbeg, NEnd - Nbeg);
            }
            //File.WriteAllText("rrrrr.xml", DataXMLStr);
            //MessageBox.Show(DataXMLStr);
            Command.Parameters["@STRXML"].Value = DataXMLStr;
            if (radioButton4.Checked)
            { Command.Parameters["@SENS"].Value = 2; }
            if (radioButton5.Checked)
            { Command.Parameters["@SENS"].Value = 4; }
            Command.ExecuteNonQuery();
            //==============================================
            Dt.Clear();
            Dt.Dispose();
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
            //====================================
        }

        private void button5_Click(object sender, EventArgs e)
        {
            //PD_JPR_DeletePairings
            DialogResult result;
            result = MessageBox.Show("Удалить ВСЕ цепочки JP из АВИАБИТ", "Удаление", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                StringBuilder ST;
                String ConStr;
                XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder());
                System.Data.SqlClient.SqlConnection Conect;
                System.Data.SqlClient.SqlCommand Command;
                //======================================
                ConStr = StrConnect1; /// "Data Source=AeroAS03;Initial Catalog=JP_Pairing ;Integrated Security=True"; //JP_Pairing//avia_gplr_3
                Conect = new System.Data.SqlClient.SqlConnection(ConStr);
                Command = Conect.CreateCommand();
                Command.CommandText = "PD_JPR_DeletePairings";
                Command.CommandType = CommandType.StoredProcedure;
                Command.Parameters.Add("@DTBegin", SqlDbType.Date);
                Command.Parameters.Add("@DTEnd", SqlDbType.Date);
                Command.Parameters.Add("@NameComment", SqlDbType.NVarChar);
                Command.Parameters.Add("@RZD", SqlDbType.Int);
                //---------------------------
                if (radioButton9.Checked)
                {
                    Command.Parameters["@DTBegin"].Value = dateTimePicker4.Value;
                    Command.Parameters["@DTEnd"].Value = dateTimePicker3.Value;
                }
                else
                {
                    Command.Parameters["@DTBegin"].Value = null;
                    Command.Parameters["@DTEnd"].Value = null;
                }

                if (radioButton8.Checked)
                {
                    Command.Parameters["@NameComment"].Value = dataGridView2.CurrentRow.Cells["Id_Paring"].Value.ToString();
                }
                else
                {
                    Command.Parameters["@NameComment"].Value = null;
                }


                if (radioButton6.Checked)
                { Command.Parameters["@RZD"].Value = 2; }
                if (radioButton7.Checked)
                { Command.Parameters["@RZD"].Value = 4; }
                //---------------------------
                Conect.Open();
                Command.ExecuteNonQuery();
                Command.Dispose();
                Conect.Close();
                Conect.Dispose();
            }

        }

        private void radioButton11_CheckedChanged(object sender, EventArgs e)
        {
            StrConnect = StrConnectTTT; /// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True";
        }

        private void radioButton12_CheckedChanged(object sender, EventArgs e)
        {
            StrConnect = StrConnectWWW;/// "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True";
        }

        private void radioButton14_CheckedChanged(object sender, EventArgs e)
        {
            StrConnect1 = StrConnectWWW;///// "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True";
        }

        private void radioButton13_CheckedChanged(object sender, EventArgs e)
        {
            StrConnect1 = StrConnectTTT; //// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True";
        }

        private void button2_Click(object sender, EventArgs e)
        {
            DV_FlightCP.RowFilter = "";
            DV_FlightCP.RowFilter = "(TypeFlight='L' or NumberFlight=1 or NumberFlight=2) and TPCrew like('1/%')";
            if (DV_FlightCP.Count > 0)
            {
                progressBar2.Minimum = 0;
                progressBar2.Maximum = DV_FlightCP.Count;
                progressBar2.Value = 0;
                foreach (DataRowView Row in DV_FlightCP)
                {
                    progressBar2.Value = progressBar2.Value + 1;
                    progressBar2.Refresh();

                    Row.BeginEdit();
                    Row["IDTaskCP"] = Row["IdTask"];
                    Row.EndEdit();

                    DV_Task.RowFilter = "ID_Task=" + Row["IdTask"];
                    foreach (DataRowView TRow in DV_Task)
                    {
                        TRow.BeginEdit();
                        TRow["ID_TaskCP"] = Row["IdTask"];
                        TRow.EndEdit();
                    }

                    string VSTR = "(DataFlight=#" + Convert.ToDateTime(Row["DataFlight"]).ToString("MM.dd.yyyy") + "#) and (NumberFlight= '" + Row["NumberFlight"].ToString() + "') and ((TypeFlight='D' and TPCrew like('1/%')) OR (TPCrew like('0/%')) OR  TPCrew like('1/%'))";

                    //MessageBox.Show(VSTR);

                    DV_FlightMEM.RowFilter = VSTR;
                    if (DV_FlightMEM.Count > 0)
                    {
                        foreach (DataRowView MRow in DV_FlightMEM)
                        {
                            MRow.BeginEdit();
                            MRow["IDTaskCP"] = Row["IdTask"];
                            MRow.EndEdit();
                        }
                    }
                    else
                    {
                        //MessageBox.Show("0");
                    }
                }
                progressBar2.Value = 0;
                //===========================================
                DV_Task.RowFilter = "ID_TaskCP > 0";
                bindingSource5.DataSource = DV_Task;
                bindingNavigator5.BindingSource = bindingSource5;
                dataGridView4.DataSource = bindingSource5;
                dataGridView4.AutoResizeColumns();
                dataGridView4.Refresh();
                try
                {
                    DV_FlightCP.RowFilter = "";
                    DV_FlightCP.RowFilter = "IDTaskCP=" + dataGridView4.CurrentRow.Cells["ID_TaskCP"].Value.ToString();
                }
                catch { }
                bindingSource6.DataSource = DV_FlightCP;
                bindingNavigator6.BindingSource = bindingSource6;
                dataGridView6.DataSource = bindingSource6;
                dataGridView6.AutoResizeColumns();
                dataGridView6.Refresh();


            }

        }

        private void dataGridView4_RowEnter(object sender, DataGridViewCellEventArgs e)
        {
            if (DV_Task.Count > 0)
                DV_FlightCP.RowFilter = "IDTaskCP=" + dataGridView4.Rows[e.RowIndex].Cells["ID_TaskCP"].Value.ToString();
            DV_FlightCP.Sort = "STD, TPCrew";

        }

        private void button6_Click(object sender, EventArgs e)
        {

            if (Flights.Rows.Count > 0)
            {
                if (radioButton19.Checked)
                {
                    //выгрузить текущую
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать ПЗ в АВИАБИТ", "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {
                        progressBar2.Maximum = 2;
                        progressBar2.Minimum = 0;
                        progressBar2.Value = 1;
                        progressBar2.Value = i++;
                        progressBar2.Refresh();
                        if (Flights.Rows.Count > 0)
                        {
                            Flights.DefaultView.RowFilter = "IDTaskCP=" + dataGridView4.Rows[dataGridView4.CurrentRow.Index].Cells["ID_TaskCP"].Value.ToString();
                            button6.UseWaitCursor = true;
                            InsertTaskAviabit();
                        }
                        Flights.DefaultView.RowFilter = "";
                        progressBar2.Value = 0;
                        progressBar2.Refresh();
                        button6.UseWaitCursor = false;
                    }
                }

                if (radioButton20.Checked)
                {
                    //выгрузить по фильтру
                    Tasks.DefaultView.RowFilter = "Id_TaskCP > 0 and DataBeg >= #" + Convert.ToDateTime(dateTimePicker6.Value.Date).ToString("MM.dd.yyyy hh:mm:ss") + "# and DataBeg <=#" + Convert.ToDateTime(dateTimePicker5.Value.AddDays(1).Date).ToString("MM.dd.yyyy hh:mm:ss") + "#";
                    if (Tasks.DefaultView.Count != 0)
                    {
                        int i;
                        i = 1;
                        DialogResult result;
                        result = MessageBox.Show("Импортировать ПЗ в АВИАБИТ", "Импорт", MessageBoxButtons.OKCancel);
                        if (result == DialogResult.OK)
                        {
                            progressBar2.Minimum = 0;
                            progressBar2.Maximum = Tasks.DefaultView.Count + 1;
                            progressBar2.Value = 1;
                            foreach (DataRowView Drow in Tasks.DefaultView)
                            {
                                progressBar2.Value = i++;
                                progressBar2.Refresh();
                                if (Flights.Rows.Count > 0)
                                    Flights.DefaultView.RowFilter = "IDTaskCP=" + Drow["ID_TaskCP"].ToString();
                                button6.UseWaitCursor = true;
                                InsertTaskAviabit();
                            }
                            Flights.DefaultView.RowFilter = "";
                            progressBar2.Value = 0;
                            progressBar2.Refresh();
                            button6.UseWaitCursor = false;
                            Tasks.DefaultView.RowFilter = "";
                        }
                    }

                }
                DeleteAllEmptyFlightTaskReestrs();
            }
        }

        private void radioButton15_CheckedChanged(object sender, EventArgs e)
        {
            StrConnect2 = StrConnectTTT; //// "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True";
        }

        private void radioButton16_CheckedChanged(object sender, EventArgs e)
        {
            StrConnect2 = StrConnectWWW;/// "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True";
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (Flights.Rows.Count > 0)
            {
                if (radioButton3.Checked)
                {
                    //выгрузить текущую
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать НАЗЕМКУ в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {
                        progressBar1.Maximum = 2;
                        progressBar1.Minimum = 0;
                        progressBar1.Value = 1;
                        progressBar1.Value = i++;
                        progressBar1.Refresh();
                        if (Flights.Rows.Count > 0)
                        {
                            Flights.DefaultView.RowFilter = "TypeFlight='PERSACT' and  Id_Flight = " + dataGridView7.Rows[dataGridView7.CurrentRow.Index].Cells["Id_Flight"].Value.ToString();
                            button3.UseWaitCursor = true;
                            InsertEventAviabit();
                        }
                        Flights.DefaultView.RowFilter = "";
                        progressBar1.Value = 0;
                        progressBar1.Refresh();
                        button3.UseWaitCursor = false;
                    }
                }

                if (radioButton2.Checked)
                {
                    //выгрузить по фильтру
                    DataView DTV_FL = new DataView(Flights);
                    DTV_FL.RowFilter = "TypeFlight='PERSACT' and  STA >= #" + Convert.ToDateTime(dateTimePicker1.Value).ToString("MM.dd.yyyy hh:mm:ss") + "# and STA <=#" + Convert.ToDateTime(dateTimePicker2.Value).ToString("MM.dd.yyyy hh:mm:ss") + "#";
                    if (DTV_FL.Count != 0)
                    {
                        int i;
                        i = 1;
                        DialogResult result;
                        result = MessageBox.Show("Импортировать НАЗЕМКУ в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);
                        if (result == DialogResult.OK)
                        {
                            progressBar1.Minimum = 0;
                            progressBar1.Maximum = DTV_FL.Count + 1;
                            progressBar1.Value = 1;
                            foreach (DataRowView Drow in DTV_FL)
                            {
                                progressBar1.Value = i++;
                                progressBar1.Refresh();
                                Flights.DefaultView.RowFilter = "TypeFlight = 'PERSACT' and  Id_Flight = " + Drow["Id_Flight"].ToString();
                                button3.UseWaitCursor = true;
                                InsertEventAviabit();
                            }
                            Flights.DefaultView.RowFilter = "";
                            DTV_FL.Dispose();
                            DTV_FL = null;
                            progressBar1.Value = 0;
                            progressBar1.Refresh();
                            button2.UseWaitCursor = false;
                        }
                    }
                }
            }
        }

        private void button7_Click(object sender, EventArgs e)
        {
            if (Flights.Rows.Count > 0)
            {
                if (radioButton2.Checked)
                {
                    //выгрузить по фильтру
                    Paring.DefaultView.RowFilter = "TypeParing='GRDUTY' and  DateBeg >= #" + Convert.ToDateTime(dateTimePicker1.Value).ToString("MM.dd.yyyy hh:mm:ss") + "# and DateBeg <=#" + Convert.ToDateTime(dateTimePicker2.Value).ToString("MM.dd.yyyy hh:mm:ss") + "#";
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать НАЗЕМКУ в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {
                        progressBar1.Maximum = Paring.DefaultView.Count + 1;
                        foreach (DataRowView Drow in Paring.DefaultView)
                        {
                            progressBar1.Value = i++;
                            if (Flights.Rows.Count > 0)
                                Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString();
                            button4.UseWaitCursor = true;
                            InsertEventAviabit();
                            Flights.DefaultView.RowFilter = "";
                        }
                        progressBar1.Value = 0;
                        button4.UseWaitCursor = false;
                    }
                    Paring.DefaultView.RowFilter = "";
                }

                if (radioButton3.Checked)
                {
                    //выгрузить текущую
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать НАЗЕМКУ JP в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {
                        progressBar1.Maximum = 1;
                        progressBar1.Value = i++;
                        if ((Flights.Rows.Count > 0) & (dataGridView2.Rows[dataGridView2.CurrentRow.Index].Cells["TypeParing"].Value.ToString() == "GRDUTY"))
                        {
                            Flights.DefaultView.RowFilter = "IdParing=" + dataGridView2.Rows[dataGridView2.CurrentRow.Index].Cells["Id_Paring"].Value.ToString();
                            button4.UseWaitCursor = true;
                            InsertEventAviabit();
                            Flights.DefaultView.RowFilter = "";
                        }
                        progressBar1.Value = 0;
                        button4.UseWaitCursor = false;
                    }
                }
            }
        }

        private void button8_Click(object sender, EventArgs e)
        {
            DateTime localDate = DateTime.Now;
            String Rem = localDate.ToShortDateString() + " " + localDate.ToShortTimeString();

            if (Flights.Rows.Count > 0)
            {

                //========================================================================================================
                //========================================================================================================
                if (radioButton2.Checked)
                {
                    //выгрузить по фильтру
                    //Paring.DefaultView.RowFilter = "TypeParing='FLIGHT' and DateBeg >= #" + Convert.ToDateTime(dateTimePicker1.Value).ToString("MM.dd.yyyy hh:mm:ss") + "# and DateBeg <=#" + Convert.ToDateTime(dateTimePicker2.Value).ToString("MM.dd.yyyy hh:mm:ss") + "#";
                    var fl = (from r in Flights.AsEnumerable()
                              join b in Paring.AsEnumerable() on r.Field<Int64>("IdParing") equals b.Field<Int64>("Id_Paring")
                              where
                              //(r.Field<string>("TypeFlight") == "L" || r.Field<string>("TypeFlight") == "D") &
                              b.Field<string>("TypeParing") == "FLIGHT" &
                              b.Field<DateTime>("DateBeg") >= dateTimePicker1.Value &
                              b.Field<DateTime>("DateBeg") <= dateTimePicker2.Value
                              group r by new { f1 = r["NumberFlight"], f2 = r["STD"] }
                              into nfl
                              select new { nfl.Key, nfl.Key.f1, nfl.Key.f2 }).ToList();

                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать Назначение JP в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {
                        progressBar1.Maximum = fl.Count + 1;   //Paring.DefaultView.Count + 1;
                        //foreach (DataRowView Drow in Paring.DefaultView)
                        //MessageBox.Show(fl.Count.ToString(),"", MessageBoxButtons.OK);
                        if (fl.Count > 0)
                        {  
                            foreach (var Drow in fl)
                            {
                                progressBar1.Value = i++;                           
                                if (radioButton4.Checked)
                                {
                                    //Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString(); // + " and TPCrew like('1/%')";
                                    Flights.DefaultView.RowFilter = "NumberFlight='" + Drow.f1.ToString()+"' and STD=#"+ Convert.ToDateTime(Drow.f2).ToString("MM.dd.yyyy HH:mm:ss")+"#"; // + " and TPCrew like('1/%')";
                                }
                                if (radioButton5.Checked)
                                {
                                    //Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString(); //+ " and TPCrew like('0/0/0/1/%')";
                                    Flights.DefaultView.RowFilter = "NumberFlight='" + Drow.f1.ToString() + "' and STD=#" + Convert.ToDateTime(Drow.f2).ToString("MM.dd.yyyy HH:mm:ss") + "#"; ; // + " and TPCrew like('1/%')";
                                }
                                button4.UseWaitCursor = true;
                                InsertAssignmentAviabit(Rem);
                                Flights.DefaultView.RowFilter = "";
                            }
                        }
                        progressBar1.Value = 0;
                        button4.UseWaitCursor = false;
                    }
                    Paring.DefaultView.RowFilter = "";
                }

                //========================================================================================================
                //========================================================================================================
                if (radioButton1.Checked)
                {
                    var fl = (from r in Flights.AsEnumerable()
                              join b in Paring.AsEnumerable() on r.Field<Int64>("IdParing") equals b.Field<Int64>("Id_Paring")
                              where
                              //(r.Field<string>("TypeFlight") == "L" || r.Field<string>("TypeFlight") == "D") &
                              b.Field<string>("TypeParing") == "FLIGHT" //&
                              ////b.Field<DateTime>("DateBeg") >= dateTimePicker1.Value &
                              ////b.Field<DateTime>("DateBeg") <= dateTimePicker2.Value
                              group r by new { f1 = r["NumberFlight"], f2 = r["STD"] }
                              into nfl
                              select new { nfl.Key, nfl.Key.f1, nfl.Key.f2 }).ToList();

                    //выгрузить все
                    //Paring.DefaultView.RowFilter = "TypeParing='FLIGHT'";
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать Назначение JP в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);
                    if (result == DialogResult.OK)
                    {                    
                        //MessageBox.Show(fl.Count.ToString(), "", MessageBoxButtons.OK);
                        if (fl.Count > 0)
                        {
                            progressBar1.Maximum = fl.Count + 1;
                            foreach (var Drow in fl)
                            {
                                progressBar1.Value = i++;
                                if (radioButton4.Checked)
                                {
                                    //Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString(); //+ " and TPCrew like('1/%')";
                                    Flights.DefaultView.RowFilter = "NumberFlight='" + Drow.f1.ToString() + "' and STD=#" + Convert.ToDateTime(Drow.f2).ToString("MM.dd.yyyy HH:mm:ss") + "#"; // + " and TPCrew like('1/%')";
                                }
                                if (radioButton5.Checked)
                                {
                                    //Flights.DefaultView.RowFilter = "IdParing=" + Drow["Id_Paring"].ToString(); //+ " and TPCrew like('0/0/0/1/%')";
                                    Flights.DefaultView.RowFilter = "NumberFlight='" + Drow.f1.ToString() + "' and STD=#" + Convert.ToDateTime(Drow.f2).ToString("MM.dd.yyyy HH:mm:ss") + "#"; ; // + " and TPCrew like('1/%')";
                                }
                                button4.UseWaitCursor = true;
                                InsertAssignmentAviabit(Rem);
                                Flights.DefaultView.RowFilter = "";
                            }
                            progressBar1.Value = 0;
                            button4.UseWaitCursor = false;
                        }
                    }
                    Paring.DefaultView.RowFilter = "";
                }

                //========================================================================================================
                //========================================================================================================
                if (radioButton3.Checked)
                {
                    //выгрузить текущую
                    Int64 Rec = (Int64)dataGridView2.Rows[dataGridView2.CurrentRow.Index].Cells["Id_Paring"].Value;
                    var fl = (from r in Flights.AsEnumerable()
                              join b in Paring.AsEnumerable() on r.Field<Int64>("IdParing") equals b.Field<Int64>("Id_Paring")
                              where
                              //(r.Field<string>("TypeFlight") == "L" || r.Field<string>("TypeFlight") == "D") &
                              b.Field<string>("TypeParing") == "FLIGHT" &
                              b.Field<Int64>("Id_Paring") == Rec
                              group r by new { f1 = r["NumberFlight"], f2 = r["STD"] }
                              into nfl
                              select new { nfl.Key, nfl.Key.f1, nfl.Key.f2 }).ToList();
                    int i;
                    i = 1;
                    DialogResult result;
                    result = MessageBox.Show("Импортировать Назначение JP в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);

                    if (result == DialogResult.OK)
                    {
                       // MessageBox.Show(fl.Count.ToString(), "", MessageBoxButtons.OK);
                        if (fl.Count > 0)
                        {
                            progressBar1.Maximum = fl.Count + 1;                          
                            foreach (var Drow in fl)
                            { 
                                if (radioButton4.Checked)
                                {
                                    //Flights.DefaultView.RowFilter = "IdParing=" + dataGridView2.Rows[dataGridView2.CurrentRow.Index].Cells["Id_Paring"].Value.ToString(); // + " and TPCrew like('1/%')";
                                    Flights.DefaultView.RowFilter = "NumberFlight='" + Drow.f1.ToString() + "' and STD=#" + Convert.ToDateTime(Drow.f2).ToString("MM.dd.yyyy HH:mm:ss") + "#"; //  ("MM.dd.yyyy hh:mm:ss") + " and TPCrew like('1/%')";
                                }
                                if (radioButton5.Checked)
                                {
                                    //Flights.DefaultView.RowFilter = "IdParing=" + dataGridView2.Rows[dataGridView2.CurrentRow.Index].Cells["Id_Paring"].Value.ToString(); // + " and TPCrew like('0/0/0/1/%')";
                                    Flights.DefaultView.RowFilter = "NumberFlight='" + Drow.f1.ToString() + "' and STD=#" + Convert.ToDateTime(Drow.f2).ToString("MM.dd.yyyy HH:mm:ss") + "#"; ; // + " and TPCrew like('1/%')";
                                }
                                button4.UseWaitCursor = true;
                                InsertAssignmentAviabit(Rem);
                                Flights.DefaultView.RowFilter = "";
                                progressBar1.Value = i++;
                            }
                            progressBar1.Value = 0;
                            button4.UseWaitCursor = false;
                            Flights.DefaultView.RowFilter = "";
                        }
                    }
                }
            }
        }


        private void dateTimePicker5_ValueChanged(object sender, EventArgs e)
        {
            DateTimePicker Dt = (DateTimePicker)sender;
            Dt.Value.AddHours(-Dt.Value.Hour);
            Dt.Value.AddMinutes(-Dt.Value.Minute);
            Dt.Value.AddSeconds(-Dt.Value.Second);
            Dt.Value.AddHours(23);
            Dt.Value.AddMinutes(59);
            Dt.Value.AddSeconds(0);
        }

        private void dateTimePicker6_Validated(object sender, EventArgs e)
        {
            DateTimePicker Dt = (DateTimePicker)sender;
            Dt.Value.AddHours(-Dt.Value.Hour);
            Dt.Value.AddMinutes(-Dt.Value.Minute);
            Dt.Value.AddSeconds(-Dt.Value.Second);
        }

        private void button9_Click(object sender, EventArgs e)
        {
            {
                if (Flights.Rows.Count > 0)
                {
                    if (radioButton3.Checked)
                    {
                        //выгрузить текущую
                        int i;
                        i = 1;
                        DialogResult result;
                        result = MessageBox.Show("Импортировать Crew BIds в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);
                        if (result == DialogResult.OK)
                        {
                            progressBar1.Maximum = 2;
                            progressBar1.Minimum = 0;
                            progressBar1.Value = 1;
                            progressBar1.Value = i++;
                            progressBar1.Refresh();
                            if (Flights.Rows.Count > 0)
                            {
                                Flights.DefaultView.RowFilter = "Bid";
                                button3.UseWaitCursor = true;
                                InsertCrewBidsAviabit();
                            }
                            Flights.DefaultView.RowFilter = "";
                            progressBar1.Value = 0;
                            progressBar1.Refresh();
                            button3.UseWaitCursor = false;
                        }
                    }

                    if (radioButton2.Checked)
                    {
                        //выгрузить по фильтру
                        DataView DTV_FL = new DataView(Flights);
                        DTV_FL.RowFilter = "Bid=1 and  STA >= #" + Convert.ToDateTime(dateTimePicker1.Value).ToString("MM.dd.yyyy hh:mm:ss") + "# and STA <=#" + Convert.ToDateTime(dateTimePicker2.Value).ToString("MM.dd.yyyy hh:mm:ss") + "#";
                        if (DTV_FL.Count != 0)
                        {
                            int i;
                            i = 1;
                            DialogResult result;
                            result = MessageBox.Show("Импортировать Crew BIds в АВИАБИТ - " + StrConnect, "Импорт", MessageBoxButtons.OKCancel);
                            if (result == DialogResult.OK)
                            {
                                progressBar1.Minimum = 0;
                                progressBar1.Maximum = DTV_FL.Count + 1;
                                progressBar1.Value = 1;
                                foreach (DataRowView Drow in DTV_FL)
                                {
                                    progressBar1.Value = i++;
                                    progressBar1.Refresh();
                                    Flights.DefaultView.RowFilter = "Bid=1 and  Id_Flight = " + Drow["Id_Flight"].ToString();
                                    button3.UseWaitCursor = true;
                                    InsertCrewBidsAviabit();
                                }
                                Flights.DefaultView.RowFilter = "";
                                DTV_FL.Dispose();
                                DTV_FL = null;
                                progressBar1.Value = 0;
                                progressBar1.Refresh();
                                button2.UseWaitCursor = false;
                            }
                        }
                    }
                }
            }
        }

    }
}