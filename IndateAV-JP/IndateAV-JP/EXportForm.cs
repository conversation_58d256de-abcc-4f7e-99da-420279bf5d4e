﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;

namespace IndateAV_JP
{
    public partial class EXportForm : Form
    {
        public EXportForm()
        {
            InitializeComponent();
            ////panel5.Visible = false;
            ////splitter2.Visible = false;
            StrConnect = "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True";
        }

        private void button1_Click(object sender, EventArgs e)
        {
            DialogResult result;
            DateTime DT_Begin=new DateTime(dateTimePicker1.Value.Year, dateTimePicker1.Value.Month, dateTimePicker1.Value.Day,0,0,0);
            DateTime DT_End= new DateTime(dateTimePicker2.Value.Year, dateTimePicker2.Value.Month, dateTimePicker2.Value.Day, 23, 59, 59);
            System.Data.SqlClient.SqlDataReader DTrdr;
            //IDataReader DTrdr;

            result = MessageBox.Show("Экспортировать Полетные задания в CTF", "Export", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                this.tabControl2.SelectedTab = tabControl2.TabPages[0];
                this.Cursor = Cursors.WaitCursor;
                DTCTFlines.Clear();
                DTCTFverifix.Clear();
                DTSExport.Tables.Clear();
                bindingSource1.DataSource = null;
                bindingNavigator1.BindingSource = null;
                bindingSource2.DataSource = null;
                bindingNavigator2.BindingSource = null;
                dataGridView1.DataSource = null;
                dataGridView2.DataSource = null;
                dataGridView1.Refresh();
                dataGridView2.Refresh();
                richTextBox1.Text = "";
                //==================================
                String StrCTF = "";
                //==========================================
                System.Data.SqlClient.SqlDataAdapter ADp = new System.Data.SqlClient.SqlDataAdapter();
                String ConStr;
                System.Data.SqlClient.SqlConnection Conect;
                System.Data.SqlClient.SqlCommand Command;
                //=========================================

                //ConStr = "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True"; ///боевая
                //ConStr = "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True"; ///Тестовая
                ConStr = StrConnect;
                Conect = new System.Data.SqlClient.SqlConnection(ConStr);
                Command = Conect.CreateCommand();

                if (this.radioButton6.Checked) Command.CommandText = "PD_JPR_ImportCTF_SectionPAiring_Export_Crew_ALL_2"; //"PD_JPR_ImportCTF_SectionPAiring_Export";
                else
                if (this.radioButton7.Checked) Command.CommandText = "PD_JPR_ImportCTF_SectionPAiring_Export_Crew_ALL_2";   //"PD_JPR_ImportCTF_SectionPAiring_Export_Crew_ALL"

                Command.CommandType = CommandType.StoredProcedure;
                Command.Parameters.Add("@DTbegin", SqlDbType.DateTime);
                Command.Parameters.Add("@DTEnd", SqlDbType.DateTime);
                Command.Parameters.Add("@TYPECREW", SqlDbType.Int); //@Parts
                Command.Parameters.Add("@Parts", SqlDbType.Int); //@Parts
                Command.Parameters.Add("@Sourth", SqlDbType.Int); //@Parts


                if (this.radioButton7.Checked) Command.Parameters.Add("@CRW", SqlDbType.Int); //@CRW
                if (this.radioButton6.Checked) Command.Parameters.Add("@CRW", SqlDbType.Int); //@CRW

                Conect.Open();
                Command.Parameters["@DTbegin"].Value = DT_Begin;
                Command.Parameters["@DTEnd"].Value = DT_End;

                if (this.radioButton1.Checked)  Command.Parameters["@TYPECREW"].Value = 0;
                if (this.radioButton2.Checked)  Command.Parameters["@TYPECREW"].Value = 1;
                if (this.radioButton11.Checked) Command.Parameters["@TYPECREW"].Value = 11;

                if (this.radioButton7.Checked) Command.Parameters["@CRW"].Value = 1; 
                if (this.radioButton6.Checked) Command.Parameters["@CRW"].Value = 0;
                                  
                if (radioButton3.Checked)
                {
                  Command.Parameters["@Parts"].Value = 0;
                  Command.Parameters["@Sourth"].Value = 0;
                }
                else if (radioButton4.Checked)
                {   Command.Parameters["@Parts"].Value = 1;
                    Command.Parameters["@Sourth"].Value = 0;
                }
                else if (radioButton8.Checked)
                {
                    Command.Parameters["@Parts"].Value = 1;
                    Command.Parameters["@Sourth"].Value = 1;
                }
                else if (radioButton5.Checked)
                {
                    Command.Parameters["@Parts"].Value = 2;
                    Command.Parameters["@Sourth"].Value = 0;
                }
                   
                Command.CommandTimeout = 300000000;
                DTrdr = Command.ExecuteReader();

                DTSExport.Tables.Add("DTCTFlines");
                DTSExport.Tables.Add("DTCTFverifix");

                if (DTrdr.HasRows)
                {
                    DTSExport.Tables["DTCTFlines"].Load(DTrdr, LoadOption.OverwriteChanges);
                    DTSExport.Tables["DTCTFverifix"].Load(DTrdr, LoadOption.OverwriteChanges);
                }
                    DTCTFlines = DTSExport.Tables["DTCTFlines"];
                    bindingSource1.DataSource = DTCTFlines;
                    dataGridView1.DataSource = bindingSource1;
                    bindingNavigator1.BindingSource = bindingSource1;
                    dataGridView1.Columns[0].SortMode = DataGridViewColumnSortMode.Automatic;
                    dataGridView1.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCellsExceptHeader);
                
                    DTSExport.Tables["DTCTFverifix"].Load(DTrdr, LoadOption.OverwriteChanges);
                    DTCTFverifix = DTSExport.Tables["DTCTFverifix"];
                    bindingSource2.DataSource = DTCTFverifix;
                    dataGridView2.DataSource = bindingSource2;
                    bindingNavigator2.BindingSource = bindingSource2;
                    dataGridView2.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);
                    dataGridView2.Columns[5].Visible = false;
                    dataGridView2.Columns[6].Visible = false;

                //ADp.SelectCommand = Command;
                //ADp.ReturnProviderSpecificTypes = true;
                //ADp.Fill(DTSExport);


                foreach (DataRow VR in DTCTFlines.Rows)
                {
                   
                    //StrCTF = StrCTF + VR.Field<String>(0) + "\r\n";
                    StrCTF = StrCTF + Convert.ToString(VR[0].ToString()) + "\r\n";
                }
                this.richTextBox1.Text = StrCTF;
                DTrdr.Close();
                ADp.Dispose();
                Command.Dispose();
                Conect.Close();
                Conect.Dispose();
                this.Cursor = Cursors.Default;
            }
        }

        private void button3_Click(object sender, EventArgs e)
        {
            Encoding utf8 = new UTF8Encoding(false);
            string TypeCrew = "";
            //===================
            this.Cursor = Cursors.WaitCursor;
            if (this.radioButton1.Checked)
            {
                TypeCrew = "_FD_";
            }
            else
            {
                TypeCrew = "_CC_";
            }
            string fileName = "Pairing_" + TypeCrew + this.dateTimePicker1.Value.ToShortDateString() + "_" + this.dateTimePicker2.Value.ToShortDateString() + ".ctf";
            saveFileDialog1.FileName = fileName;
            saveFileDialog1.Filter = "Файлы CTF (*.ctf)|";
            if (radioButton7.Checked)
            {
                fileName = "Rostering_" + TypeCrew + this.dateTimePicker1.Value.ToShortDateString() + "_" + this.dateTimePicker2.Value.ToShortDateString() + ".ctf";
                saveFileDialog1.FileName = fileName;
            }

            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                if (saveFileDialog1.FileName != null)
                {
                    File.WriteAllText(saveFileDialog1.FileName, richTextBox1.Text, utf8);
                }
            }
            this.Cursor = Cursors.Default;
        }

        private void button2_Click(object sender, EventArgs e)
        {
            DialogResult result;
            result = MessageBox.Show("Экспортировать Полетные задания в CTF", "Export", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                this.tabControl2.SelectedTab = tabControl2.TabPages[1];
                this.Cursor = Cursors.WaitCursor;
                DTEtab.Clear();
                dataGridView3.DataSource = null;
                dataGridView3.Refresh();
                richTextBox2.Text = "";
                //==================================
                String StrEtab = "";
                //==========================================
                System.Data.SqlClient.SqlDataAdapter ADp = new System.Data.SqlClient.SqlDataAdapter();
                String ConStr;
                System.Data.SqlClient.SqlConnection Conect;
                System.Data.SqlClient.SqlCommand Command;
                //=========================================

                //ConStr = "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True";
                ConStr = StrConnect;
                Conect = new System.Data.SqlClient.SqlConnection(ConStr);
                Command = Conect.CreateCommand();
                Command.CommandText = "PD_JPR_ImportEtab_TurnoverAviaBoard";
                Command.CommandType = CommandType.StoredProcedure;
                Command.Parameters.Add("@DTbegin", SqlDbType.DateTime);
                Command.Parameters.Add("@DTEnd", SqlDbType.DateTime);
                Conect.Open();
                Command.Parameters["@DTbegin"].Value = dateTimePicker1.Value;
                Command.Parameters["@DTEnd"].Value = dateTimePicker2.Value;
                Command.CommandTimeout = 3000;
                ADp.SelectCommand = Command;
                ADp.Fill(DTEtab);
                dataGridView3.DataSource = DTEtab;
                dataGridView3.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);

                foreach (DataRow VR in DTEtab.Rows)
                {
                    StrEtab = StrEtab + VR.Field<String>(1) + "\r\n";
                }
                this.richTextBox2.Text = StrEtab;
                //====================================
                ADp.Dispose();
                Command.Dispose();
                Conect.Close();
                Conect.Dispose();
                this.Cursor = Cursors.Default;
            }
        }

        private void button4_Click(object sender, EventArgs e)
        {
            Encoding utf8 = new UTF8Encoding(false);
            //===================
            this.Cursor = Cursors.WaitCursor;
 
            string fileName = "ac_rotations_" + this.dateTimePicker1.Value.ToShortDateString() + "_" + this.dateTimePicker2.Value.ToShortDateString() + ".etab";
            saveFileDialog1.FileName = fileName;
            saveFileDialog1.Filter = "Таблицы Etab (*.etab)|";
            

            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                if (saveFileDialog1.FileName != null)
                {
                    File.WriteAllText(saveFileDialog1.FileName, richTextBox2.Text, utf8);
                }
            }
            this.Cursor = Cursors.Default;
        }

        private void copySelectedRowsToClipboard(DataGridView dgv)
        {
            dgv.ClipboardCopyMode = DataGridViewClipboardCopyMode.EnableWithAutoHeaderText;
            Clipboard.Clear();
            if (dgv.GetClipboardContent() != null)
            {
                Clipboard.SetDataObject(dgv.GetClipboardContent());
                Clipboard.GetData(DataFormats.Text);
                IDataObject dt = Clipboard.GetDataObject();
                if (dt.GetDataPresent(typeof(string)))
                {
                    string tb = (string)(dt.GetData(typeof(string)));
                    Encoding encoding = Encoding.GetEncoding(1251);
                    byte[] dataStr = encoding.GetBytes(tb);
                    Clipboard.SetDataObject(encoding.GetString(dataStr));
                }
            }
            dgv.ClipboardCopyMode = DataGridViewClipboardCopyMode.Disable;
        }

        private void dataGridView2_PreviewKeyDown(object sender, PreviewKeyDownEventArgs e)
        {
           if (e.Control && e.KeyCode == Keys.C)
                if (((DataGridView)sender).SelectedCells.Count > 0)
                {
                    copySelectedRowsToClipboard((DataGridView)sender);
                }
        }
        private void dataGridView1_PreviewKeyDown(object sender, PreviewKeyDownEventArgs e)
        {
            if (e.Control && e.KeyCode == Keys.C)
                if (((DataGridView)sender).SelectedCells.Count > 0)
                {
                    copySelectedRowsToClipboard((DataGridView)sender);
                }
        }

        private void toolStripButton1_Click(object sender, EventArgs e)
        {
            try
            {
                DTCTFlines.DefaultView.RowFilter = "STR1 like '%" + toolStripTextBox1.Text + "%'" ; 
            }
            catch(Exception)
            {

            }
        }

        private void toolStripButton2_Click(object sender, EventArgs e)
        {
            try
            {
                DTCTFlines.DefaultView.RowFilter = "";
                toolStripTextBox1.Text = "";
            }
            catch (Exception)
            {

            }
        }

        private void radioButton9_CheckedChanged(object sender, EventArgs e)
        {
            StrConnect = "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True";
        }

        private void radioButton10_CheckedChanged(object sender, EventArgs e)
        {
            StrConnect = "Data Source=AeroAS03;Initial Catalog=avia_gplr_2;Integrated Security=True";
        }

        private void toolStripTextBox2_Click(object sender, EventArgs e)
        {

        }

        private void toolStripButton3_Click(object sender, EventArgs e)
        {
            int n;
            n=this.richTextBox1.Find(this.toolStripTextBox1.Text, Nstr, this.toolStripTextBox1.Text.Length-1,RichTextBoxFinds.None);

            if (n > 0)
            {
                Nstr = n;
                this.richTextBox1.Select(Nstr, this.toolStripTextBox1.Text.Length);
            }
        }

        private void richTextBox1_Enter(object sender, EventArgs e)
        {
            Nstr = 1;
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }
    }
}
