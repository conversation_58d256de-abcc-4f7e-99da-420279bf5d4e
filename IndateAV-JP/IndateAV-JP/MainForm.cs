﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IndateAV_JP
{
    public partial class MainForm : Form
    {
        /// <summary>
        /// ///crew planning data adapter CPDA
        /// </summary>
        public MainForm()
        {
            InitializeComponent();
        }



        private void OpenFile(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();
            openFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
            openFileDialog.Filter = "Текстовые файлы (*.txt)|*.txt|Все файлы (*.*)|*.*";
            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                string FileName = openFileDialog.FileName;
            }
        }

        private void SaveAsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
            saveFileDialog.Filter = "Текстовые файлы (*.txt)|*.txt|Все файлы (*.*)|*.*";
            if (saveFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                string FileName = saveFileDialog.FileName;
            }
        }

        private void ExitToolsStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void CutToolStripMenuItem_Click(object sender, EventArgs e)
        {
        }

        private void CopyToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (this.ActiveMdiChild != null)
            {
                Form Fm = this.ActiveMdiChild;
                if (Fm.ActiveControl.GetType() == typeof(DataGridView))
                {
                    copySelectedRowsToClipboard((DataGridView)Fm.ActiveControl);
                }              
                if (Fm.ActiveControl.GetType() == typeof(RichTextBox))
                {
                    RichTextBox edit = (RichTextBox)Fm.ActiveControl;
                    Clipboard.SetDataObject(edit.SelectedText);
                }

            }
        }
        private void copySelectedRowsToClipboard(DataGridView dgv)
        {
            dgv.ClipboardCopyMode = DataGridViewClipboardCopyMode.EnableWithAutoHeaderText;
            Clipboard.Clear();
            if (dgv.GetClipboardContent() != null)
            {
                Clipboard.SetDataObject(dgv.GetClipboardContent());
                Clipboard.GetData(DataFormats.Text);
                IDataObject dt = Clipboard.GetDataObject();
                if (dt.GetDataPresent(typeof(string)))
                {
                    string tb = (string)(dt.GetData(typeof(string)));
                    Encoding encoding = Encoding.GetEncoding(1251);
                    byte[] dataStr = encoding.GetBytes(tb);
                    Clipboard.SetDataObject(encoding.GetString(dataStr));
                }
            }
            dgv.ClipboardCopyMode = DataGridViewClipboardCopyMode.Disable;
        }

        private void ImportExcelFun()
        {
            string fileName = null;
            openFileDialog1.Filter = "Файлы Excel 2003|*.xls|Файлы Excel 2010|*.xlsx|Файлы CSV|*.CSV";
            openFileDialog1.FileName = "";

            if (openFileDialog1.ShowDialog() == DialogResult.OK)
            {
                fileName = openFileDialog1.FileName;
            }
            if (fileName != null)
            {
                ExcelImport ImportExcel = new ExcelImport(fileName);
                ImportExcel.MdiParent = this;
                ImportExcel.Show();
            }
        }
        private void PasteToolStripMenuItem_Click(object sender, EventArgs e)
        {
        }

        private void ToolBarToolStripMenuItem_Click(object sender, EventArgs e)
        {
            toolStrip.Visible = toolBarToolStripMenuItem.Checked;
        }

        private void StatusBarToolStripMenuItem_Click(object sender, EventArgs e)
        {
            statusStrip.Visible = statusBarToolStripMenuItem.Checked;
        }

        private void CascadeToolStripMenuItem_Click(object sender, EventArgs e)
        {
            LayoutMdi(MdiLayout.Cascade);
        }

        private void TileVerticalToolStripMenuItem_Click(object sender, EventArgs e)
        {
            LayoutMdi(MdiLayout.TileVertical);
        }

        private void TileHorizontalToolStripMenuItem_Click(object sender, EventArgs e)
        {
            LayoutMdi(MdiLayout.TileHorizontal);
        }

        private void ArrangeIconsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            LayoutMdi(MdiLayout.ArrangeIcons);
        }

        private void CloseAllToolStripMenuItem_Click(object sender, EventArgs e)
        {
            foreach (Form childForm in MdiChildren)
            {
                childForm.Close();
            }
        }

        private void toolStripMenuItem1_Click(object sender, EventArgs e)
        {
            EXportForm ExportForm = new EXportForm();
            ExportForm.MdiParent = this;
            ExportForm.Show();
        }

        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            IMportForm ImportForm = new IMportForm();

            ImportForm.MdiParent = this;
            ImportForm.Show();
        }

        private void importFileDataToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ImportExcelFun();
        }

        private void exportEtabToolStripMenuItem_Click(object sender, EventArgs e)
        {

            EtabExport EtabForm = new EtabExport();
                        EtabForm.MdiParent = this;
                        EtabForm.Show();
        }

        private void toolStripButton1_Click(object sender, EventArgs e)
        {
            EXportForm ExportForm = new EXportForm();
            ExportForm.MdiParent = this;
            ExportForm.Show();
        }

        private void toolStripButton2_Click(object sender, EventArgs e)
        {
            IMportForm ImportForm = new IMportForm();
            ImportForm.MdiParent = this;
            ImportForm.Show();
        }

        private void toolStripButton4_Click(object sender, EventArgs e)
        {
            EtabExport EtabForm = new EtabExport();
            EtabForm.MdiParent = this;
            EtabForm.Show();
        }

        private void toolStripButton5_Click(object sender, EventArgs e)
        {
            ImportExcelFun();
        }
    }
}
