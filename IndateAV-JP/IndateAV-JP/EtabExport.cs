﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;

namespace IndateAV_JP
{
    public partial class EtabExport : Form
    {
        public EtabExport()
        {
            InitializeComponent();
            panel5.Visible = false;
            splitter2.Visible = false;
            panel6.Dock = DockStyle.Fill;
            DTSExport = new DataSet();
            StrConnect = "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True";
            //------------------------------------------------------------------------------------------
            InsertNewEtab("crew_info",                  "PD_JPR_ExportEtab_Crew_info");
            InsertNewEtab("crew_employment",            "PD_JPR_ExportEtab_Crew_employment");
            InsertNewEtab("crew_qualifications",        "PD_JPR_ExportEtab_Crew_qualifications_MOD1");
            InsertNewEtab("crew_restrictions",          "PD_JPR_ExportEtab_Crew_restrictions");
            InsertNewEtab("historical_block_time",      "PD_JPR_ExportEtab_historical_block_time");
            InsertNewEtab("historical_duty_time",       "PD_JPR_ExportEtab_historical_duty_time");
            InsertNewEtab("recency_crew_history",       "PD_JPR_ExportEtab_Recency_crew_history");
            InsertNewEtab("recurrent_crew_expiry",      "PD_JPR_ExportEtab_Recurrent_crew_expiry");
            InsertNewEtab("qualification_restrictions", "PD_JPR_ExportEtab_Qualification_restrication");
            InsertNewEtab("crew_not_fly_with",          "PD_JPR_ExportEtab_Crew_not_fly_with");
            InsertNewEtab("recurrent_crew_expiry_modific2", "PD_JPR_ExportEtab_Recurrent_crew_expiry_mod2");
            
            //InsertNewEtab("New_crew_qualifications_TEST", "PD_JPR_ExportEtab_Crew_qualifications_MOD1");


            //InsertNewEtab("Special_schedule_crew", "");
            //InsertNewEtab("Hotel", "");
            //InsertNewEtab("Recurrent_types", "");
            //InsertNewEtab("Travel_document_restrictions", "");
            //---------------------------------------------------------------
            this.dataGridView1.DataSource = NameEtab.Tables["NameEtab"];
           
        }
         private void InsertNewEtab(string Value, string NameProcess)
        {
            DataTable NmDT = NameEtab.Tables["NameEtab"];
            DataRow DR = NmDT.NewRow();
            DR.BeginEdit();
            DR.SetField("NameTable", Value);
            DR.SetField("NameProcess", NameProcess);
            DR.SetField("LoadData", false);
            DR.SetField("SaveData", false);
            DR.SetField("TextEtab", "NO DATA");
            DR.SetField("MarkData", false);
            DR.EndEdit();
            NmDT.Rows.Add(DR);
        }
        private void dataGridView1_RowEnter(object sender, DataGridViewCellEventArgs e)
        {

            string Nmf = dataGridView1.Rows[e.RowIndex].Cells["NameTable"].Value.ToString();
            if (DTSExport.Tables.IndexOf(Nmf) >= 0)
            { 
                bindingSource1.DataSource = DTSExport.Tables[Nmf];
                bindingNavigator1.BindingSource = bindingSource1;
                this.dataGridView2.DataSource = bindingSource1;
                this.richTextBox1.Text = dataGridView1.Rows[e.RowIndex].Cells["TextEtab"].Value.ToString();
                dataGridView2.Columns["STRLN"].Visible = false;
                try
                {
                    DTSExport.Tables[Nmf].DefaultView.RowFilter = "[TableNumber] <>''";
                }
                catch
                {
                    DTSExport.Tables[Nmf].DefaultView.RowFilter = "[TableNumber1] <>''";
                }

                

            }
            else
            {
                this.bindingSource1.DataSource = null;
                bindingNavigator1.BindingSource = null;
                this.dataGridView2.DataSource = null;
                this.richTextBox1.Text = "";
            }
        }

 
        private void выбратьВСЕToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DataTable NmDT = NameEtab.Tables["NameEtab"];
            foreach (DataRow dr in NmDT.Rows)
            {
                dr.BeginEdit();
                dr.SetField("MarkData", true);
                dr.EndEdit();
            }
        }

        private void отменитьВСЕToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DataTable NmDT = NameEtab.Tables["NameEtab"];
            foreach (DataRow dr in NmDT.Rows)
            {
                dr.BeginEdit();
                dr.SetField("MarkData", false);
                dr.EndEdit();
            }
        }

        private void загрузитьДанныеETABsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DateTime DT_Begin = new DateTime(dateTimePicker1.Value.Year, dateTimePicker1.Value.Month, dateTimePicker1.Value.Day, 0, 0, 0);
            DateTime DT_End = new DateTime(dateTimePicker2.Value.Year, dateTimePicker2.Value.Month, dateTimePicker2.Value.Day, 23, 59, 59);

            DialogResult result;
            string StrCTF = "";
            System.Data.SqlClient.SqlConnection Conect = new System.Data.SqlClient.SqlConnection(StrConnect);
            Conect.Open();
            System.Data.SqlClient.SqlDataAdapter ADP = new System.Data.SqlClient.SqlDataAdapter();
            System.Data.SqlClient.SqlCommand Command = Conect.CreateCommand();
            Command.Parameters.Add("@DTbegin", SqlDbType.DateTime);
            Command.Parameters.Add("@DTEnd", SqlDbType.DateTime);
            StrConnect = "Data Source=AeroDB03;Initial Catalog=aviacompany;Integrated Security=True";

            result = MessageBox.Show("Экспортировать Данные", "Export", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                DataTable NmDT = NameEtab.Tables["NameEtab"];
                foreach (DataRow dr in NmDT.Rows)
                {
                    if (dr.Field<string>("NameProcess") != "" & dr.Field<Boolean>("MarkData"))
                    {
                        Command.CommandText = dr.Field<string>("NameProcess");
                        Command.CommandType = CommandType.StoredProcedure;
                        Command.CommandTimeout = 3000;
                        Command.Parameters["@DTbegin"].Value = DT_Begin;
                        Command.Parameters["@DTEnd"].Value = DT_End;

                        ADP.SelectCommand = Command;
                        if (DTSExport.Tables.IndexOf(dr.Field<string>("NameTable")) >= 0)
                        {
                            ADP.Fill(DTSExport.Tables[dr.Field<string>("NameTable")]);
                        }
                        else
                        {
                            DTSExport.Tables.Add(dr.Field<string>("NameTable"));
                            ADP.Fill(DTSExport.Tables[dr.Field<string>("NameTable")]);
                        }
                        //--------------------------------------------------
                        DataTable TBL = DTSExport.Tables[dr.Field<string>("NameTable")];
                        StrCTF = "";
                        foreach (DataRow VR in TBL.Rows)
                        {
                            StrCTF = StrCTF + VR.Field<String>("STRLN") + "\n";
                        }
                        dr.BeginEdit();
                        dr.SetField("TextEtab", StrCTF);
                        dr.SetField("MarkData", false);
                        dr.EndEdit();
                    }
                }
                CP_DTSExport= DTSExport.Copy();
            }
            ADP.Dispose();
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
        }
        private void сохранитьETABsВПапкеToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Encoding utf8 = new UTF8Encoding(false);

            if (folderBrowserDialog1.ShowDialog() == DialogResult.OK)
            {
                if (folderBrowserDialog1.SelectedPath != null)
                {
                    Cursor = Cursors.WaitCursor;
                    DataTable NmDT = NameEtab.Tables["NameEtab"];
                    foreach (DataRow dr in NmDT.Rows)
                    {
                        if (dr.Field<string>("NameProcess") != "" & dr.Field<Boolean>("MarkData"))
                        {
                            File.WriteAllText(folderBrowserDialog1.SelectedPath + "\\" + dr.Field<string>("NameTable") + ".etab", dr.Field<string>("TextEtab"), utf8);
                            dr.BeginEdit();
                            if (dr.Field<Boolean>("MarkData")) dr.SetField("MarkData", false);
                            else dr.SetField("MarkData", true);
                            dr.EndEdit();
                        }
                    }
                }
            }
            Cursor = Cursors.Default;
        }

        private void toolStripSplitButton1_ButtonClick(object sender, EventArgs e)
        {
            toolStripSplitButton1.ShowDropDown();
        }

        private void toolStripSplitButton2_ButtonClick(object sender, EventArgs e)
        {
            toolStripSplitButton2.ShowDropDown();
        }

        private void toolStripButton1_Click(object sender, EventArgs e)
        {
            if (!panel5.Visible)
            {
                panel6.Dock = DockStyle.Top;
                panel5.Visible = true;
                splitter2.Visible = true;

            }
            else
            {
                panel6.Dock = DockStyle.Fill;
                panel5.Visible = false;
                splitter2.Visible = false;
            }
        }

    }
}
