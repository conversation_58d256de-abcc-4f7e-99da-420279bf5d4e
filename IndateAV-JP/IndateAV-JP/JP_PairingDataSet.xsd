﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="JP_PairingDataSet" targetNamespace="http://tempuri.org/JP_PairingDataSet.xsd" xmlns:mstns="http://tempuri.org/JP_PairingDataSet.xsd" xmlns="http://tempuri.org/JP_PairingDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="JP_PairingConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="JP_PairingConnectionString (Settings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.IndateAV_JP.Properties.Settings.GlobalReference.Default.JP_PairingConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="JP_PairingDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="True" msprop:Generator_DataSetName="JP_PairingDataSet" msprop:Generator_UserDSName="JP_PairingDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded" />
    </xs:complexType>
  </xs:element>
</xs:schema>