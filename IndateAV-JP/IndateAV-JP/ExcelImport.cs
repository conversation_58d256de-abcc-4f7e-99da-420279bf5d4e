﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.OleDb;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Xml;

namespace IndateAV_JP
{
    public partial class ExcelImport : Form
    {
        private string FileName = null;
        private DataSet ds = new DataSet();
        private FileInfo FInfo;

        public ExcelImport()
        {
            InitializeComponent();
        }

        public ExcelImport(string FileNameInput)
        {
            InitializeComponent();
            FileName = FileNameInput;
            this.Text = "Загруженные данные из - " + FileName;
            FInfo = new FileInfo(FileName);


            if (FInfo.Extension == ".xls")
            {
                ////var connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + fileName + ";Extended Properties=\"Excel 12.0;IMEX=1;HDR=NO;TypeGuessRows=0;ImportMixedTypes=Text\""; ;
                var OleDbcon = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + FileName + "; Extended Properties = Excel 8.0; ");

                using (var conn = OleDbcon)
                {

                    try
                    {
                        conn.Open();
                        var sheets = conn.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = "SELECT * FROM [" + sheets.Rows[0]["TABLE_NAME"].ToString() + "] ";

                            var adapter = new OleDbDataAdapter(cmd);

                            adapter.Fill(ds);
                            bindingSource1.DataSource = ds.Tables[0];
                            bindingNavigator1.BindingSource = bindingSource1;
                            dataGridView1.DataSource = bindingSource1;
                            string flt="["+ds.Tables[0].Columns[0].Caption.ToString() + "] >'' ";
                            ds.Tables[0].DefaultView.RowFilter = flt;
                        }
                   }
                    catch (Exception e)
                    {
                        MessageBox.Show(e.Message, "Import from file", MessageBoxButtons.OK);
                    }

                }
            }

            if (FInfo.Extension == ".csv")
            {
                var OleDbcon1 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Extended Properties='text;HDR=YES;FMT=Delimited(;)';Data Source=" + FInfo.DirectoryName + ";");  //;HDR=Yes;FMT=Delimited(;)'

                using (var conn1 = OleDbcon1)
                {
                    conn1.Open();

                    using (var cmd = conn1.CreateCommand())
                    {
                        var ad = new OleDbDataAdapter("SELECT * FROM " + FInfo.Name + " ", conn1);
                        ad.Fill(ds);
                        dataGridView1.DataSource = ds.Tables[0];

                        bindingSource1.DataSource = ds.Tables[0];
                        bindingNavigator1.BindingSource = bindingSource1;
                        dataGridView1.DataSource = bindingSource1;
                    }
                }
            }

        }

        private void toolStripButton1_Click(object sender, EventArgs e)
        {
            String ConStr;
            
            StringWriter writerFL = new StringWriter();
            StringWriter writerXSD;
            string xmlfile;
            bool Fl = true;
            
            //==================================
            ConStr = "Data Source = AeroDB03; Initial Catalog = aviacompany ; Integrated Security = True"; //aviacompany
            //ConStr = "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True";
            //Data Source = AeroDB03; Initial Catalog = aviacompany; Integrated Security = True
            //ConStr = "Data Source=AeroAS03;Initial Catalog=JP_Pairing;Integrated Security=True";

            //==========================================================
            ds.Tables[0].WriteXmlSchema(writerFL, true);
            ds.Tables[0].WriteXmlSchema("ffff.xsd");

            //===================
            DataTable newTable = new DataTable();
            xmlfile = "EventsAV.xsd";
            newTable.ReadXmlSchema(xmlfile);
            writerXSD = new StringWriter();
            newTable.WriteXmlSchema(writerXSD, true);
            if (Fl & writerFL.ToString() == writerXSD.ToString())
            {
                GoExccelOutputEventAV(ConStr, ds);
                Fl = false;
            }

            //===============================
            newTable = new DataTable();
            xmlfile = "FormsAV.xsd";
            newTable.ReadXmlSchema(xmlfile);
            writerXSD = new StringWriter();
            newTable.WriteXmlSchema(writerXSD, true);
            
            if (Fl &  writerFL.ToString() == writerXSD.ToString())
            {
                GoExccelOutputForm(ConStr, ds);
                Fl = false;
            }
            //=================================

            newTable.Dispose();
        }

        private void GoExccelOutputEventAV(String ConStr, DataSet dsv)
        {
            //==============================================Начало процесса===========================================
            //==========================================================================================================
            DataTable Dt = new DataTable();
            DialogResult result;
            int i = 1;
            //=====================================
            StringBuilder ST;
            String XMLStr;
            String DataXMLStr = "";
            System.Data.SqlClient.SqlConnection Conect;
            System.Data.SqlClient.SqlCommand Command;
            //=========================================

            Conect = new System.Data.SqlClient.SqlConnection(ConStr);
            Command = Conect.CreateCommand();
            Command.CommandText = "PD_JPR_InsertEvent";
            Command.CommandType = CommandType.StoredProcedure;
            Command.Parameters.Add("@STRXML", SqlDbType.NVarChar);
            Conect.Open();
            //==============================================
            Dt = dsv.Tables[0].Copy();
            Dt.Rows.Clear();
            //==============================================

            foreach (DataColumn Clm in dsv.Tables[0].Columns)
            {
                if (Clm.DataType == Type.GetType("System.DataTime"))
                {
                    Clm.DateTimeMode = DataSetDateTime.Utc;
                }
            }
            //==============================================Обработка таблицы===========================================
            //==========================================================================================================
            result = MessageBox.Show(this.Owner, "Импортировать события в АВИАБИТ", "Импорт", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                toolStripProgressBar1.Maximum = ds.Tables[0].Rows.Count + 1;
                foreach (DataRow Drow in ds.Tables[0].Rows)
                {
                    toolStripProgressBar1.Value = i++;
                    this.UseWaitCursor = true;
                    //======================================
                    Dt.Rows.Clear();
                    Dt.ImportRow(Drow);
                    //Dt.WriteXml
                    XMLStr = "";
                    XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder(), new XmlWriterSettings
                    {
                        ConformanceLevel = ConformanceLevel.Auto,
                    });
                    Dt.WriteXml(XMLWR);
                    XMLStr = ST.ToString();
                    int Nbeg = XMLStr.IndexOf("<Table>");
                    int NEnd = XMLStr.IndexOf("</DocumentElement>");
                    if (Nbeg > 0)
                    {
                        DataXMLStr = XMLStr.Substring(Nbeg, NEnd - Nbeg);
                    }
                    //File.WriteAllText("rrrrr.xml", DataXMLStr);
                    Command.Parameters["@STRXML"].Value = DataXMLStr;
                    Command.ExecuteNonQuery();
                    //====================================
                    this.UseWaitCursor = false;
                }
            }
            //==============================================Завершение таблицы===========================================
            //==========================================================================================================
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
            //==============================================
            Dt.Clear();
            Dt.Dispose();
            //==============================================End процесса===========================================
            //==========================================================================================================
        }

        private void GoExccelOutputForm(String ConStr, DataSet dsv)
        {
            DataTable Dt=new DataTable();
            DialogResult result;
            int i = 1;
            //=====================================
            StringBuilder ST;
            String XMLStr;
            String DataXMLStr = "";
            System.Data.SqlClient.SqlConnection Conect;
            System.Data.SqlClient.SqlCommand Command;
            //=========================================

            Conect = new System.Data.SqlClient.SqlConnection(ConStr);
            Command = Conect.CreateCommand();
            Command.CommandText = "PD_JPR_InsertUniForms";
            Command.CommandType = CommandType.StoredProcedure;
            Command.Parameters.Add("@STRXML", SqlDbType.NVarChar);
            Conect.Open();
            //==============================================
            Dt = dsv.Tables[0].Copy();
            Dt.Rows.Clear();
            //==============================================

            foreach (DataColumn Clm in dsv.Tables[0].Columns)
            {
                if (Clm.DataType == Type.GetType("System.DataTime"))
                {
                    Clm.DateTimeMode = DataSetDateTime.Utc;
                }
            }
            //==============================================Обработка таблицы===========================================
            //==========================================================================================================
            result = MessageBox.Show(this.Owner, "Импортировать униформу в АВИАБИТ", "Импорт", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                toolStripProgressBar1.Maximum = ds.Tables[0].Rows.Count + 1;
                foreach (DataRow Drow in ds.Tables[0].Rows)
                {
                    toolStripProgressBar1.Value = i++;
                    this.UseWaitCursor = true;
                    //======================================
                    Dt.Rows.Clear();
                    Dt.ImportRow(Drow);
                    //Dt.WriteXml
                    XMLStr = "";
                    XmlWriter XMLWR = XmlWriter.Create(ST = new StringBuilder(), new XmlWriterSettings
                    {
                        ConformanceLevel = ConformanceLevel.Auto,
                    });
                    Dt.WriteXml(XMLWR);
                    XMLStr = ST.ToString();
                    int Nbeg = XMLStr.IndexOf("<Table>");
                    int NEnd = XMLStr.IndexOf("</DocumentElement>");
                    if (Nbeg > 0)
                    {
                        DataXMLStr = XMLStr.Substring(Nbeg, NEnd - Nbeg);
                    }
                    //File.WriteAllText("rrrrr.xml", DataXMLStr);
                    Command.Parameters["@STRXML"].Value = DataXMLStr;
                    Command.ExecuteNonQuery();
                    //====================================
                    this.UseWaitCursor = false;
                }
            }
            //==============================================Завершение таблицы===========================================
            //==========================================================================================================
            Command.Dispose();
            Conect.Close();
            Conect.Dispose();
            //==============================================
            Dt.Clear();
            Dt.Dispose();
            //==============================================Начало процесса===========================================
            //==========================================================================================================
        }


        private void toolStripButton2_Click(object sender, EventArgs e)
        {
            System.IO.StringWriter writer = new System.IO.StringWriter();
            ds.Tables[0].WriteXmlSchema(writer, true);
            File.WriteAllText("rrrrr.xml", writer.ToString());
            MessageBox.Show(writer.ToString(), "----", MessageBoxButtons.OK);
        }

    }
}