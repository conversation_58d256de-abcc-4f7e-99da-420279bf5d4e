﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="menuStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="toolStripMenuItem1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAAAAAP8A/5625Zeu3Zeu3Jiv3YOYxcvf9pSp0ZKo1vz9/2+Crsnd83eLtoyh
        z/f7/1tsloWayPL4/sLM43+UwO72/uZDKXmNuenz/vT5/nOGsuTw/W1/q9/u/dgzH+NAJ9EqGmd4o9rr
        /cUbEcEXD2FynNXp/Or0/bEEA8kgFFprlbQHBVdnkauzyG82LG8vlG9eLB01GG/OnG/lwG/tlG/wCG/y
        RHBvcHBIWHCGUHBjUHDxXHEFUHENJHEVzHEc6HB3bHB6ZHGaBHGCRHIX7HIvEHIxHHI0bHI4BHKoMHKP
        HHLEGHGKNHM3EHNOgHNWVHNe/HNmGHKyRHK4rHPjcHQGxHRhOHRxNHR26HR9nHSBUHTv1HT3OHUQPHPS
        GHWCIHWZfHWhUHWp+HWvdHYvhHX7jHZGUHYVEHapdHcgsHcohHcxLHc4SHY0kHY8KHdlJHeIIHfi9Hfy
        1Hf4iHf/PHgC8HgwmHdWyHiUlHh8rHkEPHkbmHkjbHksFHkY0HmhFHmmRHnEYHmS5HosbHo8CHpCbHpK
        VHpRcHrpfHrTPHsEEHrmmHtXYHtuhHtwkHt0HHt3wHwvSHv/uHxZTHr2hHyDDHyaaHyiPHyq5HyvyH0t
        1Hz7rH1EtH0PWH2oHH23mH29TH3EDH3J8H4oHH4wDH5aYH42uH7JBH7gYH7oNH7dmH7wDM9R9M9iyM+E
        LM9WYM/vKM/+***************************+INET0NEq9NEoLNEx1NE0XNGpwNGhZNHEXNCG0NIz
        8NJK2NJS8NJbmNJitNG9bNGyiNLZWNMAlNNcXNNwBNNznNN4PNN74NPtSNPXRNQUgNP0UNR7WNSSyNSa
        nNSjRNSqYNQECNQKRNUlFNVJ1NWjZNWzLNW44NW/lNXDSNYvINYfXNZbLNUcKNbClNbZ8NbhxNbqbNbv
        UNdn3HOT6NeEMNdUnNfrDNf6wNgBLNgJFNgQMNhrqNdyPNiQdNd4/NkODNklMNktBNkwCNkyRNmnWHTf
        nCH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIhgABCBxIsKDAAQgTDihQwCAAAwsi
        SjzQIIHBBBIlMljQwEHBBws0iBQZoWSEghJCjhy5gELBCgtQyJyJYsGFghhi0hRp8+bADQs4CBV6oajR
        gR0WjFi69IPRCyBCCBShlOkIElhJXCgx0MSCE2DBylyQQgXBCBnTpijYoq3bty4cyp1LV2BAADs=
</value>
  </data>
  <data name="toolStripMenuItem2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhEAAQAIcAAAEAAP////8A/5625Zeu3Zeu3Jiv3YOYxcvf9pOo0JKo1vz9/2+Crsnd83eLtoyh
        z/f7/1tsloWayPL4/sLM43+UwO72/gOGAHmNuenz/vT5/giKAwOHAHOGsuTw/ev0/fH3/gmLAyGhEAKD
        AAJ+AAJ4AAJzAG1/q9/u/e/2/ialEzKsGjmwHT+1IUa7JQJvAGd4o9rr/ePw/ez1/hKJCEO6IwJ7AAJ2
        AAJwAAJtAGFynNXp/Or0/SSbEgJ3AFprlQJ6AFdnkYGNrKuzyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAFUtoPn7KAAAAAAAAAAAAAAAAPt+MAAAAAAAAQAAAAAABP//AQAB/wAAAAAAAAAA
        APKD3AAAMwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPvLFPvLFAABGENmBPt8cPSa4AAAAAAACAAAYAAA
        APt+CPt+CAAA8AAAAAAAAAAAAAAAAENzIPt8cPzGAPzGAAAAzAAAAPt+mPt+wPt+4AAAAAAAAMwAIAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPt8cAAAAAAAAPScJPScJAAAZEHOHPt+
        MPSa4Pt+aAAACAAAYAAAKAAAI0GhwAAAAEHOPPt+MPEU/Pt+aAAASAAAH0GiwAAAAEHOXPt+MPI+4AAF
        uAAAI0MbTAAAAEOBYPybBAAAAAAAAAAF2AAAF0D+FAAAAAAAAAAAAPt/LPt/LAAKDENmBPrPBPSa4AAA
        AAAACAAAYAAAAPt/VPt/VAAGBAAAAAAAAAAAAAAAAENzIPrPBPt/ePt/eAAF4EHOHPrQePSa4PrQsAAA
        CAAAYAAAKAAAI0GhwAAAAEHOPPrQePEU/PrQsAAASAAAH0GiwAAAAEHOXPrQePI+4PrQsPt/3Pt/3AAF
        fFB0YnRuaXJldgBmZgAAAPt//Pt//AAFXAAAAAAAAAAAAAAAAAAAAAAAAAEACAAAAEN1zPuAAAAAAQAA
        3SH/C05FVFNDQVBFMi4wAwEBAAAh+QQBAAAAACwAAAAAEAAQAAAIkAABCBxIsKDAAQgTDihQwCAAAwsi
        SjzQQIFBBRIlMljQwEHBBws0iBQZoWSEghJCjhy5gELBCgs+yJypYcGFghhizpSpIcRNgh0WeBjq4QOI
        ECou2LBhQuCJBTGixvhwQcUKFi1c5BAIA6rUojRcMDWxFYCOBTvSpp3aoynBCBnj2igYpK5du0KGONzL
        t6/AgAA7
</value>
  </data>
  <data name="undoToolStripMenuItem.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGmSURBVDhPvZHdS5MBFMb3p3TX6E7c1BTHUIc2xUbEqGUz
        LaV5s6ViRvhuul0M9lFshFjmjV/bKLVajRAid6N3KYg3FkHDiWvNmXt3++udDiMd4nthD5ybc57nx+Ec
        xbkqGM0TfCcysSgyuyQSlioSFymOT5d/QaRQnjmR4VAO+1SOlx8PASUhnkiKmZXDQSH05E2erkCGBtsK
        NV0xKu9EaRz8xlisBKDQGJ7aYeKziD0k8vRtHrN3G51tGc39RdS3X9Fgfc/l9lmM7gzTkq8Y/au+sW16
        nu/zYDzHTe8e1XejVHUsSOHwgdkwFKPsxiS6h5s4pnMnAZ3+JM0jaW759hkYT6NsDqBqCx0ZL2gFND1x
        dANfcYRLAK45t6jo+86VkdTBJh3eHwiTv/4FWOLohSTOUoC6wQTa3g2qLcuo2j9QZnqNsuXZkbHCPE+t
        ZQmD6ye20cRJwHEdv/RFfUBaf5Pu4C4+6b3F9tlVde8T191ZjMIXeWGzfxetdZWrriytj9a5ZHghD9D0
        OCGFf9PUv0a5KSJ/dZP01vr+DdRtc/LD/0EKxR+XdBDBXAMPDQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="redoToolStripMenuItem.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGVSURBVDhPzZHdK0NhHMf3p7jBknbBNpqbc+FtttrUrBim
        LEJeRo0lzMsK2S6M5D3vJjXFJinhZu64WolQu9FaXjrnuf56nrOl5kTIhW99L56e7/dzfuf3yP5MO+cE
        /jOCLeqlY4KJgADnioDk9ddiZebFI4KBdQGD2wLcOwJ6VimEOhn7XKw8e0hQ4nxAXt0BNLYQuNYwbD4e
        9gUB9sUEZPOCgOuMSIEbpwTm0SeoLcsobAtAVbMrWm3dRz2FNM7yaKGQJZoraAqL0yarCbk2BBR13yKz
        bALGvkPxUlXjR65lC9nGeVSMvYqTNM/xUFqD0glcfga4Q4bWizSu/z2grN6GXDcJx0IcFg8P3VAcioo1
        KWCYAnQDjzTsSwFMBQmY2Ze1QzGou+4h109LAR0zUZS748ip3E0BsIVp6D8rrSEoTOtiOb14XArw7BE0
        +J5R5Lihe5hMCUgW9pnM/Zcwjb4g33byvcJHZdFtG3ojMIy8gGu/Qq33+ecgBtE7r2F08yjtjf5uEiad
        K4Yq+mzJ47+WTPYGxTcHtykTmrAAAAAASUVORK5CYII=
</value>
  </data>
  <data name="cutToolStripMenuItem.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGhSURBVDhPYyAG/AcCKJMwWLlp///ZyzbBNYA033nw9H91
        6xTiDJk8d83/nmmL/1e1zgRr+Pb9x//Vm/b8T8mpIt4Vk2av/D95zmqQ5f+fvXjzPy2/5r+YEB8J3ti4
        73/X5IX/T5y5+P/gsXP/w+KzSTNg4aod/7unLPrfO23h//LGvv8+QdHEawaBzokL/5c09P1Pzqv7Hxqb
        +d/NO4g0A0AgIa/lf1hC4f+QtLb//gktxBsQ3fHuf0Tn6/8hzQ/+e5ed+u9Tfva/X9Xl/z6lp/+7Ze/E
        b1BE+6v/kT3v/gc33PlvHT3vv2lQ33+3vH3//auv/ncFaraKmY/fgLD25//D2p7+d0zbANZsGtT737vk
        1H//yqv/nTI2/TcJ7sdvQGjLo/9Bjff+O6Vu+m8c0A02IABou2/F2f8WEdP+aztk4TcguOnef/+yS//t
        ktf8N/SBBJx3yfH/dkmr/mvaJP+XkdfEb4Bf5cX/9kmrgbZ3/Vc3CwcnHvuUNf+1rBP+q5tH/Df2b8dv
        gHPGlv9mIZP+a1jF/jf0qsGvmDqAgQEAkYXpGFtqYpEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="copyToolStripMenuItem.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHgSURBVDhPvZHdS1pxGMfPn7IKieq63Y/RKw0aFF1ssEEv
        dOVFXXUVFQ3Wlg4H2dSo7A21jnUiKiPFylwttMy9GXMNNgUrzinLrGN98zxaVgu7GOwLn5vf+X0/D8/v
        MP8cbuEbJFiblzDMuDDAfcQbLYfklfSRyrejn3CirnUID0tr7peYrd5kLZHY2Tm6hudgcXjxrEF5v8Qw
        66aixQew64DHL+C9fhrskgDTAg+jnUffLA/V+B7xojNwUzjAOUkg5eQ0BuEgikAoTILbaR/ZQX6tg0jW
        GUZnstPHXscpFDMCWtg/aOz3Y3h+l86vp0ETArciIK9qgiCBOr6vtPdRRMQOH8F2YB9f/LvQTQWp1MHG
        CM/WPl4qgsitmkROxSiyywcTAmnf46h4NVmu86Fe/RnvjNskuIxwKGLr9zEcmwfQsGuQPelJCYRwFF9/
        huD+HoDT8wu2Tz/Q2pv4vc0jYcjVQTx/68PTtg1Cw65CVtqdEuSXVP9Fo2qDBGJ8vRAvYtN/BKtLgHlx
        Dx9GncgqUqUe8q7UtK+Q4PrkS7pNy8gsUKQXVDbZETk5g27cBa15DZqxVZosldWGJWQ8fp1eUCS3xF/b
        SGSX6yEr0yKruAuZhcp4uQMPHr1KL/hPYZgL78LfVeQiZg4AAAAASUVORK5CYII=
</value>
  </data>
  <data name="pasteToolStripMenuItem.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJXSURBVDhPtZJdSFNhHMYPdGE3RR/UVVcF0U0UEVQXGYWt
        sNlikdii5SK2SckmS9tGas0cunRzadoW+THUprMSpUnREBqOvmdsRh8SOSPXsnY2N+eyeDrn3UaujCDo
        gYcD73mf3/+Dl/ovcvZr4bihQW+LCrL8fUgez6+edgPmuqX2CIL+DkT8V+EbqUGJmANDpQKXdWoYK4uJ
        k9GE2FCMtiEe6kYs2AlLkwLRyTaUSHh4MaTE2VNcKEWb4Xcew6R9NWTC/b8D2HA8ZEWj9ijcrhrQ7+sx
        MaLGW9dx9Jl5kAgy0VG6Ck8NFAoO/zJSd1ttAkBfh65UiHJFHtSFuThTwIdCzINcxIVUkIU62RoCyMvO
        TAd0NuswQ3dhJmBCdLwKgeeF8LYtJ/a0LoO7aREeGTNImHXWto3pAItJi+lAC6bHKhB9eRKRZwdB9y+G
        vtWOKlMvzl3qglJngVxjhkRlhLCoOh3Q3KDBlM+IyIgYU4+5CN/fhI+3VpBwSvHZbwiGYhibCCJXeh45
        +eqfELO+DPSrCoQfchAeXI/QwEr4WilSmVXzTSexyeqAZ9RPwneHvNjDFyUgDdUqfB4+jZBjHejbSxGw
        ZWD0GkXa/jr7HV/CTOUPQXje+OEafodsgRwcJrxr7wFkbt8Bqu5CMQIPpEx4CT71LMR4+wJ4GynImJnZ
        tlMdXGE6qLcMoG/QS76st25YC+pieRF5IKktpyxmFja38j3XaxK23XHDan+SDtCXnWBeGA8i/m7k7NxC
        frDbZmdOVZvPBPAnHWK2zV74m5PX/1UU9QOTNfjZ1V1+MwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="indexToolStripMenuItem.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAKWSURBVDhPjZJbSNNRHMf3HPRcYu+9Gb32VhAJ9RASlOBb
        b2aIFop5GXPeyrmp8zLNec01HbmpqJvabjbndHl3JTmGbk7zroUXKD79N5XMFPzCjwMHPp9zzu/8ROdF
        3eChpGSc7OxBsrIcHG1fLCE4VBMT69TVz6J5P4NUekHRMexyrVOumsBqW+OjOUC/eR5tiwdJtvN80Wm4
        q3sZh+MnA9YdbNYt+vvW0esXaWqeFm409K/oNNzRGcRi3qKnZ5X2jmXaBLBVN09T4xwqlQe5fIycvKFD
        wWlYbwiEQYMhSIt2gcZGLzU1s5SXT6NQjJKfP8KLZDMJz42caNhmGNa2zKPTLaB556O+bo6W9gCy1k1i
        S/eJke0gqQkgL58k/pkRffvgoUDbOoVc4aauwcvb6q9UlnlQlkyh1gVJ0kKVFT4vCOWHevsvHuf5aTN7
        mPP7EIklNrS6SSRSO7LiYWRFo7wpdFPb7CVRA7phWFzaJqPAxL0n6vBqcO1yJ9FNodKF6GWKEd9yAJNl
        jLT0PsRiW/jPc5tWUFkI5xgeGfOH19J6J1LNEnHxJkQZWRqKlb0sbQeZXxXeV2JBmmvnYcEGhrFDQSih
        W4QEtx5UUtE8StPAARHXKxC9LhoSTu4XyojZMcXGwXeszi9EiwPoTwh2fuyH4ZQcE3MrQi8se1yJzP87
        B5IcO+mZfRSX2fAGF1B1LyLv3D3CwSmcLq9x4RXgKaGZyaUeomPU/09keqaZV4Koy/KNu8njdLkP2PsN
        xk9+om4XM7MIFV1rXL6pJE9pOXukM8UWUtN6aWib4UasCXGtD61jj2brLkmKGS5FKUhI/cDAaOBswXFU
        1W6KlE7inrYTcS2Xq5EZ3H9UhUxlY2gyKMAi0R+3BHAuvd7mVQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="searchToolStripMenuItem.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALGSURBVDhPldL/T01xHMfx/hczs8VmmC/zg1i7oa6pOXVL
        Jbrqlm5fbrluSqV7uynpdq/qtoh7C9Ei3eu0fBnKl6yFmSRkWF93VUZ2Q/Z0P2OuxmZe2/ntvB/nfD7v
        V8Dvya2UEY+hwk2+VcZY28n+Cheph86jKWzm52t/T15VBwdtMs6ubq6+7EMe7sA5VESGnIRkOklirhN1
        XuPfkQMWGZNDprX/Mg8+PGFgdpScthIsL/JJuaRkg7mcGFMP0YYGEvQn5iN68bs2N3W9VjrGHdyc6qVv
        pp8Br4eWERdJdjMK+1PW2NpYW6NGyqxkR1atHxFAsVPG/rCcmiE9jrc2nMNmah87ST3TgqZukpiK1yw9
        G8Hi06GsN6YTlVblB7LL2qlzv8EkW9h7fyuZvWHoHAZymnzHOn4Rk7UBQ3kl8dYUAqsVBJaFE6Ep9wMZ
        5jbqr8xSWF+P8qyCLXI06lor1Y0urnXdZszzjrfjHpoutBOXp2VBSQjKRJMf0Bpbsbq9HGjyEG9QE1q/
        G11TMZ03uhD5Ogcfvd8YnZrD1nCeddoINscX+IG9h1rIrHmF9vhnNNYhwnO1vnUeYWTC82t4/P1XBke+
        4Op+RkRcLMHRBj+QUnSO1NJbJB77RIJlimjjHfaXmhiemPwxPD3nG/5Mz6CXutYetkmRBEk6PyAaJkqy
        q+QBqsPjKIp6kYw62jtvMDbt+/LoF+4993K5b4Y0fQFh0k6K87P9gIhoWJzeSdTBOyzNNrNMF0uyLosT
        zW7augewX+ghbV8BkartNFtiabenU2XKmY+IhqnSq9mUYSVYnYpSY0ZKLkTasYtwSUVM5DZsuo2MnVLw
        7W4CjqNZfyKiYaIkYs9iVeK2xYWJM8cog0kOW8GjgpUM24OYdEWyL2nrfOBfEUBS6HKu61ezJ2QJqtBV
        /weICGTdskWErF3oGw4I+A4LpgTtu1iCBgAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="toolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>125, 17</value>
  </metadata>
  <metadata name="statusStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>223, 17</value>
  </metadata>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>332, 17</value>
  </metadata>
  <metadata name="openFileDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>422, 17</value>
  </metadata>
  <metadata name="notifyIcon1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>562, 17</value>
  </metadata>
  <data name="notifyIcon1.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAgBAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAGAAAADwAA
        ABYAAAAaAAAAGwAAABsAAAAZAAAAFQAAAA4AAAAGAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAIAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAADIxN
        NF2jWz25q2BB5bBkRf+yZkj/rWRH3J5ZPaZ4Qy1bAAAAIQAAABgAAAANAAAABAAAAAAAAAAAAAAAAAAA
        AAAAAAACAAAADQAAAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4JK
        MzeoXj/PrmFC/7JmSP+2bE7/uG5R/7pxU/+8c1b/vnZZ/7tyVP+sZUfVcj4sVwAAACAAAAAUAAAABwAA
        AAAAAAAAAAAAA5BONT6RUTZyAAAAEgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAOaVDtbrGBB965iQ/+zaEr/tWtM/7dtT/+5b1L/u3JU/710V/+/d1r/wXld/8J8X/+8c1b/nlk8nRoN
        DSgAAAAZAAAACQAAAAWUUjlRrWBC9JRSOIwAAAASAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAABnVg6YK1gQfyuYkP/smdI/7RpS/+2bE7/uG5R/7pxU/+8c1b/vnZZ/794W//Be17/w31h/8WA
        Y//DfGD/q2NE0TwjGTMAAAAblFM4ab12WPy2bE7/lFI4jAAAABIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAJ1VOjmsYEH3rmFC/7JnSv+4c1f/vnxh/8GAZv/BgGb/wX5j/753Wv+/d1r/wXld/8J8
        X//EfmL/xoFl/8iDaP/Hgmb/r2RG3pVTOJG9dVj/0pB1/7ZsTv+QUTd+AAAAEQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAACIVTMPq19B4a5iRP++f2X/woZu/7t4Xf+1blL/s2pN/7NqTv+6dlr/xYhv/8eI
        bv/BfF//w31h/8WAY//Hgmb/yYVp/8uHa//JhWn/v3da/9GPdP/TkXb/tmxO/5BRN34AAAARAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAKhcPpitYEH/tW9T/7RsT/+tYEH/rV9A7qldPperXkBnrWFAV6xg
        QnitYEHJuXRZ/8ySev/FgGT/xoFl/8iDaP/Khmr/zIht/86Lb//QjXL/0pB1/9SSeP+3bE//kFE3fgAA
        ABEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACfWz4trWBB/K1gQf+tYEH/rWBB/KVcPY6IVTMPAAAAAAAA
        AAAAAAAAAAAAAAAAAACtYUBUtGtO786Wf//IhGj/yYVp/8uHa//Nim7/z4xx/9GPdP/TkXb/1JN5/7dt
        T/+KTTRxAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKleP7WtYEH/rWBB/6xfQO+YVTlIAAAAAgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACqXT4htWxP5tGag//Kh2v/zIht/86Lb//QjXL/0pB1/9SS
        eP/WlXr/t21P/4pNNHEAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAACfWz4trWBB/61gQf+sX0HsiU42NAAA
        AAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACdXDsnt29U88uIbP/Nim7/z4xx/9GP
        dP/TkXb/1JN5/9eWfP+3bU//ik00cQAAAA8AAAAAAAAAAAAAAAAAAAAAAAAAAKpfQJ+tYEH/rF9A8otM
        MTkAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABJ9YPYK3bU//y4ds/86L
        b//QjXL/0pB1/9SSeP/WlXr/15d9/7duUP+DSDFjAAAADwAAAAAAAAAAAAAAAAAAAAAAAAAArWBB361g
        QfyUUjlRAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASfWD2CuG9R/8uG
        a//Nim7/z4xx/9GPdP/TkXb/1JN5/9eWfP/YmX7/uG5Q/4NIMWMAAAAOAAAAAAAAAAAAAAAAAAAAAAAA
        AACtYEH2pl0/eQAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAp11AfL9/
        Zf/dtKT/3rWk/961pf/ftqX/4Lam/+C3pv/gt6f/4bio/+K5qP+8eFz/klA2WQAAAAkAAAAAAAAAAAAA
        AAAAAAAAAAAAAKhbQDgAAAAJAAAADwAAAA8AAAAPAAAADwAAAA8AAAAPAAAADwAAAA8AAAAPAAAAD5dT
        OFaqX0DPrGBBzaxgQc2sYEHNrGBBzaxgQc2sYEHNrGBBzaxgQc2sYEHNrGBBzaxgQc2iWDs0AAAAAwAA
        AAAAAAAAAAAAAAAAAAAAAAAAnls9KoNHMURyPitOcj4rTnI+K05yPitOcj4rTnI+K05yPitOcj4rTnI+
        K05yPitOd0EsSy4XFxYAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAA
        AA8AAAALAAAAAAAAAAAAAAAAAAAAAAAAAACsX0HErWBB/61gQf+tYEH/rWBB/61gQf+tYEH/rWBB/61g
        Qf+tYEH/rWBB/61gQf+jWjyQAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAKfWTxyjU80awAAABIAAAAAAAAAAAAAAAAAAAAAAAAAAKxfQcTYoYv/2p6F/9aWfP/Uk3n/05F2/9GO
        c//PjHH/zYlu/8uHa//AeVz/pVo8kAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAABnlY8Wa1gQf+VUziEAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAArF9BxNmjjv/anoX/1ZR6/9OS
        d//SkHX/0I1y/86Lb//MiG3/wHlc/6BaPJQAAAAGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAZJQNDasX0DyrWBB/4lMMlsAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAACsX0HE2aWR/9md
        hf/Uk3n/05F2/9GOc//PjHH/zYlu/8F6Xv+dVzukAAAAHAAAAAoAAAABAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAKCRy8rq19A561gQf+rX0DqKxUVGAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAKxf
        QcTap5P/2JyE/9OSd//SkHX/0I1y/86Lb//MiG3/w3xg/6tfQuFNKx48AAAAGwAAAAwAAAACAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAFekcuMqtfQOetYEH/rWBB/5lVO4cAAAAJAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAArF9BxNuqlv/YnIT/05F2/9GOc//PjHH/zYlu/8uHa//JhWn/wHhb/6pgQuRfNyRGAAAAHgAA
        ABMAAAALAAAABgAAAAQAAAAHAAAADpBPNl6sX0DvrWBB/61gQf+rX0HtSS4bHAAAAAIAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAACsX0HE26yZ/9ecg//SkHX/0I1y/86Lb//MiG3/yoZq/8iDZ//GgWX/wXte/7Fm
        SfaUUjePNB8VMQAAAB4AAAAZAAAAGHpDLEWkWz24rWBB/61gQf+uYUL/rWBB/5dVOXgAAAAHAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAKxfQcTcrpz/15yD/9GOc//PjHH/zYlu/8uHa//JhWn/x4Jm/8V/
        Y//DfWD/wXte/7xzVv+0aUv/q2JF16lhQ9atYkPmr2NF/7BkRv+4c1f/undc/69jRP+oXj/SIhERDwAA
        AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAArF9BxN2xn//XnIT/xH5i/7lwVPbbq5j/y4hs/8iD
        Z//GgWX/xH5i/8J8X//AeVz/vnda/710V/+7clT/uW9S/7hvUv+5c1f/unVa/7NpS/+vY0T/rGBA+oZJ
        MDsAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACsX0HE3rOi/8yOdf+kWz6RmVk5KMGB
        aOvZqpf/yIRo/8V/Y//DfWD/wXte/794W/++dln/vHNW/7pxU/+4blD/tmxO/7RpS/+yZkj/sGRF/61h
        Qv+aVTlvAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKxfQcTMlH7/pF5BkgAA
        AAUAAAAAqmNAJLx5X+HXqJX/x4Rp/8J8X//AeVz/vnda/710V/+7clT/uW9S/7dtT/+1akz/s2hK/7Fl
        R/+uYUL/o1o9jQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAArF9BxKle
        PosAAAAFAAAAAAAAAAAAAAAAqmNHErRsT7vOm4b/y5B4/794W/++dln/vHNW/7pxU/+4blD/tmxO/7Rp
        S/+xZUb/rWFC/6NaPYIAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACtXkJRAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAK1fQGO6d1voxox0/8OEa/+8dln/uW9R/7Vq
        TP+yZkf/rmJD/6xfQd2iWTxNAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKpVVQatYEFarWFArq5g
        QdWtYEH/rV9B2atgQLCpXkBckklJBwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA/+AB+f/AAPH/gABh/wAAAf4AAAH+AAAB/AAAAfwAAAH4D4AB+B/AAfA/
        4AHwf8AB8P+AAfH/gAHwAAAB8AAf8fAAP+HwAH/B8AD/gfAAfwHwAD4D8AAAA/AAAAfwAAAH8AAAD/AA
        AB/wgAA/8cAAf/fwAP//+AP///////////8=
</value>
  </data>
  <data name="toolStripButton1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFzSURBVDhPY6AKmLft6X9cePq6u0B8ZyNQGStENRYwY/3d
        /7hA84yj/0/f//Z/8soLB4FK2SE60MCkFdfAiv/8xcT5TevAcifvfv5f1rX1MFA5piE9C8+DFX3/jYmj
        c6ahYKByMSBmA2uEgdZZJ8AGfPrxDycGgUNnHoMMkAdiHrBGGKifcgCs4N23fzgxCDxz1gQZoADEvGCN
        MFDZuxOs4OXnfxj4y08IBgGQAVBDBIGYCawZBIrbNoEVPPnwFwPDNKFjoDaQNyCG5NavBhtw/91fDHzD
        WB5D82MH9f8XtaRAhnCDDUivWAw24Nbrvxj4qLQgCgYZcFCcH6RZEYiFwQYkFs0BG3D15R8M/OrLXzAG
        gY0szP9PKoiANKsAsTQQQwIzKnsqWAEhsIWdFaRZFYhlgZgPiJmBmIEhPH3if2Lw6s0nQAYoAzHI6RDN
        UABKnqAUJkcAgxIRyOmQwEMCINM4gBgkgQ+Doo4LiFmAGAgYGACMfO0Fp0xG4gAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolStripButton2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGgSURBVDhPrZLdK0NxGMdPZ2HeonlJ3of5A9y5cqH8C4or
        Vy5Yk9UMZbGQsQvvC6klpWjemsKNJjVRu+JClotxMTTvQ3bO4/k9p3NxHGsufOtzdb7P5/dyfty/ZHHn
        BuIx6wkil5tYS5Lav8S1HoR4sbuO4OQqCpMrAR9WU6SJH5lYOadyTFBjGvDQt+PgC1gc3kOsqyVj7gCV
        3r/UNLfPKMB6PpJMg3IG5/wkeP4Q48LiOw0xQRmSQYNybFMHVIhExV+RBZohjgnKkUwalNPt3KVC+EVU
        wQSvnyJoHRrQDJOgAsmiQTnmoS0SXD8KCu5eBXh6FyF1VAN6dzbwdg74PsSKmEkmXajRtkqCq4ig4AFX
        Z9uuXs6BmrVCqN0ogTqvHhr2DcCbSJBHglbrEgku7gQFIdzF/ZsIuhEt1O9VAt+LK1uQDsRIgmIStHQu
        kOAsHFPBdnKLR6ma1wHfRUMGRI+UItIOmtqmSZAofA8JqhD2FtIR6Q4aW8chEf3ONVjd9jOB+i9gmCkX
        KUoAO3MBkoYowiPseWr/AFuM9TEc9w0ZCOCKxRnEgwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolStripButton4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFqSURBVDhPpZJdS8JgFMfPZd+gO+tbRd14U4R1YUEvRC/Q
        C0iBUUwCi16oiCKiIBCCwIugILyoCCqCDEQkJDWzuc1tp3PcI4/ZkIF/+O05+z3Pf4wx2IqlsRUgepZE
        1bAxWzIx+WngY0avbnhx3IXI8QvmVAtT+Qo+f+iYSGm08ea4QnPHXVjef8BM0cTXrIF3aQ1v3suoHD15
        ctyFxe0EToQv/hDeu/fkuAsL0WtsTGjzVkwybo67MKPExa3M3NqVmGTcHHeBX6UxUyuXYpJxc9yFkdC5
        uJUZW4qJScbNcReCsyf/Ps7w/Kknx10ITB6gTU+z6cJYxOD0IZq0mhZihTEdZ5DUa1Rs5C70ju44hwiD
        DupE3/guanSAKQvY/dCfWEW3sURwF/zBdVTpAP+mtU3/0AZ+01rUHL7KjivQWlBtzBM5grvQHVCcL1KX
        noGImGTcHHehq38VWwEobUQ74aujg+gUaxMHvl9eYi/f372VKgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolStripButton5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHISURBVDhPlZPNSxtRFMWDiuhOs9U/yY0rNxUXUrpodVGM
        mpagG9FNkBQ/atEiitjSCortoiBdFEVshSARWuyiFUtFLGrHZGaSmXnHe++bFzNBBQ/8yLvn3XOYMEms
        Um3tXaiG7HqiRhZuUvZFfCo7EQfDgWRqtAzP5u7beHyG1mt1qkJ8CfUXQF4Cuf3vZXg24j1abybqJGik
        C34BxecCh5bfruqw+wzKSULZg6aghWiQoJEUBAeyjHBZFZgBqHw/1GUCgdVnClqJRgkaSYGfw+uPfyLM
        Vc23eVKgSruYWvmJMzvAj5Mitn87mHx/EJmNZ7kBji487B8XJaML3E2ML+XkYvfIleX04l5kNt6x5Uvp
        zqEjGSkIChsYm/uKZPpTmZFX25H5Jo8zusBax/Dkl/CFaaUyn8PTtao9zkiBf/5OGiuVGPsQnugtKw17
        AX36gYYzUuD9W8TTkbXI4/UM6dK7PM7Espm4753O4nHqDTxqZUo+8HBgAa6nBIewCfYKJYVCUSFPcCa2
        lW7q5afoTszLks0LRGfvS1zSkuVq/jvau7AVzokzgjP8W+I/SNODJ9PhN9bqeJQJT9eq9jjDBaw6vrw/
        GVwB5Nu3C13ViBkAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>51</value>
  </metadata>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAgBAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAGAAAADwAA
        ABYAAAAaAAAAGwAAABsAAAAZAAAAFQAAAA4AAAAGAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAIAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAADIxN
        NF2jWz25q2BB5bBkRf+yZkj/rWRH3J5ZPaZ4Qy1bAAAAIQAAABgAAAANAAAABAAAAAAAAAAAAAAAAAAA
        AAAAAAACAAAADQAAAAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4JK
        MzeoXj/PrmFC/7JmSP+2bE7/uG5R/7pxU/+8c1b/vnZZ/7tyVP+sZUfVcj4sVwAAACAAAAAUAAAABwAA
        AAAAAAAAAAAAA5BONT6RUTZyAAAAEgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAOaVDtbrGBB965iQ/+zaEr/tWtM/7dtT/+5b1L/u3JU/710V/+/d1r/wXld/8J8X/+8c1b/nlk8nRoN
        DSgAAAAZAAAACQAAAAWUUjlRrWBC9JRSOIwAAAASAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAABnVg6YK1gQfyuYkP/smdI/7RpS/+2bE7/uG5R/7pxU/+8c1b/vnZZ/794W//Be17/w31h/8WA
        Y//DfGD/q2NE0TwjGTMAAAAblFM4ab12WPy2bE7/lFI4jAAAABIAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAJ1VOjmsYEH3rmFC/7JnSv+4c1f/vnxh/8GAZv/BgGb/wX5j/753Wv+/d1r/wXld/8J8
        X//EfmL/xoFl/8iDaP/Hgmb/r2RG3pVTOJG9dVj/0pB1/7ZsTv+QUTd+AAAAEQAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAACIVTMPq19B4a5iRP++f2X/woZu/7t4Xf+1blL/s2pN/7NqTv+6dlr/xYhv/8eI
        bv/BfF//w31h/8WAY//Hgmb/yYVp/8uHa//JhWn/v3da/9GPdP/TkXb/tmxO/5BRN34AAAARAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAKhcPpitYEH/tW9T/7RsT/+tYEH/rV9A7qldPperXkBnrWFAV6xg
        QnitYEHJuXRZ/8ySev/FgGT/xoFl/8iDaP/Khmr/zIht/86Lb//QjXL/0pB1/9SSeP+3bE//kFE3fgAA
        ABEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACfWz4trWBB/K1gQf+tYEH/rWBB/KVcPY6IVTMPAAAAAAAA
        AAAAAAAAAAAAAAAAAACtYUBUtGtO786Wf//IhGj/yYVp/8uHa//Nim7/z4xx/9GPdP/TkXb/1JN5/7dt
        T/+KTTRxAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKleP7WtYEH/rWBB/6xfQO+YVTlIAAAAAgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACqXT4htWxP5tGag//Kh2v/zIht/86Lb//QjXL/0pB1/9SS
        eP/WlXr/t21P/4pNNHEAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAACfWz4trWBB/61gQf+sX0HsiU42NAAA
        AAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACdXDsnt29U88uIbP/Nim7/z4xx/9GP
        dP/TkXb/1JN5/9eWfP+3bU//ik00cQAAAA8AAAAAAAAAAAAAAAAAAAAAAAAAAKpfQJ+tYEH/rF9A8otM
        MTkAAAACAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABJ9YPYK3bU//y4ds/86L
        b//QjXL/0pB1/9SSeP/WlXr/15d9/7duUP+DSDFjAAAADwAAAAAAAAAAAAAAAAAAAAAAAAAArWBB361g
        QfyUUjlRAAAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASfWD2CuG9R/8uG
        a//Nim7/z4xx/9GPdP/TkXb/1JN5/9eWfP/YmX7/uG5Q/4NIMWMAAAAOAAAAAAAAAAAAAAAAAAAAAAAA
        AACtYEH2pl0/eQAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAp11AfL9/
        Zf/dtKT/3rWk/961pf/ftqX/4Lam/+C3pv/gt6f/4bio/+K5qP+8eFz/klA2WQAAAAkAAAAAAAAAAAAA
        AAAAAAAAAAAAAKhbQDgAAAAJAAAADwAAAA8AAAAPAAAADwAAAA8AAAAPAAAADwAAAA8AAAAPAAAAD5dT
        OFaqX0DPrGBBzaxgQc2sYEHNrGBBzaxgQc2sYEHNrGBBzaxgQc2sYEHNrGBBzaxgQc2iWDs0AAAAAwAA
        AAAAAAAAAAAAAAAAAAAAAAAAnls9KoNHMURyPitOcj4rTnI+K05yPitOcj4rTnI+K05yPitOcj4rTnI+
        K05yPitOd0EsSy4XFxYAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAA
        AA8AAAALAAAAAAAAAAAAAAAAAAAAAAAAAACsX0HErWBB/61gQf+tYEH/rWBB/61gQf+tYEH/rWBB/61g
        Qf+tYEH/rWBB/61gQf+jWjyQAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAKfWTxyjU80awAAABIAAAAAAAAAAAAAAAAAAAAAAAAAAKxfQcTYoYv/2p6F/9aWfP/Uk3n/05F2/9GO
        c//PjHH/zYlu/8uHa//AeVz/pVo8kAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAABnlY8Wa1gQf+VUziEAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAArF9BxNmjjv/anoX/1ZR6/9OS
        d//SkHX/0I1y/86Lb//MiG3/wHlc/6BaPJQAAAAGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAZJQNDasX0DyrWBB/4lMMlsAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAACsX0HE2aWR/9md
        hf/Uk3n/05F2/9GOc//PjHH/zYlu/8F6Xv+dVzukAAAAHAAAAAoAAAABAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAKCRy8rq19A561gQf+rX0DqKxUVGAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAKxf
        QcTap5P/2JyE/9OSd//SkHX/0I1y/86Lb//MiG3/w3xg/6tfQuFNKx48AAAAGwAAAAwAAAACAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAFekcuMqtfQOetYEH/rWBB/5lVO4cAAAAJAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAArF9BxNuqlv/YnIT/05F2/9GOc//PjHH/zYlu/8uHa//JhWn/wHhb/6pgQuRfNyRGAAAAHgAA
        ABMAAAALAAAABgAAAAQAAAAHAAAADpBPNl6sX0DvrWBB/61gQf+rX0HtSS4bHAAAAAIAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAACsX0HE26yZ/9ecg//SkHX/0I1y/86Lb//MiG3/yoZq/8iDZ//GgWX/wXte/7Fm
        SfaUUjePNB8VMQAAAB4AAAAZAAAAGHpDLEWkWz24rWBB/61gQf+uYUL/rWBB/5dVOXgAAAAHAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAKxfQcTcrpz/15yD/9GOc//PjHH/zYlu/8uHa//JhWn/x4Jm/8V/
        Y//DfWD/wXte/7xzVv+0aUv/q2JF16lhQ9atYkPmr2NF/7BkRv+4c1f/undc/69jRP+oXj/SIhERDwAA
        AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAArF9BxN2xn//XnIT/xH5i/7lwVPbbq5j/y4hs/8iD
        Z//GgWX/xH5i/8J8X//AeVz/vnda/710V/+7clT/uW9S/7hvUv+5c1f/unVa/7NpS/+vY0T/rGBA+oZJ
        MDsAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACsX0HE3rOi/8yOdf+kWz6RmVk5KMGB
        aOvZqpf/yIRo/8V/Y//DfWD/wXte/794W/++dln/vHNW/7pxU/+4blD/tmxO/7RpS/+yZkj/sGRF/61h
        Qv+aVTlvAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKxfQcTMlH7/pF5BkgAA
        AAUAAAAAqmNAJLx5X+HXqJX/x4Rp/8J8X//AeVz/vnda/710V/+7clT/uW9S/7dtT/+1akz/s2hK/7Fl
        R/+uYUL/o1o9jQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAArF9BxKle
        PosAAAAFAAAAAAAAAAAAAAAAqmNHErRsT7vOm4b/y5B4/794W/++dln/vHNW/7pxU/+4blD/tmxO/7Rp
        S/+xZUb/rWFC/6NaPYIAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AACtXkJRAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAK1fQGO6d1voxox0/8OEa/+8dln/uW9R/7Vq
        TP+yZkf/rmJD/6xfQd2iWTxNAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAKpVVQatYEFarWFArq5g
        QdWtYEH/rV9B2atgQLCpXkBckklJBwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA/+AB+f/AAPH/gABh/wAAAf4AAAH+AAAB/AAAAfwAAAH4D4AB+B/AAfA/
        4AHwf8AB8P+AAfH/gAHwAAAB8AAf8fAAP+HwAH/B8AD/gfAAfwHwAD4D8AAAA/AAAAfwAAAH8AAAD/AA
        AB/wgAA/8cAAf/fwAP//+AP///////////8=
</value>
  </data>
</root>